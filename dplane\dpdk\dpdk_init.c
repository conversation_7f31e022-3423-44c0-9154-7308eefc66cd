#include "flow_common.h"
#include "dpdk_common.h"
#include "ngfw_env.h"
#include <dpdk_kni_fifo.h>
//#include <arch_cpu.h>

//#include <stdio.h>
#include <linux/netdevice.h>



#ifdef PLATFORM_LOONGSON
#define RTE_LOONGSON_RX_DESC_DEFAULT 128
#define RTE_LOONGSON_TX_DESC_DEFAULT 256
#endif
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

static struct rte_eth_conf gdev_conf = {
	.rxmode = {
		.mq_mode = RTE_ETH_MQ_RX_RSS,
		.offloads = 0,
	},
	.rx_adv_conf = {
		.rss_conf = {
			.rss_key = NULL,
			.rss_hf = RTE_ETH_RSS_IP,
		},
	},
	.txmode = {
		.mq_mode = RTE_ETH_MQ_TX_NONE,
	},
	.intr_conf = {
		.lsc = 1, /* lsc interrupt feature enabled */
	}
};

struct dpdk_dplane_params dpdk_conf;

#ifdef PLATFORM_LOONGSON
FLOW_SHARED int loongson_check_error_on = 0;
#endif

extern void dpdk_kni_memzone_init(void);
extern int dpdk_status_dp2ifmd_single(uint8_t port_id, struct rte_eth_link *link);

static int dpdk_port_event_callback(uint16_t port_id, enum rte_eth_event_type type, void *cb_arg,
				    void *ret_param)
{
	struct rte_eth_link link;

	//RTE_SET_USED(param);

	rte_eth_link_get_nowait(port_id, &link);
	dpdk_status_dp2ifmd_single(port_id, &link);

	log_info("Port %u Link %s\n", port_id, link.link_status ? "Up" : "Down");

	return 0;
}

static void dpdk_soft_queue_init(struct dpdk_soft_queue *tmp_queue, dpdk_squeue_type_t sq_type, int pindex,
				 int cindex)
{
	struct dpdk_port_params *pparams = NULL;

	if (sq_type == DPDK_SOFT_LQ) {
		sprintf(tmp_queue->sq_name, "soft_lq_c%dp%d", cindex, pindex);
		tmp_queue->queue_size = DPDK_RING_SIZE;
		tmp_queue->queue_ring = rte_ring_create(tmp_queue->sq_name, tmp_queue->queue_size,
							SOCKET_ID_ANY, RING_F_SC_DEQ);
	} else if (sq_type == DPDK_SOFT_RQ) {
		sprintf(tmp_queue->sq_name, "soft_rq_c%d", cindex);
		tmp_queue->queue_size = DPDK_MAX_PKT_BURST * RTE_MAX_ETHPORTS;
		tmp_queue->queue_ring = rte_ring_create(tmp_queue->sq_name, tmp_queue->queue_size,
							SOCKET_ID_ANY, RING_F_SC_DEQ);
	} else if (sq_type == DPDK_SOFT_TQ) {
		pparams = dpdk_conf.port_params + pindex;
		sprintf(tmp_queue->sq_name, "soft_tq_c%dp%d", cindex, pindex);
		if (pparams->hw_rx_queues == 1)
			tmp_queue->queue_size = DPDK_RING_SIZE * dpdk_conf.numa_cores;
		else
			tmp_queue->queue_size = DPDK_RING_SIZE;
		tmp_queue->queue_ring = rte_ring_create(tmp_queue->sq_name, tmp_queue->queue_size,
							SOCKET_ID_ANY, RING_F_SC_DEQ);
	} else if (sq_type == DPDK_DISPATCH_TQ) {
		sprintf(tmp_queue->sq_name, "dispatch_tq_c%dp%d", cindex, pindex);
		tmp_queue->queue_size = 32 << 10;	//DPDK_MAX_PKT_BURST *RTE_MAX_ETHPORTS;
		tmp_queue->queue_ring = rte_ring_create(tmp_queue->sq_name, tmp_queue->queue_size,
							SOCKET_ID_ANY, RING_F_SC_DEQ | RING_F_SP_ENQ);
	} else if (sq_type == DPDK_DISPATCH_RQ) {
		sprintf(tmp_queue->sq_name, "dispatch_rq_c%d", cindex);
		tmp_queue->queue_size = DPDK_RING_SIZE;
		tmp_queue->queue_ring = rte_ring_create(tmp_queue->sq_name, tmp_queue->queue_size,
							SOCKET_ID_ANY, RING_F_SC_DEQ | RING_F_SP_ENQ);
	}
	//printf("%s:%d port %d create queue:%s on core %d.\n", __FUNCTION__,__LINE__,  pindex, tmp_queue->sq_name, cindex);

	if (tmp_queue->queue_ring == NULL)
		printf("%s create queue %s is fail.\n", __FUNCTION__, tmp_queue->sq_name);

}

static void dpdk_mempool_init(void)
{
	unsigned int nindex;
	struct dpdk_mempool_params *pmpparams = NULL;

	for (nindex = 0; nindex < rte_socket_count(); nindex++) {
		pmpparams = dpdk_conf.mp_params + nindex;
		pmpparams->mempool_dev = NULL;
		pmpparams->mempool_local = NULL;

		if ((dpdk_conf.numa_nodemask & (1 << nindex)) == 0)
			continue;

		pmpparams = dpdk_conf.mp_params + nindex;

		if (pmpparams->mempool_dev == NULL) {
			sprintf(pmpparams->mp_fw_name, "mp_fw%d", nindex);
			pmpparams->pool_buffer_size = MBUF_SIZE;	//2048 + sizeof(struct rte_mbuf) + RTE_PKTMBUF_HEADROOM,
			pmpparams->pool_cache_size = 512;
			pmpparams->pool_size = dpdk_conf.mbuf_pool_size;
			if (pmpparams->pool_size < DPDK_MEMPOOL_MIN_SIZE)
				pmpparams->pool_size = DPDK_MEMPOOL_MIN_SIZE;

			log_debug("init mp forwarding %s, pool size %d\n", pmpparams->mp_fw_name, pmpparams->pool_size);

			pmpparams->mempool_dev = rte_pktmbuf_pool_create(pmpparams->mp_fw_name,
									 pmpparams->pool_size,
									 pmpparams->pool_cache_size,
									 0,
									 pmpparams->pool_buffer_size -
									 STRUCT_MBUF_SIZE, nindex);
			if (pmpparams->mempool_dev == NULL) {
				log_fatal("Cannot create mbuf pool %s\n", pmpparams->mp_fw_name);
			}
		}

		if (pmpparams->mempool_local == NULL) {
			sprintf(pmpparams->mp_local_name, "mp_local%d", nindex);
			pmpparams->kni_pool_buf_size = MBUF_PAYLOAD_SIZE + RTE_PKTMBUF_HEADROOM;
			pmpparams->kni_pool_size = dpdk_conf.kni_pool_size;
			pmpparams->kni_pool_cache_size = DPDK_KNI_MBUF_SZ;

			log_debug("init mp local %s, pool size %d\n", pmpparams->mp_local_name, pmpparams->kni_pool_size);

			pmpparams->mempool_local = rte_pktmbuf_pool_create(pmpparams->mp_local_name,
									   pmpparams->kni_pool_size,
									   pmpparams->kni_pool_cache_size,
									   0,
									   pmpparams->kni_pool_buf_size,
									   nindex);
			if (pmpparams->mempool_local == NULL)
				log_fatal("Cannot create mbuf pool '%s'\n", pmpparams->mp_local_name);
		}

		if (pmpparams->mempool_control == NULL) {
			sprintf(pmpparams->mp_control_name, "mp_control%d", nindex);
			pmpparams->control_pool_buf_size = MBUF_PAYLOAD_SIZE + RTE_PKTMBUF_HEADROOM;
			pmpparams->control_pool_size = dpdk_conf.ctrl_pool_size;
			pmpparams->control_pool_cache_size = 32;

			log_debug("init mp control %s, pool size %d\n", pmpparams->mp_control_name, pmpparams->control_pool_size);

			pmpparams->mempool_control = rte_pktmbuf_pool_create(pmpparams->mp_control_name,
									     pmpparams->control_pool_size,
									     pmpparams->control_pool_cache_size,
									     0,
									     pmpparams->control_pool_buf_size,
									     nindex);
			if (pmpparams->mempool_control == NULL)
				log_fatal("Cannot create mbuf pool '%s'\n", pmpparams->mp_control_name);
		}
	}
}

static void dpdk_params_init(void)
{
	int cindex;
	unsigned int pindex, qindex;
	char dpdk_ports[32];
	struct dpdk_port_params *pparams = NULL;
	struct dpdk_soft_queue *psoftq = NULL;
	int socket;
	struct rte_malloc_socket_stats sock_stats;

	for (socket = 0; socket < rte_socket_count(); socket++) {
		if ((rte_malloc_get_socket_stats(socket, &sock_stats) < 0))
			continue;
	}

	if (sock_stats.heap_totalsz_bytes > (8ll << 30)) {
		/* 此组参数会让系统预分配大约1800M内存，适用于16G或以上内存的设备 */
		dpdk_conf.mbuf_pool_size = 8192 * 32 * 2;
		dpdk_conf.kni_pool_size = 8192 * 8;
		dpdk_conf.ctrl_pool_size = 8192;
		dpdk_conf.nic_desc_size = 2048;
	} else if (sock_stats.heap_totalsz_bytes > (2ll << 30)) {
		/* 此组参数会让系统预分配大约800M内存，适用于4G或以上内存的设备 */
		dpdk_conf.mbuf_pool_size = 8192 * 32;
		dpdk_conf.kni_pool_size = 8192 * 8;
		dpdk_conf.ctrl_pool_size = 8192;
		dpdk_conf.nic_desc_size = 2048;
	} else {
		/* 此组参数会让系统预分配大约200M内存，这样2G内存的设备也能运行系统了 */
		dpdk_conf.mbuf_pool_size = 8192 * 8;
		dpdk_conf.kni_pool_size = 8192 * 2;
		dpdk_conf.ctrl_pool_size = 4096;
		dpdk_conf.nic_desc_size = 1024;
	}

	ngfw_get_dpdk_ports(dpdk_ports, 31);
	if (atoi(dpdk_ports) > rte_eth_dev_count_total())
		dpdk_conf.phyports = rte_eth_dev_count_total();
	else
		dpdk_conf.phyports = atoi(dpdk_ports);

	for (qindex = 0, pindex = 0; pindex < dpdk_conf.phyports; pindex++, qindex = 0) {
		pparams = dpdk_conf.port_params + pindex;
		pparams->pindex = pindex;
		pparams->socket_id = rte_eth_dev_socket_id(pindex);
		pparams->hwq_rxdesc = dpdk_conf.nic_desc_size;
		pparams->hwq_txdesc = dpdk_conf.nic_desc_size;
#ifdef PLATFORM_LOONGSON
		pparams->hwq_rxdesc = RTE_LOONGSON_RX_DESC_DEFAULT;
		pparams->hwq_txdesc = RTE_LOONGSON_TX_DESC_DEFAULT;
		pparams->hw_rx_queues = 1;	//rte_lcore_count();
		pparams->hw_tx_queues = 1;	//rte_lcore_count();
#else
		pparams->hw_rx_queues = dpdk_conf.numa_rxs;
		pparams->hw_tx_queues = 1;//rte_lcore_count();
#endif
		dp_memcpy(&pparams->dev_conf, &gdev_conf, sizeof(gdev_conf));
		pparams->rx_conf = NULL;
		pparams->tx_conf = NULL;
		//dp_memcpy(&pparams->rx_conf, &grx_conf, sizeof(grx_conf));
		//dp_memcpy(&pparams->tx_conf, &gtx_conf, sizeof(gtx_conf));

		for (cindex = 0; cindex < RTE_MAX_LCORE; cindex++) {
			if (rte_lcore_is_enabled(cindex) == 0)
				continue;

#ifdef PLATFORM_LOONGSON
			if (pindex == 4) {	/* ge2 */
				if (cindex == 1) {
					pparams->hw_rq_maps[cindex] = qindex;
					pparams->hw_tq_maps[cindex] = qindex;
					pparams->p2cindex = cindex;
					psoftq = pparams->soft_tx_queue + cindex;
					dpdk_soft_queue_init(psoftq, DPDK_SOFT_TQ, pindex, cindex);
				} else {
					pparams->hw_rq_maps[cindex] = rte_lcore_count();
					pparams->hw_tq_maps[cindex] = qindex;	//rte_lcore_count();
				}

				continue;
			}

			if (cindex == (pindex % rte_lcore_count())) {
				pparams->hw_rq_maps[cindex] = qindex;
				pparams->hw_tq_maps[cindex] = qindex;
				pparams->p2cindex = cindex;
				psoftq = pparams->soft_tx_queue + cindex;
				dpdk_soft_queue_init(psoftq, DPDK_SOFT_TQ, pindex, cindex);
			} else {
				pparams->hw_rq_maps[cindex] = rte_lcore_count();
				pparams->hw_tq_maps[cindex] = qindex;	//rte_lcore_count();
			}
#else
			pparams->hw_rq_maps[cindex] = qindex;
			pparams->hw_tq_maps[cindex] = qindex;
			qindex++;
			pparams->p2cindex = -1;
#endif
		}
	}

	for (cindex = 0; cindex < RTE_MAX_LCORE; cindex++) {
		if (rte_lcore_is_enabled(cindex) == 0)
			continue;

		psoftq = dpdk_conf.core_rcv_queue + cindex;
		dpdk_conf.core_rcv_maps[cindex] = cindex;
		dpdk_soft_queue_init(psoftq, DPDK_SOFT_RQ, -1, cindex);
	}
}

static inline void dpdk_one_port_init(uint16_t port_id)
{
	unsigned lcore_id;
	int ret;
	int socket_id;
	uint16_t queue_id;
	uint16_t nb_rx_q;
	uint16_t nb_tx_q;
	int drv_core_id;
	int count;
	struct rte_eth_dev_info dev_info;
	struct dpdk_port_params *pparams;
	struct dpdk_mempool_params *pmpparams;
	struct dpdk_soft_queue *soft_rxq = NULL;
	struct dpdk_soft_queue *soft_txq = NULL;

	log_info("Initializing NIC port %u ...\n", port_id);

	rte_eth_dev_info_get(port_id, &dev_info);

	pparams = dpdk_conf.port_params + port_id;

	pparams->tx_core_id = port_id % dpdk_conf.numa_txs;

	/* Init port */
#if 1
	nb_rx_q = (pparams->hw_rx_queues < dev_info.max_rx_queues) ?
	    pparams->hw_rx_queues : dev_info.max_rx_queues;
	nb_tx_q = (pparams->hw_tx_queues < dev_info.max_tx_queues) ?
	    pparams->hw_tx_queues : dev_info.max_tx_queues;

	if (strcmp(dev_info.driver_name, "net_tsrn10") == 0) {
		nb_rx_q = 1;
		nb_tx_q = 1;
	}
#else
	nb_rx_q = 1;
	nb_tx_q = 1;
#endif

	dpdk_conf.port_nb_rxq_max[port_id] = nb_rx_q;
	dpdk_conf.port_nb_txq_max[port_id] = nb_tx_q;
	pparams->hw_rx_queues = nb_rx_q;
	pparams->hw_tx_queues = nb_tx_q;

	/* virtual function don't support lsc
	   em on kvm/vmware don't support lsc
	   virito on dpdk-pc platform(that is, dplane running on ubuntu) may also failed to get link-state with lsc sometimes.
	 */
	if (!(*dev_info.dev_flags & RTE_ETH_DEV_INTR_LSC))
		pparams->dev_conf.intr_conf.lsc = 0;

	/* ZTE requires that enable vlan-strip by default.
	 * I think It's a reasonable requirement when running on cloud platform,
	 * otherwise, vfw may not be connected if vlan is set on host.
	 *
	 * Note: if you want to create subif on xge0 (which is a VF), which means
	 * you want to receive and send vlan packet on xge0,
	 * you must disable vlan-strip on xge0  by manual, and ensure that
	 * there is no vlan set for this VF on host.
	 *
	 * in additional, if you want to receive vlan packets, you may need to
	 * set vlan-filter, for SR-IOV(vf).
	 */
	if (IS_DPDK_VF_DRV(dev_info.driver_name)) {
		//pparams->dev_conf.rxmode.hw_vlan_strip = 1;
		//pparams->dev_conf.rxmode.hw_vlan_filter = 1;
		/* VF can't disable HW CRC Strip */
		//pparams->dev_conf.rxmode.hw_strip_crc = 1;

		pparams->dev_conf.rxmode.offloads |=
		    RTE_ETH_RX_OFFLOAD_VLAN_STRIP | RTE_ETH_RX_OFFLOAD_VLAN_FILTER | ~RTE_ETH_RX_OFFLOAD_KEEP_CRC;
	}

	if (IS_DPDK_PARAVIRT_DRV(dev_info.driver_name))
		pparams->dev_conf.rxmode.mq_mode = 0;

	pparams->dev_conf.rx_adv_conf.rss_conf.rss_hf = (RTE_ETH_RSS_IP | RTE_ETH_RSS_UDP | RTE_ETH_RSS_TCP) & dev_info.flow_type_rss_offloads;
	if (nb_rx_q == 1)
		pparams->dev_conf.rx_adv_conf.rss_conf.rss_hf = 0;

	ret = rte_eth_dev_configure(port_id, nb_rx_q, nb_tx_q, &pparams->dev_conf);
	if (ret < 0)
		rte_panic("Cannot init NIC port %u (%d)\n", port_id, ret);

	ret = rte_eth_dev_callback_register(port_id, RTE_ETH_EVENT_INTR_LSC, dpdk_port_event_callback, NULL);
	if (ret < 0)
		log_error("rte_eth_dev_callback_register port %u failed\n", port_id);

#if 1
	socket_id = rte_eth_dev_socket_id(port_id);
#if RTE_VERSION > RTE_VERSION_NUM(22, 11, 0, 0)
	if (socket_id < 0)
		socket_id = 0;
#endif
	pmpparams = dpdk_conf.mp_params + socket_id;

	/* Init RX queues */
	for (queue_id = 0; queue_id < nb_rx_q; queue_id++) {
		ret = rte_eth_rx_queue_setup(port_id, queue_id, min_t(int, dev_info.rx_desc_lim.nb_max, pparams->hwq_rxdesc),
					     socket_id, pparams->rx_conf, pmpparams->mempool_dev);
		if (ret < 0)
			rte_exit(EXIT_FAILURE, "rte_eth_rx_queue_setup queueid %d err=%d, port=%d\n",
				queue_id, ret, port_id);
	}

	/* Init TX queues */
	for (queue_id = 0; queue_id < nb_tx_q; queue_id++) {
		ret = rte_eth_tx_queue_setup(port_id, queue_id, min_t(int, dev_info.tx_desc_lim.nb_max, pparams->hwq_txdesc),
					socket_id, pparams->tx_conf);
		if (ret < 0)
			rte_exit(EXIT_FAILURE, "rte_eth_tx_queue_setup queueid %d err=%d, port=%d\n",
				queue_id, ret, port_id);
	}
#endif

	for (lcore_id = 0; lcore_id < RTE_MAX_LCORE; lcore_id++) {
		if (rte_lcore_is_enabled(lcore_id) == 0)
			continue;

		socket_id = rte_lcore_to_socket_id(lcore_id);
		if ((dpdk_conf.numa_nodemask & (1 << socket_id)) == 0)
			continue;

		pmpparams = dpdk_conf.mp_params + socket_id;

#if 0
		/* Init RX queues */
		if (pparams->hw_rq_maps[lcore_id] < dev_info.max_rx_queues) {
			ret = rte_eth_rx_queue_setup(pindex, pparams->hw_rq_maps[lcore_id], pparams->hwq_rxdesc,
						     nodeid, pparams->rx_conf, pmpparams->mempool_dev);
			if (ret < 0)
				rte_exit(EXIT_FAILURE,
					 "core %d rte_eth_rx_queue_setup queueid %d err=%d, port=%d\n",
					 lcore_id, pparams->hw_rq_maps[lcore_id], ret, pindex);
		}

		/* Init TX queues */
		if (pparams->hw_tq_maps[lcore_id] < dev_info.max_tx_queues) {
			ret = rte_eth_tx_queue_setup(pindex, pparams->hw_tq_maps[lcore_id], pparams->hwq_txdesc,
						     nodeid, pparams->tx_conf);
			if (ret < 0)
				rte_exit(EXIT_FAILURE,
					 "core %d rte_eth_tx_queue_setup queueid %d err=%d, port=%d\n",
					 lcore_id, pparams->hw_rq_maps[lcore_id], ret, pindex);
		}
#endif

#ifndef PLATFORM_LOONGSON
		/* 当网卡队列数小于核数时，需要进行分流:
		   驱动核(drv_core_id): 从网卡硬件队列收包并分流到不同的业务核，同时从对应的业务核取发包缓存队列并发送至网卡硬件队列；
		   业务核(lcore_id): 不会操作网卡硬件队列，只处理被驱动核分流至本核软件缓存队列(soft_rxq)中的报文；
		   业务处理完成后，将报文缓存至本核软件发包缓存队列(soft_txq)，待驱动核取走。
		 */
		if (pparams->hw_rq_maps[lcore_id] >= dev_info.max_rx_queues) {
			/* 驱动核设置分流映射 */
			drv_core_id = lcore_id % nb_rx_q;
			count = pparams->dispatch_conf[drv_core_id].rx_disp_core_num;
			pparams->dispatch_conf[drv_core_id].rx_disp_to_core[count] = lcore_id;
			pparams->dispatch_conf[drv_core_id].rx_disp_core_num++;
//			RTE_LOG(INFO, USER1,
//				"Init dispatch rxq on core[%d] for port[%d]: core%d dispatch to core%d \n",
//				lcore_id, port_id, drv_core_id, lcore_id);

			/* 业务核初始化接收缓存队列 */
			soft_rxq = &dpdk_conf.core_disp_queue[lcore_id].rx_queues[drv_core_id];
			if (soft_rxq->queue_ring == NULL) {
				dpdk_soft_queue_init(soft_rxq, DPDK_DISPATCH_RQ, -1, lcore_id);
				count = dpdk_conf.core_disp_queue[lcore_id].rx_queues_num;
				dpdk_conf.core_disp_queue[lcore_id].rx_queues_maps[count] = drv_core_id;
				dpdk_conf.core_disp_queue[lcore_id].rx_queues_num++;
			}
		}

		/* 初始化每核应从哪些核取缓存报文 */
		/* 驱动核设置业务发送映射 */
		drv_core_id = lcore_id % nb_tx_q;
		count = pparams->dispatch_conf[drv_core_id].tx_get_core_num;
		pparams->dispatch_conf[drv_core_id].tx_get_from_core[count] = lcore_id;
		pparams->dispatch_conf[drv_core_id].tx_get_core_num++;
//		RTE_LOG(INFO, USER1, "Init soft txq on core[%d] for port[%d]: core%d get from core%d \n",
//			lcore_id, port_id, drv_core_id, lcore_id);

		/* 业务核初始化发送缓存队列 */
		soft_txq = &dpdk_conf.core_disp_queue[lcore_id].tx_queues[port_id];
		dpdk_soft_queue_init(soft_txq, DPDK_DISPATCH_TQ, port_id, lcore_id);
#endif
	}

	/* Start port */
	ret = rte_eth_dev_start(port_id);
	if (ret < 0)
		RTE_LOG(ERR, USER1, "Cannot start port %u (%d)\n", port_id, ret);

	rte_eth_promiscuous_enable(port_id);
}

int flow_dpdk_check_hwtxrx_err(int pindex)
{
	int ret = 0;
#ifdef PLATFORM_LOONGSON
	if (loongson_check_error_on == 0)
		return ret;

	struct dpdk_port_params *pparams = NULL;
	struct net_device *dev = NULL;

	pparams = dpdk_conf.port_params + pindex;
	dev = dev_lookup_by_name(pparams->dev_name);
	if (dev != NULL && DEV_IS_LISTEN(dev))
		pparams->is_listen_mode = 1;
	else
		pparams->is_listen_mode = 0;

	rte_eth_stats_get(pindex, &pparams->stats[pparams->current_index]);

	if (pparams->current_index == 1)
		pparams->current_index = 0;
	else
		pparams->current_index = 1;

	if (pparams->stats[0].opackets == pparams->stats[1].opackets
	    && pparams->stats[0].ipackets == pparams->stats[1].ipackets
	    && pparams->stats[0].imissed == pparams->stats[1].imissed) {
		pparams->allow_max_timers = 0;
		return ret;
	}

	if (pparams->is_listen_mode == 0) {
		if (pparams->stats[0].opackets == pparams->stats[1].opackets)
			pparams->allow_max_timers++;
		else if (pparams->stats[0].ipackets == pparams->stats[1].ipackets
			 && pparams->stats[0].imissed != pparams->stats[1].imissed)
			pparams->allow_max_timers++;
		else
			pparams->allow_max_timers = 0;
	} else {
		if (pparams->stats[0].ipackets == pparams->stats[1].ipackets
		    && pparams->stats[0].imissed != pparams->stats[1].imissed)
			pparams->allow_max_timers++;
	}

	if (pparams->allow_max_timers > 3) {
		dp_config_wlock();
		rte_eth_dev_set_link_down(pparams->pindex);
		dpdk_one_port_init(pindex);
		rte_eth_dev_set_link_up(pparams->pindex);
		dp_config_wunlock();
		pparams->allow_max_timers = 0;
		memset(pparams->stats, 0, sizeof(pparams->stats));
		ret = 1;
	}
#endif
	return ret;
}

static inline void dpdk_ports_init(void)
{
	uint32_t pindex;

	/* Init NIC ports, then start the ports */
	for (pindex = 0; pindex < dpdk_conf.phyports; pindex++) {
		dpdk_one_port_init(pindex);
	}
}

static void dpdk_work_queue_init(void)
{
	int i = 0;
	char queue_name[32];

	for (i = 0; i < dpdk_conf.numa_workers; i++) {
		snprintf(queue_name, sizeof(queue_name), "work_queue%d", i);
		dpdk_conf.rx_ring[i] = rte_ring_create(queue_name, 64 << 10, SOCKET_ID_ANY, RING_F_SC_DEQ);
		if (dpdk_conf.rx_ring[i] == NULL)
			printf("----- create rx_ring error\n");
	}
}

/* 声明统一的 cryptodev 初始化函数 */
#include "dpdk_cryptodev_unified.h"

void dpdk_init(void)
{
	dpdk_params_init();
	dpdk_mempool_init();
	dpdk_ports_init();
	dpdk_kni_memzone_init();
	dpdk_work_queue_init();

	/* 初始化 cryptodev */
	int ret = unified_cryptodev_init();
	if (ret < 0) {
		log_error("Failed to initialize cryptodev: %d\n", ret);
		/* 继续运行，但禁用 cryptodev */
	} else {
		log_info("Cryptodev initialized successfully\n");
	}
}


