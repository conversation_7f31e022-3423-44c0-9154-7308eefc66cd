#
#  Top of build tree
#
TOP_DIR = ../../..

#
#  Top of current module
#
MODULE_TOP_DIR = ..

#
#  macros definition
#
include $(TOP_DIR)/arch.mk

#
#  include flag
#
INC_DIR += -I$(MODULE_TOP_DIR)/include  -I$(MODULE_TOP_DIR)/include/linux -I$(MODULE_TOP_DIR)/octeon/config
INC_DIR += -I$(MODULE_TOP_DIR)/include/app_engine
INC_DIR += -I$(MODULE_TOP_DIR)/include/app_engine/av/
ifeq ($(ARCH), OCTEON)
CFLAGS_LOCAL += $(CPPFLAGS_GLOBAL)
INC_DIR += -I$(OCTEON_ROOT)/target/include
endif

#
#  link flag (only for bin)
#

CFLAGS_LOCAL += -Wno-misleading-indentation -Wno-implicit-fallthrough -Wno-implicit-function-declaration -Wno-error
CFLAGS_LOCAL += -DENABLE_CRYPTODEV
ifeq ($(OEM), VRV)
	CFLAGS_LOCAL += -DOEM_VRV
endif

LDFLAGS_PATH += 

DEP_LIBS_LIST += 

#
#  module properties
#
BUILD_TYPE = obj
BUILD_NAME = flow

#
#  subdirectories to build. SUB_MOD are subdirectory with objs to link
#
SUB_MOD =
#
#  You can add other libraries/executable in directories to build
#
SUB_DIR = $(SUB_MOD)

######## Include common rules
include $(TOP_DIR)/rules.mk

