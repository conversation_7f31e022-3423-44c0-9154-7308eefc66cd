# dplane 集成 cryptodev 支持进度

## 已完成的工作

### 第一步：基础设施搭建

1. 创建了 `xfrm_cryptodev.h` 文件，定义了 cryptodev 集成所需的数据结构和函数接口
2. 创建了 `xfrm_cryptodev.c` 文件，实现了 cryptodev 初始化和全局管理功能
3. 创建了 `xfrm_state.h` 文件，扩展了 xfrm_state 结构以支持 cryptodev
4. 修改了 `xfrm_state.c` 文件，添加了对 cryptodev 会话的支持：
   - 在 `xfrm_state_alloc()` 函数中初始化 cryptodev 字段
   - 在 `__xfrm_state_delete()` 函数中销毁 cryptodev 会话
   - 在 `xfrm_init_state()` 函数中创建 cryptodev 会话
5. 修改了 `xfrm_policy.c` 文件，在 `xfrm_init()` 函数中初始化 cryptodev
6. 修改了 `Makefile`，添加了 cryptodev 相关的编译选项
7. 创建了 `xfrm_cryptodev_session.c` 文件，实现了会话管理功能

### 第二步：数据包处理集成

1. 创建了 `xfrm_cryptodev_ops.c` 文件，实现了加密操作处理：
   - 实现了 `skb_to_mbuf()` 和 `mbuf_to_skb()` 函数，用于数据包转换
   - 实现了 `xfrm_set_crypto_op_params()` 函数，设置加密操作参数
   - 实现了 `xfrm_cryptodev_encrypt()` 和 `xfrm_cryptodev_decrypt()` 函数，处理加密和解密
   - 实现了批量操作入队和出队函数
   - 实现了完成处理函数

2. 修改了 `xfrm_input.c` 文件，集成解密支持：
   - 在 `xfrm_input()` 函数中添加了 cryptodev 解密支持
   - 添加了 `xfrm_input_crypto_done()` 函数，处理异步完成的解密操作

3. 修改了 `xfrm_output.c` 文件，集成加密支持：
   - 在 `xfrm_output2()` 函数中添加了 cryptodev 加密支持
   - 添加了 `xfrm_output_crypto_done()` 函数，处理异步完成的加密操作

4. 创建了 `xfrm_cryptodev_poll.c` 文件，实现异步处理：
   - 实现了轮询线程，处理完成的加密操作
   - 实现了设备健康检查，监控和恢复加密设备
   - 实现了线程启动和停止函数

### 第三步：代码校对和修正

1. 修复了 cryptodev 轮询线程的启动问题：
   - 在 `xfrm_init()` 函数中添加了启动轮询线程的代码
   - 确保在 cryptodev 初始化成功后才启动轮询线程

2. 添加了对 IPv6 的 cryptodev 支持：
   - 在 `xfrm_input6()` 函数中添加了 cryptodev 解密支持
   - 确保 IPv6 数据包也能使用硬件加速

3. 添加了资源清理功能：
   - 创建了 `xfrm_cleanup()` 函数，用于清理 cryptodev 资源
   - 在清理函数中停止轮询线程并释放 cryptodev 资源
   - 在 `xfrm_cryptodev.h` 中添加了函数声明

### 第四步：集成到 dplane 主程序和添加配置管理接口

1. 创建了 `dpdk_cryptodev.c` 文件，实现了 DPDK cryptodev 的初始化和管理：
   - 实现了 `dpdk_cryptodev_init()` 函数，初始化 DPDK cryptodev
   - 实现了 `dpdk_cryptodev_uninit()` 函数，清理 DPDK cryptodev 资源
   - 实现了设备探测、内存池初始化和设备配置功能

2. 修改了 `dpdk_init.c` 文件，集成 cryptodev 初始化：
   - 在 `dpdk_init()` 函数中添加了 cryptodev 初始化代码
   - 确保在 DPDK 初始化后初始化 cryptodev

3. 修改了 `dpdk_main.c` 文件，添加清理函数：
   - 创建了 `dpdk_cleanup()` 函数，用于清理资源
   - 使用 `atexit()` 注册清理函数，确保程序退出时释放资源

4. 创建了 `xfrm_algo_map.c` 文件，实现了算法映射：
   - 实现了 IPsec 算法到 DPDK cryptodev 算法的映射
   - 支持加密算法、认证算法和 AEAD 算法的映射
   - 实现了设置加密变换的函数

5. 创建了 `xfrm_cryptodev_config.c` 文件，实现了配置和管理接口：
   - 实现了 cryptodev 的启用/禁用功能
   - 实现了调试级别、软件回退和统计信息间隔的配置
   - 实现了统计信息收集和显示功能
   - 实现了设备管理和重置功能
   - 实现了命令行处理函数

6. 创建了 `xfrm_cryptodev_debug.c` 文件，实现了调试和日志功能：
   - 实现了不同级别的日志函数
   - 实现了日志缓冲区管理
   - 实现了各种调试信息打印函数，包括会话、操作、mbuf、SA 和上下文信息

### 第五步：补充修改和完善

1. 修改了 `dplane/net/xfrm/Makefile`，添加新文件到编译列表：
   - 添加了所有 cryptodev 相关源文件
   - 添加了 DPDK cryptodev 库依赖

2. 创建了 `xfrm_cryptodev_config.h` 文件，添加 cryptodev 配置选项：
   - 定义了各种配置参数和默认值
   - 定义了调试级别和错误处理策略
   - 声明了配置和管理函数

3. 修改了 `xfrm_cryptodev.h` 文件，包含配置头文件：
   - 移除了重复的配置定义
   - 使用统一的配置参数

4. 创建了 `xfrm_cryptodev_cli.c` 文件，实现命令行接口集成：
   - 实现了命令处理函数
   - 实现了命令分发逻辑
   - 提供了命令注册机制

### 第六步：集成到 dplane 主循环

1. 修改了 `xfrm_cryptodev_poll.c` 文件，删除轮询线程相关代码：
   - 删除了 `xfrm_cryptodev_poll_thread_func()` 函数
   - 删除了 `xfrm_cryptodev_start_poll_thread()` 和 `xfrm_cryptodev_stop_poll_thread()` 函数
   - 创建了 `xfrm_cryptodev_poll()` 函数，用于在主循环中轮询 cryptodev

2. 修改了 `xfrm_cryptodev.h` 文件，更新函数声明：
   - 删除了轮询线程相关函数声明
   - 添加了 `xfrm_cryptodev_poll()` 函数声明

3. 修改了 `xfrm_policy.c` 文件，移除轮询线程启动和停止代码：
   - 在 `xfrm_init()` 函数中移除了启动轮询线程的代码
   - 在 `xfrm_cleanup()` 函数中移除了停止轮询线程的代码

4. 修改了 `flow_main.c` 文件，在主循环中添加 cryptodev 轮询：
   - 在 `flow_async_process()` 函数中添加了对 cryptodev 的轮询
   - 添加了必要的头文件和外部声明

### 第七步：修复编译问题

1. 修复了 `RTE_CRYPTODEV_MAX_DEV_COUNT` 宏未定义的问题：
   - 定义了自定义的 `DPDK_CRYPTODEV_MAX_DEVS` 宏
   - 替换了所有使用 `RTE_CRYPTODEV_MAX_DEV_COUNT` 的地方

2. 修复了 `struct rte_cryptodev_qp_conf` 结构体成员问题：
   - 移除了 `mp_session_private` 成员的使用
   - 根据不同版本的 DPDK 调整了代码

3. 添加了版本兼容性支持：
   - 添加了 `RTE_VERSION_NUM` 宏定义
   - 使用条件编译处理不同版本的 DPDK API
   - 为较旧版本的 DPDK 提供了替代实现

4. 修复了算法映射表问题：
   - 重新定义了算法映射表结构
   - 使用基于名称和密钥大小的映射方式
   - 添加了 AEAD 算法支持
   - 实现了新的算法查找函数

5. 修复了 `xfrm_set_crypto_xforms` 函数未被调用的问题：
   - 在 `xfrm_cryptodev_session.c` 中调用 `xfrm_set_crypto_xforms` 函数
   - 修改了 `xfrm_cryptodev_select_device` 函数，使用新的算法映射方式
   - 添加了函数声明到 `xfrm_cryptodev.h` 文件

6. 修复了 `xfrm_algo_map.c` 文件中的结构体成员访问问题：
   - 修改了 `xfrm_set_cipher_params` 函数，使用 `x->props.ealgo` 和 `x->enc_key` 替代 `x->ealg->alg_name` 和 `x->ealg->alg_key`
   - 修改了 `xfrm_set_auth_params` 函数，使用 `x->props.aalgo` 和 `x->auth_key` 替代 `x->aalg->alg_name` 和 `x->aalg->alg_key`
   - 修改了 `xfrm_set_aead_params` 函数，暂时禁用 AEAD 算法支持
   - 修改了 `xfrm_set_crypto_xforms` 函数，使用 `x->props.mode` 替代 `x->xso.dir`

7. 修复了编译错误和警告：
   - 删除了 `xfrm_cryptodev.c` 文件中重复的结构体定义
   - 删除了 `xfrm_cryptodev.c` 文件中重复的函数定义
   - 修改了统计函数的定义，从 `static inline` 改为全局函数
   - 添加了 `RTE_CRYPTO_*_LIST_END` 宏的定义
   - 修复了 `rte_cryptodev_info` 结构体中 `device_name` 成员不存在的问题
   - 修复了未使用变量的警告
   - 修复了不可达代码的警告

8. 修复了 `xfrm_state.h` 重定义问题：
   - 移除了 `xfrm_cryptodev_cli.c` 文件中对 `xfrm_state.h` 的包含
   - 移除了 `xfrm_cryptodev_session.c` 文件中对 `xfrm_state.h` 的包含
   - 移除了 `xfrm_cryptodev_ops.c` 文件中对 `xfrm_state.h` 的包含
   - 移除了 `xfrm_cryptodev_config.c` 文件中对 `xfrm_state.h` 的包含

9. 修复了 `xfrm_cryptodev_config.c` 文件中的其他错误：
   - 修改了 `cryptodev_enabled` 变量的声明，从 `static` 改为全局变量
   - 修复了 `print_cryptodev_info` 函数中的设备名称访问错误
   - 移除了 `error_count` 成员的访问，因为该成员在某些版本的 DPDK 中不存在
   - 移除了 `xfrm_cryptodev_start_poll_thread` 和 `xfrm_cryptodev_stop_poll_thread` 函数的调用，因为轮询线程已集成到主循环中

10. 修复了 `xfrm_cryptodev_debug.c` 文件中的错误：
    - 移除了对 `xfrm_state.h` 的包含
    - 修改了 `print_crypto_session_info` 函数，移除了对已删除成员的访问
    - 添加了对 `session->flags` 成员的访问，提供更多会话信息
    - 修改了 `print_sa_info` 函数，使用 `x->props.mode` 替代 `x->xso.dir`
    - 修改了 `print_sa_info` 函数，使用 `x->props.ealgo` 和 `x->enc_key_len` 替代 `x->ealg->alg_name` 和 `x->ealg->alg_key_len`
    - 修改了 `print_sa_info` 函数，使用 `x->props.aalgo` 和 `x->auth_key_len` 替代 `x->aalg->alg_name` 和 `x->aalg->alg_key_len`
    - 移除了对 `x->aead` 成员的访问，因为该成员不存在
    - 移除了对 `x->crypto_session` 和 `x->crypto_flags` 成员的访问，因为这些成员不存在

11. 修复了 `xfrm_cryptodev_ops.c` 文件中的错误：
    - 修改了 `xfrm_set_crypto_op_params` 函数，移除了对 `sym_op->cipher.iv` 成员的访问，使用 `sym_op->cipher.data` 代替
    - 修改了 `xfrm_set_crypto_op_params` 函数，使用 `x->props.mode` 替代 `x->xso.dir`
    - 修改了 `xfrm_set_crypto_op_params` 函数，移除了对 `sym_op->auth.digest.length` 成员的访问
    - 修改了 `xfrm_cryptodev_encrypt` 和 `xfrm_cryptodev_decrypt` 函数，使用 `x->context` 替代 `x->crypto_session`

12. 修复了 `xfrm_cryptodev_poll.c` 文件中的错误：
    - 移除了对 `xfrm_state.h` 的包含
    - 修改了 `xfrm_cryptodev_health_check` 函数，使用 `stats.enqueued_count - stats.dequeued_count` 作为错误估计，替代 `stats.error_count`
    - 添加了错误估计的计算逻辑，避免访问不存在的成员

13. 修复了 `xfrm_cryptodev_session.c` 文件中的错误：
    - 修改了 `xfrm_cryptodev_select_device` 函数，使用 `x->props.ealgo` 和 `x->enc_key_len` 替代 `x->ealg->alg_name` 和 `x->ealg->alg_key_len`
    - 修改了 `xfrm_cryptodev_select_device` 函数，使用 `x->props.aalgo` 和 `x->auth_key_len` 替代 `x->aalg->alg_name` 和 `x->aalg->alg_key_len`
    - 添加了算法 ID 到算法名称的映射逻辑
    - 修改了错误消息，使用算法 ID 替代算法名称
    - 修改了 `xfrm_cryptodev_session_create` 函数，使用 `x->context` 替代 `x->crypto_session`
    - 移除了对 `x->crypto_flags` 成员的访问
    - 修改了 `xfrm_cryptodev_session_destroy` 函数，使用 `x->context` 替代 `x->crypto_session`

14. 修复了 `xfrm_input.c` 文件中的错误：
    - 移除了对 `xfrm_state.h` 的包含
    - 修改了 cryptodev 相关代码，使用 `x->context` 替代 `x->crypto_session`
    - 修改了 `sa_type` 和 `tunn_dev` 成员的访问，使用 `x->props.mode` 替代
    - 添加了 `ret` 变量的声明
    - 修复了 `xfrm_input6` 函数中的 `crypto_session` 和 `crypto_flags` 成员访问错误
    - 修复了指针类型不兼容的警告，使用正确的类型转换
    - 移除了未使用的变量

15. 修复了 `xfrm_output.c` 文件中的错误：
    - 移除了对 `xfrm_state.h` 的包含
    - 修改了 cryptodev 相关代码，使用 `x->context` 替代 `x->crypto_session`
    - 移除了对 `x->crypto_flags` 成员的访问

16. 修复了 `xfrm_state.c` 文件中的错误：
    - 修改了 `xfrm_state_alloc` 函数，使用 `x->context` 替代 `x->crypto_session` 和 `x->crypto_flags`
    - 修改了 `__xfrm_state_delete` 函数，使用 `x->context` 替代 `x->crypto_session`

17. 统一了 `cryptodev_enabled` 变量的定义：
    - 在 `xfrm_cryptodev_config.c` 文件中保留唯一的定义
    - 在 `xfrm_cryptodev.c` 文件中使用 `extern` 声明
    - 在 `xfrm_policy.c` 文件中添加 `extern` 声明
    - 在 `xfrm_state.c` 文件中添加 `extern` 声明
    - 确保所有文件使用同一个 `cryptodev_enabled` 变量

18. 修复了 `xfrm_state.c` 文件中的错误：
    - 移除了对 `xfrm_state.h` 的包含
    - 移除了 `__xfrm_state_delete` 函数的重复声明
    - 修改了 `xfrm_init_state` 函数，移除了对 `x->crypto_flags` 成员的访问

19. 修复了函数重复定义的问题：
    - 修改了 `xfrm_cryptodev.c` 文件，将 `update_crypto_stats_submit`、`update_crypto_stats_complete` 和 `update_crypto_stats_fail` 函数声明为 `extern`
    - 修改了 `xfrm_cryptodev.c` 文件，将 `update_crypto_stats_fallback` 函数声明为 `extern`，并重命名为 `update_crypto_stats_sw_fallback`
    - 修改了 `xfrm_cryptodev.c` 文件，将 `crypto_stats` 变量声明为 `extern`
    - 修改了 `xfrm_cryptodev_config.c` 文件，将 `crypto_stats` 变量声明为全局变量
    - 修改了 `xfrm_cryptodev_config.h` 文件，添加了 `xfrm_cryptodev_stats` 结构体定义和 `crypto_stats` 变量的声明
    - 修改了 `xfrm_cryptodev.c` 文件，移除了 `xfrm_cryptodev_stats` 结构体的定义
    - 修改了 `xfrm_cryptodev.c` 文件，移除了 `static struct xfrm_cryptodev_stats crypto_stats` 的定义
    - 修改了 `xfrm_cryptodev.c` 文件，使用 `reset_crypto_stats()` 替代 `memset(&crypto_stats, 0, sizeof(crypto_stats))`
    - 修改了 `xfrm_cryptodev.c` 文件，使用 `print_crypto_stats()` 替代直接访问 `crypto_stats` 成员

20. 修复了 `cryptodev_enabled` 变量的多重定义问题：
    - 修改了 `dpdk_cryptodev.c` 文件，将 `cryptodev_enabled` 变量声明为 `extern`
    - 确保所有文件使用 `xfrm_cryptodev_config.c` 中定义的 `cryptodev_enabled` 变量

21. 修复了 `rte_pktmbuf_mtophys_offset` 函数未定义的问题：
    - 创建了 `xfrm_cryptodev_utils.c` 文件，实现了 `rte_pktmbuf_mtophys_offset` 函数
    - 添加了 `skb_to_mbuf` 和 `mbuf_to_skb` 函数的实现
    - 修改了 `Makefile`，添加了 `xfrm_cryptodev_utils.c` 文件
    - 修复了 `struct rte_mbuf` 结构体中 `udata64` 成员不存在的问题，使用 `shinfo` 成员替代
    - 实现了 `rte_pktmbuf_mtophys` 函数，解决了链接错误
    - 定义了 `phys_addr_t` 类型，确保代码能够正确编译

## 下一步工作

1. 测试和调试：
   - 修改了 `xfrm_cryptodev.c` 文件，添加了 OpenSSL PMD 支持
   - 添加了 `xfrm_cryptodev_create_openssl_device` 函数，用于创建 OpenSSL PMD 设备
   - 修改了 `xfrm_cryptodev_init` 函数，在初始化时创建 OpenSSL PMD 设备
   - 添加了 `rte_vdev.h` 头文件，用于 `rte_vdev_init` 函数
   - 创建了 `test_cryptodev.sh` 测试脚本，用于测试 cryptodev 功能
   - 修改了测试脚本，添加了 `--vdev="crypto_openssl"` 参数，显式创建 OpenSSL PMD 设备
   - 在没有加密卡的情况下，可以使用 OpenSSL 软件加密进行测试

2. 创建 OpenSSL cryptodev 设备：
   - 创建了 `dpdk_cryptodev_init.c` 和 `dpdk_cryptodev_init.h` 文件，实现了 OpenSSL cryptodev 设备的创建和初始化
   - 修改了 `dpdk/Makefile`，添加了 DPDK cryptodev 相关标志和链接库
   - 修改了 `dpdk_cryptodev.c` 文件，添加了 OpenSSL PMD 创建功能
   - 实现了 `create_openssl_cryptodev` 函数，用于创建 OpenSSL PMD 设备
   - 实现了 `configure_cryptodev` 函数，用于配置 cryptodev 设备
   - 实现了 `dpdk_openssl_cryptodev_init` 和 `dpdk_openssl_cryptodev_uninit` 函数，用于初始化和清理 OpenSSL cryptodev 资源
   - 修改了函数名，避免与 `dpdk_cryptodev.c` 文件中的函数名冲突
   - 修复了类型比较警告，使用 `(int)dev_id == -1` 替代 `dev_id < 0`
   - 在 `dpdk_cryptodev_uninit` 函数中调用 `dpdk_openssl_cryptodev_uninit` 函数，确保资源正确释放

3. 修复 OpenSSL cryptodev 设备创建错误：
   - 添加了更多错误检查和处理逻辑
   - 添加了 `sys/types.h` 和 `sys/stat.h` 头文件，用于检查文件是否存在
   - 修改了 `create_openssl_cryptodev` 函数，添加了更多错误检查和调试信息
   - 修改了 `configure_cryptodev` 函数，添加了设备 ID 有效性检查
   - 修改了 `dpdk_openssl_cryptodev_init` 函数，添加了 DPDK EAL 初始化检查
   - 添加了 OpenSSL 驱动检查，尝试加载 OpenSSL 驱动模块
   - 添加了更详细的错误信息，包括 errno 和错误描述

4. 修复编译错误：
   - 修复了 `rte_cryptodev_info_get` 函数的返回类型问题，该函数在某些 DPDK 版本中返回 void
   - 修改了 `rte_eal_get_configuration()` 的比较方式，使用 `rte_eal_has_hugepages()` 替代
   - 添加了 `rte_version.h` 头文件，用于 DPDK 版本相关功能
   - 修复了 `dev_id` 类型比较警告，使用 `0xFF` 作为错误标志，而不是 `-1`
   - 添加了对 `driver_name` 是否为 NULL 的检查，避免空指针访问

5. 增强 cryptodev 设备创建功能：
   - 添加了 DPDK 库路径检查，查找 `librte_crypto_openssl.so` 库
   - 添加了多种 PMD 驱动尝试，包括 `crypto_openssl`、`crypto_null`、`crypto_aesni_mb`、`crypto_qat` 和 `crypto_scheduler`
   - 添加了环境变量设置，尝试使用 `DPDK_LIBRARY_PATH` 环境变量
   - 修改了设备查找逻辑，接受任何加密设备，不仅仅是 OpenSSL
   - 添加了更详细的调试信息，帮助诊断问题

6. 解决 Connection refused 错误：
   - 添加了 `/dev/crypto` 设备检查，包括存在性和权限检查
   - 添加了更多的 DPDK EAL 初始化检查和调试信息
   - 添加了 DPDK 版本和配置检查
   - 添加了用户权限检查
   - 添加了 DPDK 日志检查
   - 修复了编译错误，包括变量重复声明和类型错误

7. 调查 cryptodev 初始化失败的原因：
   - 确认 DPDK 的 `crypto_openssl` PMD 不依赖于 `/dev/crypto` 设备
   - 还原了软件加密备选方案的修改，因为需要使用 DPDK 的 cryptodev 功能
   - 添加了更多的调试信息，包括：
     - 检查 DPDK 库路径和 `librte_crypto_openssl.so` 是否存在
     - 使用 `dlopen` 检查 `librte_crypto_openssl.so` 是否可加载
     - 检查 DPDK 版本和配置
     - 检查 `rte_vdev_init` 函数是否可用
     - 检查 DPDK 是否已经初始化
     - 检查 DPDK 是否支持虚拟设备
     - 使用 `rte_errno` 获取更详细的错误信息
     - 检查 DPDK 加载的驱动
   - 修复了编译错误：
     - 添加了 `_GNU_SOURCE` 宏定义，用于 `RTLD_DEFAULT`
     - 修复了 `rte_eal_get_configuration` 的返回类型问题
     - 修改了 `rte_vdev_bus` 的检查方式

8. 解决 DPDK 没有 vdev 支持的问题：
   - 确认了问题根源：DPDK 编译时没有包含虚拟设备（vdev）支持
   - 修改了 `dpdk_cryptodev_init.c`，在检测到没有 `rte_vdev_init` 函数时直接返回错误
   - 修改了 `dpdk_cryptodev.c`，在创建 OpenSSL PMD 设备失败时禁用 cryptodev 功能
   - 修改了 `xfrm_cryptodev.c`，添加了对没有 vdev 支持的处理：
     - 在 `xfrm_cryptodev_create_openssl_device` 函数中检查 `rte_vdev_init` 函数是否可用
     - 在 `xfrm_cryptodev_init` 函数中处理 `-ENOTSUP` 错误，禁用 cryptodev 功能但不返回错误
   - 添加了必要的头文件和宏定义

9. 解决 DPDK 库链接错误：
   - 确认了问题根源：Makefile 中链接了不存在的 DPDK 库
   - 修改了主 Makefile，移除了有问题的库链接：
     - `-lrte_net_knd` - KND 网络驱动
     - `-lrte_net_rnpgbe` - RNPGBE 网络驱动
     - `-lrte_net_tsrn10` - TSRN10 网络驱动
   - 简化了 DPDK 库链接配置，只包含确定存在的基础库
   - 注释掉了可选的网络驱动和其他库，避免链接错误
   - 创建了检查脚本 `check_dpdk_libs.sh` 用于检查可用的 DPDK 库
   - 编译和构建修改后的代码
   - 测试基本功能，确保 cryptodev 集成正常工作
   - 性能测试，比较软件加密和硬件加速的性能差异
   - 错误处理测试，确保系统在各种错误情况下能够正常工作

2. 优化：
   - 优化内存使用和数据复制
   - 优化批处理大小和队列配置
   - 优化设备选择和负载均衡策略