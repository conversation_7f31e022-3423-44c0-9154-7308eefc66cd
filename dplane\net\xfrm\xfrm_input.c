/*
 * xfrm_input.c
 *
 * Changes:
 * 	YOSHIFUJI Hideaki @USAGI
 * 		Split up af-specific portion
 *
 */

#include <linux/slab.h>
#include <linux/module.h>
#include <linux/netdevice.h>
#include <linux/netfilter_bridge.h>
#include <net/ip.h>
#include <net/xfrm.h>
#include <linux/ipv6.h>
#include "ipsec_msg.h"
#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_config.h"

/* Fetch spi and seq from ipsec header */
int xfrm_parse_spi(struct sk_buff *skb, u8 nexthdr, __be32 *spi, __be32 *seq)
{
	int offset, offset_seq;
	int hlen;

	switch (nexthdr) {
	case IPPROTO_AH:
		hlen = sizeof(struct ip_auth_hdr);
		offset = offsetof(struct ip_auth_hdr, spi);
		offset_seq = offsetof(struct ip_auth_hdr, seq_no);
		break;
	case IPPROTO_ESP:
		hlen = sizeof(struct ip_esp_hdr);
		offset = offsetof(struct ip_esp_hdr, spi);
		offset_seq = offsetof(struct ip_esp_hdr, seq_no);
		break;
	case IPPROTO_COMP:
		if (!pskb_may_pull(skb, sizeof(struct ip_comp_hdr)))
			return -EINVAL;
		*spi = htonl(ntohs(*(__be16*)(skb_transport_header(skb) + 2)));
		*seq = 0;
		return 0;
	default:
		return 1;
	}

	if (!pskb_may_pull(skb, hlen))
		return -EINVAL;

	*spi = *(__be32*)(skb_transport_header(skb) + offset);
	*seq = *(__be32*)(skb_transport_header(skb) + offset_seq);
	return 0;
}

int xfrm_prepare_input(struct xfrm_state *x, struct sk_buff *skb)
{
	struct xfrm_mode *inner_mode = x->inner_mode;
	int err;

	err = x->outer_mode->afinfo->extract_input(x, skb);
	if (err)
		return err;

	if (x->sel.family == AF_UNSPEC) {
		inner_mode = xfrm_ip2inner_mode(x, XFRM_MODE_SKB_CB(skb)->protocol);
		if (inner_mode == NULL)
			return -EAFNOSUPPORT;
	}

	skb->protocol = inner_mode->afinfo->eth_proto;
	return inner_mode->input2(x, skb);
}

int xfrm_input(struct sk_buff *skb, int nexthdr, __be32 spi, int encap_type)
{
	struct net *net =&init_net;
	int err;
	__be32 seq;
	struct xfrm_state *x=NULL;
	xfrm_address_t *daddr;
	struct xfrm_mode *inner_mode;
	struct ethhdr *new_ethhdr;
	unsigned int family;
	int decaps = 0;
	int mac_len = 0;

	struct xfrm_sa_bundle *sab = NULL;

	IPSEC_DEBUG("input* packet begin to IPsec decap. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);

	daddr = (xfrm_address_t *)(skb_network_header(skb) +
				   XFRM_SPI_SKB_CB(skb)->daddroff);
	family = XFRM_SPI_SKB_CB(skb)->family;

	seq = 0;
	if (!spi && (err = xfrm_parse_spi(skb, nexthdr, &spi, &seq)) != 0) {
		IPSEC_DEBUG_DROP("input* parse spi error:%d. daddr %s  saddr %s, len %d\n",
			err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		XFRM_INC_STATS(net, LINUX_MIB_XFRMINHDRERROR);
		goto drop;
	}

	//Record mac_len for mac rebuild. See below.
	if (skb_mac_header(skb))
		mac_len = skb_network_header(skb) - skb_mac_header(skb);

	do {

		if(x)
			xfrm_state_put(x);

		x = xfrm_state_lookup(net, 0, daddr, spi, nexthdr, family);
		if (x == NULL) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] lookup failed. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINNOSTATES);
			//xfrm_audit_state_notfound(skb, family, spi, seq);
			goto drop;
		}

//		skb->sp->xvec[skb->sp->len++] = x;

		spin_lock(&x->lock);
		if (unlikely(x->state != XFRM_STATE_VALID)) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] state is invalid :%d. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				x->state,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEINVALID);
			goto drop_unlock;
		}

		if ((x->encap ? x->encap->encap_type : 0) != encap_type) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] encap type is mismatched. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEMISMATCH);
			goto drop_unlock;
		}

		if (x->repl->check(x, skb, seq)) {
			IPSEC_DEBUG_DROP("input* seq check failed [sa_spi=%u]. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATESEQERROR);
			goto drop_unlock;
		}

		if (xfrm_state_check_expire(x, NULL)) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] expired. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEEXPIRED);
			goto drop_unlock;
		}

		spin_unlock(&x->lock);

#if 0
		seq_hi = htonl(xfrm_replay_seqhi(x, seq));

		XFRM_SKB_CB(skb)->seq.input.low = seq;
		XFRM_SKB_CB(skb)->seq.input.hi = seq_hi;
#endif
//		skb_dst_force(skb);

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-1. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		/* 根据全局配置选择处理路径 */
#ifdef ENABLE_CRYPTODEV
		switch (xfrm_get_crypto_mode()) {
		case XFRM_CRYPTO_MODE_CRYPTODEV:
			/* 纯cryptodev模式 - x->context存储cryptodev会话 */
			if (xfrm_has_cryptodev_session(x) && cryptodev_enabled) {
				int ret = xfrm_cryptodev_decrypt(x, skb);
				if (ret == 0) {
					/* cryptodev处理成功 */
					goto crypto_success;
				} else if (ret == -EINPROGRESS) {
					/* 异步处理中 */
					xfrm_state_hold(x);
					return 0;
				} else {
					/* cryptodev失败，记录错误但继续软件路径 */
					IPSEC_DEBUG("Cryptodev decrypt failed: %d, falling back to software\n", ret);
					/* 清理cryptodev上下文，准备软件处理 */
					xfrm_cryptodev_cleanup_context(x);
				}
			}
			/* 如果没有cryptodev会话或失败，回退到软件 */
			break;

		case XFRM_CRYPTO_MODE_AUTO:
			/* 自动模式 - 优先尝试cryptodev */
			if (xfrm_has_cryptodev_session(x) && cryptodev_enabled) {
				int ret = xfrm_cryptodev_decrypt(x, skb);
				if (ret == 0) {
					goto crypto_success;
				} else if (ret == -EINPROGRESS) {
					xfrm_state_hold(x);
					return 0;
				}
				/* cryptodev失败，继续软件路径 */
				IPSEC_DEBUG("Cryptodev failed, using software fallback\n");
			}
			break;

		case XFRM_CRYPTO_MODE_SOFTWARE:
		default:
			/* 纯软件模式 - x->context存储ESP上下文 */
			break;
		}
#endif

		nexthdr = x->type->input(x, skb);

crypto_success:
#if 0
		if (nexthdr == -EINPROGRESS)
			return 0;
#endif

		spin_lock(&x->lock);
		if (nexthdr <= 0) {
			IPSEC_DEBUG_DROP("input* next header error:%d. daddr %s  saddr %s, len %d\n",
				nexthdr,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEPROTOERROR);
			goto drop_unlock;
		}

		/* only the first xfrm gets the encap type */
		encap_type = 0;

		x->repl->advance(x, seq);

		x->curlft.bytes += skb->len;
		x->curlft.packets++;

		spin_unlock(&x->lock);

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-2. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		XFRM_MODE_SKB_CB(skb)->protocol = nexthdr;

		inner_mode = x->inner_mode;

#if 0
		if (x->sel.family == AF_UNSPEC) {
			inner_mode = xfrm_ip2inner_mode(x, XFRM_MODE_SKB_CB(skb)->protocol);
			if (inner_mode == NULL)
				goto drop;
		}
#endif

		if (inner_mode->input(x, skb)) {
			IPSEC_DEBUG_DROP("input* inner mode input error. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEMODEERROR);
			goto drop;
		}

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-3. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		if (x->outer_mode->flags & XFRM_MODE_FLAG_TUNNEL) {
			decaps = 1;
			break;
		}

		/*
		 * We need the inner address.  However, we only get here for
		 * transport mode so the outer address is identical.
		 */
		daddr = &x->id.daddr;
		family = x->outer_mode->afinfo->family;

		err = xfrm_parse_spi(skb, nexthdr, &spi, &seq);
		if (err < 0) {
			IPSEC_DEBUG_DROP("input* parse spi2 error:%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINHDRERROR);
			goto drop;
		}
	} while (!err);

	//FIXME:local deliver packet need mac header for eth_type_trans();
	if (mac_len) {
		unsigned char *old_mac = skb_mac_header(skb);
		skb->mac_header = skb->network_header - mac_len;
		memmove(skb_mac_header(skb), old_mac, mac_len);
	}

	//skb->ipsec_done = 1;
	skb_vpn_flag_set(skb,SKB_VPN_FLAG_IPSEC_DECRYPT);
	nf_reset(skb);

#if 0
	/* fix bug NGOS-1941, for bridge */
	if (skb->nf_bridge )
		skb->nf_bridge->mask |= BRNF_NF_BRIDGE_PREROUTING;
#endif

	IPSEC_DEBUG("input* packet IPsec decap finished. daddr %s  saddr %s, len %d, dev %s \n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len, skb->i_dev->dev_name);



	/* 注意：xfrm_state 结构体中没有 sa_type 和 tunn_dev 成员 */
	if (x->props.mode == XFRM_MODE_TUNNEL) {
		/* 使用默认设备 */
		if(skb->i_dev) {
			LDEV_ADD_RXPKTS(skb->i_dev, 1);
			LDEV_ADD_RXOCTS(skb->i_dev, skb->len);

			/* remove other l2 header */
			new_ethhdr = skb->mac_header;
			skb->mac_header = skb->network_header - ETH_HLEN;
			memmove(skb_mac_header(skb), new_ethhdr, ETH_HLEN);

			/*本地报文会在内核检查mac头和入接口mac是否一致*/
			new_ethhdr = skb->mac_header;
			dp_memcpy(new_ethhdr->h_dest, skb->i_dev->dev_addr, ETH_ALEN);
			new_ethhdr->h_proto = skb->protocol;
		} else {
			IPSEC_DEBUG_DROP("input* packet IPsec sa tunnel-dev is NULL. daddr %s  saddr %s, len %d\n",
					ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			goto drop;
		}
	}

	//add by guoxd for ipsec tunnel check
	if(ip_hdr(skb)->saddr == 0 && ip_hdr(skb)->daddr == 0){
		sab = xfrm_tunnel_sab_find_by_state(x);
		if (sab){
			IPSEC_DEBUG("input* ipsec tunnel check rcv packet begin . daddr %s  saddr %s, len %d, skb->i_dev: %s \n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len, skb->i_dev->dev_name);
			dp_ipsec_check_rcv(skb, sab);
		}
		goto drop;
	}

	if (decaps) {
//		skb_dst_drop(skb);
//		netif_rx(skb);
		xfrm_state_put(x);
		ip_rcv_fast(skb);
		return 0;
	} else {
		err = x->inner_mode->afinfo->transport_finish(skb, 0);
		xfrm_state_put(x);
		return err;
	}

drop_unlock:
	spin_unlock(&x->lock);
drop:
	if (x)
		xfrm_state_put(x);
	kfree_skb(skb);
	return 0;
}

/* 处理 cryptodev 完成的解密数据包 */
void xfrm_input_crypto_done(struct sk_buff *skb, int err)
{
	struct xfrm_cryptodev_metadata *meta;
	struct xfrm_state *x;
	int nexthdr;
	struct net *net = &init_net;

	/* 获取元数据 */
	meta = (struct xfrm_cryptodev_metadata *)skb->cb;
	x = meta->x;

	/* 如果操作失败，释放资源并返回 */
	if (err) {
		IPSEC_DEBUG_DROP("Cryptodev decrypt failed: %d\n", err);
		xfrm_state_put(x);
		kfree_skb(skb);
		return;
	}

	/* 继续处理解密后的数据包 */
	IPSEC_DEBUG("Cryptodev decrypt completed successfully\n");

	/* 继续处理数据包，类似于 xfrm_input 中的处理 */
	nexthdr = XFRM_MODE_SKB_CB(skb)->protocol;

	spin_lock(&x->lock);
	if (nexthdr <= 0) {
		IPSEC_DEBUG_DROP("input* next header error:%d\n", nexthdr);
		XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEPROTOERROR);
		goto drop_unlock;
	}

	x->repl->advance(x, meta->seq);

	x->curlft.bytes += skb->len;
	x->curlft.packets++;

	spin_unlock(&x->lock);

	/* 继续处理内部模式 */
	if (x->inner_mode->input(x, skb)) {
		IPSEC_DEBUG_DROP("input* inner mode input error\n");
		XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEMODEERROR);
		goto drop;
	}

	/* 检查是否为隧道模式 */
	if (x->outer_mode->flags & XFRM_MODE_FLAG_TUNNEL) {
		/* 隧道模式处理 */
		skb_vpn_flag_set(skb, SKB_VPN_FLAG_IPSEC_DECRYPT);
		nf_reset(skb);

		/* 处理隧道设备 - 注意：xfrm_state 结构体中没有 sa_type 和 tunn_dev 成员 */
		if (x->props.mode == XFRM_MODE_TUNNEL) {
			/* 使用默认设备 */
			if (skb->i_dev) {
				LDEV_ADD_RXPKTS(skb->i_dev, 1);
				LDEV_ADD_RXOCTS(skb->i_dev, skb->len);

				/* 处理以太网头 */
				unsigned char *mac_ptr;
				struct ethhdr *eth_ptr;
				if (skb_mac_header(skb)) {
					/* 保存 MAC 头长度和指针 */
					mac_ptr = skb_mac_header(skb);
					skb->mac_header = skb->network_header - ETH_HLEN;
					memmove(skb_mac_header(skb), mac_ptr, ETH_HLEN);
				}

				/* 设置目的 MAC 地址 */
				eth_ptr = (struct ethhdr *)skb_mac_header(skb);
				dp_memcpy(eth_ptr->h_dest, skb->i_dev->dev_addr, ETH_ALEN);
				eth_ptr->h_proto = skb->protocol;
			} else {
				IPSEC_DEBUG_DROP("input* packet IPsec sa tunnel-dev is NULL\n");
				goto drop;
			}
		}

		/* 释放 SA 引用并处理数据包 */
		xfrm_state_put(x);
		ip_rcv_fast(skb);
	} else {
		/* 传输模式处理 */
		x->inner_mode->afinfo->transport_finish(skb, 0);
		xfrm_state_put(x);
		return;
	}

	return;

drop_unlock:
	spin_unlock(&x->lock);
drop:
	xfrm_state_put(x);
	kfree_skb(skb);
}


int xfrm_input6(struct sk_buff *skb, int nexthdr, __be32 spi, int encap_type)
{
	struct net *net =&init_net;
	int err;
	__be32 seq;
	struct xfrm_state *x=NULL;
	xfrm_address_t *daddr;
	struct xfrm_mode *inner_mode;
	struct ethhdr *new_ethhdr;
	unsigned int family;
	int decaps = 0;
	int mac_len = 0;

	IPSEC_DEBUG("input* packet begin to IPsec decap. daddr %s  saddr %s, len %d\n",
			ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);

	daddr = (xfrm_address_t *)(skb_network_header(skb) +
				   XFRM_SPI_SKB_CB(skb)->daddroff);
	family = XFRM_SPI_SKB_CB(skb)->family;

	seq = 0;
	if (!spi && (err = xfrm_parse_spi(skb, nexthdr, &spi, &seq)) != 0) {
		IPSEC_DEBUG_DROP("input* parse spi error:%d. daddr %s  saddr %s, len %d\n",
			err,ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
		XFRM_INC_STATS(net, LINUX_MIB_XFRMINHDRERROR);
		goto drop;
	}

	//Record mac_len for mac rebuild. See below.
	if (skb_mac_header(skb))
		mac_len = skb_network_header(skb) - skb_mac_header(skb);

	do {
		if(x)
			xfrm_state_put(x);

		x = xfrm_state_lookup(net, 0, daddr, spi, nexthdr, family);
		if (x == NULL) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] lookup failed. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINNOSTATES);
			//xfrm_audit_state_notfound(skb, family, spi, seq);
			goto drop;
		}

//		skb->sp->xvec[skb->sp->len++] = x;

		spin_lock(&x->lock);
		if (unlikely(x->state != XFRM_STATE_VALID)) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] state is invalid :%d. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				x->state,ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEINVALID);
			goto drop_unlock;
		}

		if ((x->encap ? x->encap->encap_type : 0) != encap_type) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] encap type is mismatched. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEMISMATCH);
			goto drop_unlock;
		}

		if (x->repl->check(x, skb, seq)) {
			IPSEC_DEBUG_DROP("input* seq check failed [sa_spi=%u]. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATESEQERROR);
			goto drop_unlock;
		}

		if (xfrm_state_check_expire(x, NULL)) {
			IPSEC_DEBUG_DROP("input* sa[spi=%u] expired. daddr %s  saddr %s, len %d\n",
				ntohl(spi),
				ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEEXPIRED);
			goto drop_unlock;
		}

		spin_unlock(&x->lock);

#if 0
		seq_hi = htonl(xfrm_replay_seqhi(x, seq));

		XFRM_SKB_CB(skb)->seq.input.low = seq;
		XFRM_SKB_CB(skb)->seq.input.hi = seq_hi;
#endif
//		skb_dst_force(skb);

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-1. daddr %s  saddr %s, len %d\n",
				ip6_string(&ipv6_hdr(skb)->daddr),ip6_string2(&ipv6_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		/* 检查是否有 cryptodev 会话 */
#ifdef ENABLE_CRYPTODEV
		if (x->context && cryptodev_enabled) {
			/* 使用 cryptodev 进行解密 */
			int ret = xfrm_cryptodev_decrypt(x, skb);
			if (ret == 0) {
				/* 数据包已提交给 cryptodev，将在异步完成 */
				/* 注意：需要保持 x 的引用计数，在异步完成时释放 */
				xfrm_state_hold(x);
				return 0;
			}
			/* 如果 cryptodev 失败，回退到软件路径 */
			IPSEC_DEBUG("Cryptodev decrypt failed, falling back to software: %d\n", ret);
		}
#endif

		nexthdr = x->type->input(x, skb);

#if 0
		if (nexthdr == -EINPROGRESS)
			return 0;
#endif

		spin_lock(&x->lock);
		if (nexthdr <= 0) {
			IPSEC_DEBUG_DROP("input* next header error:%d. daddr %s  saddr %s, len %d\n",
				nexthdr,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEPROTOERROR);
			goto drop_unlock;
		}

		/* only the first xfrm gets the encap type */
		encap_type = 0;

		x->repl->advance(x, seq);

		x->curlft.bytes += skb->len;
		x->curlft.packets++;

		spin_unlock(&x->lock);

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-2. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		XFRM_MODE_SKB_CB(skb)->protocol = nexthdr;

		inner_mode = x->inner_mode;

#if 0
		if (x->sel.family == AF_UNSPEC) {
			inner_mode = xfrm_ip2inner_mode(x, XFRM_MODE_SKB_CB(skb)->protocol);
			if (inner_mode == NULL)
				goto drop;
		}
#endif

		if (inner_mode->input(x, skb)) {
			IPSEC_DEBUG_DROP("input* inner mode input error. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINSTATEMODEERROR);
			goto drop;
		}

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("input* dump-3. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		if (x->outer_mode->flags & XFRM_MODE_FLAG_TUNNEL) {
			decaps = 1;
			break;
		}

		/*
		 * We need the inner address.  However, we only get here for
		 * transport mode so the outer address is identical.
		 */
		daddr = &x->id.daddr;
		family = x->outer_mode->afinfo->family;

		err = xfrm_parse_spi(skb, nexthdr, &spi, &seq);
		if (err < 0) {
			IPSEC_DEBUG_DROP("input* parse spi2 error:%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMINHDRERROR);
			goto drop;
		}
	} while (!err);

	//FIXME:local deliver packet need mac header for eth_type_trans();
	if (mac_len) {
		unsigned char *old_mac = skb_mac_header(skb);
		skb->mac_header = skb->network_header - mac_len;
		memmove(skb_mac_header(skb), old_mac, mac_len);
	}

	//skb->ipsec_done = 1;
	skb_vpn_flag_set(skb,SKB_VPN_FLAG_IPSEC_DECRYPT);
	nf_reset(skb);

#if 0
	/* fix bug NGOS-1941, for bridge */
	if (skb->nf_bridge )
		skb->nf_bridge->mask |= BRNF_NF_BRIDGE_PREROUTING;
#endif

	IPSEC_DEBUG("input* packet IPsec decap finished. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);

	/* 注意：xfrm_state 结构体中没有 sa_type 和 tunn_dev 成员 */
	if (x->props.mode == XFRM_MODE_TUNNEL) {
		/* 使用默认设备 */
		if(skb->i_dev) {
			LDEV_ADD_RXPKTS(skb->i_dev, 1);
			LDEV_ADD_RXOCTS(skb->i_dev, skb->len);

			/* remove other l2 header */
			new_ethhdr = skb->mac_header;
			skb->mac_header = skb->network_header - ETH_HLEN;
			memmove(skb_mac_header(skb), new_ethhdr, ETH_HLEN);

			/*本地报文会在内核检查mac头和入接口mac是否一致*/
			new_ethhdr = skb->mac_header;
			dp_memcpy(new_ethhdr->h_dest, skb->i_dev->dev_addr, ETH_ALEN);
			new_ethhdr->h_proto = skb->protocol;
		} else {
			IPSEC_DEBUG_DROP("input* packet IPsec sa tunnel-dev is NULL. daddr %s  saddr %s, len %d\n",
					ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			goto drop;
		}
	}

	if (decaps) {
//		skb_dst_drop(skb);
//		netif_rx(skb);
		xfrm_state_put(x);
		ip_rcv_fast(skb);
		return 0;
	} else {
		err = x->inner_mode->afinfo->transport_finish(skb, 0);
		xfrm_state_put(x);
		return err;
	}

drop_unlock:
	spin_unlock(&x->lock);
drop:
	if (x)
		xfrm_state_put(x);
	kfree_skb(skb);
	return 0;
}

