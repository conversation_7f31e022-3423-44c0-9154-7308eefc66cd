#ifndef _NET_XFRM_H
#define _NET_XFRM_H

#include <linux/compiler.h>
#include <linux/xfrm.h>
#include <linux/spinlock.h>
#include <list.h>
#include <linux/skbuff.h>
//#include <linux/in6.h>
#include <linux/slab.h>

#include <net/ip.h>
#include <net/route.h>
//#include <net/ipv6.h>
//#include <net/ip6_fib.h>
#include <net/flow.h>

#include <linux/interrupt.h>
#include "flow_common.h"
#include <net/ip_policy.h>
#include <net/xfrm_algo.h>

#include <linux/ipsec.h>

#ifndef NPROTO
#define NPROTO		40
#endif
#define DP_CRYPTO_CARD_DISABLE		0
#define DP_CRYPTO_CARD_ENABLE		1

#define XFRM_IKE_PORT	500
#define XFRM_IKE_UDP_ENCAP_PORT	4500
struct dp_ipsec_isakmp_port_msg;
extern struct dp_ipsec_isakmp_port_msg g_isakmp_port;

#define XFRM_PROTO_ESP		50
#define XFRM_PROTO_AH		51
#define XFRM_PROTO_COMP		108
#define XFRM_PROTO_IPIP		4
#define XFRM_PROTO_IPV6		41
#define XFRM_PROTO_ROUTING	IPPROTO_ROUTING
#define XFRM_PROTO_DSTOPTS	IPPROTO_DSTOPTS

#define XFRM_ALIGN4(len)	(((len) + 3) & ~3)
#define XFRM_ALIGN8(len)	(((len) + 7) & ~7)
#define MODULE_ALIAS_XFRM_MODE(family, encap) \
	MODULE_ALIAS("xfrm-mode-" __stringify(family) "-" __stringify(encap))
#define MODULE_ALIAS_XFRM_TYPE(family, proto) \
	MODULE_ALIAS("xfrm-type-" __stringify(family) "-" __stringify(proto))

#ifdef CONFIG_XFRM_STATISTICS
#define XFRM_INC_STATS(net, field)	SNMP_INC_STATS((net)->mib.xfrm_statistics, field)
#define XFRM_INC_STATS_BH(net, field)	SNMP_INC_STATS_BH((net)->mib.xfrm_statistics, field)
#define XFRM_INC_STATS_USER(net, field)	SNMP_INC_STATS_USER((net)-mib.xfrm_statistics, field)
#else
#define XFRM_INC_STATS(net, field)	((void)(net))
#define XFRM_INC_STATS_BH(net, field)	((void)(net))
#define XFRM_INC_STATS_USER(net, field)	((void)(net))
#endif


//temporarily use
#ifndef GRE_KEY
#define GRE_KEY		__cpu_to_be16(0x2000)
#endif

#ifndef GRE_CSUM
#define GRE_CSUM	__cpu_to_be16(0x8000)
#endif
//temporarily use end

/* Full description of state of transformer. */
struct xfrm_state {
//#ifdef CONFIG_NET_NS
	struct net		*xs_net;
//#endif
	struct hlist_node	byspi;

	atomic_t		refcnt;
	spinlock_t		lock;

	struct xfrm_id		id;
	struct xfrm_selector	sel;
	u32			genid;

	u16 			state;
	u16			dying;

	/* Parameters of this state. */
	struct {
		u32		reqid;
		u8		mode;
		u8		replay_window;
		u8		aalgo, ealgo, calgo;
		u8		flags;
		u16		family;
		xfrm_address_t	saddr;
		int		header_len;
		int		trailer_len;
	} props;

	struct xfrm_lifetime_cfg lft;

	/* Data for transformer */
	struct xfrm_auth_alg 	*aalg;
	struct xfrm_enc_alg 	*ealg;
	struct xfrm_comp_alg 	*calg;
	u16					auth_key_len;
	u16					enc_key_len;
	void					*auth_key;
	void					*enc_key;
	void					*context;

	/* Data for encapsulator */
	struct xfrm_encap_tmpl	*encap;

	/* Data for care-of address */
	xfrm_address_t	*coaddr;

	/* State for replay detection */
	struct xfrm_replay_state replay;
	/* The functions for replay detection. */
	struct xfrm_replay	*repl;

	/* Statistics */
	struct xfrm_stats	stats;

	struct xfrm_lifetime_cur curlft;
//	struct tasklet_hrtimer	mtimer;

	/* used to fix curlft->add_time when changing date */
	long		saved_tmo;

	/* Last used time */
	unsigned long		lastused;

	/* Reference to data common to all the instances of this
	 * transformer. */
	const struct xfrm_type	*type;
	struct xfrm_mode	*inner_mode;
	struct xfrm_mode	*inner_mode_iaf;
	struct xfrm_mode	*outer_mode;

	/* Security context */
	struct xfrm_sec_ctx	*security;

	int policy_id;
	struct net_device *tunn_dev;
	int sa_type;

	/* Cryptodev 支持字段 */
#ifdef ENABLE_CRYPTODEV
	uint32_t crypto_flags;  /* cryptodev 标志 */
#endif

};

/* Cryptodev 标志定义 */
#ifdef ENABLE_CRYPTODEV
#define XFRM_CRYPTO_FLAG_HW_OFFLOAD    0x0001  /* 使用硬件加速 */
#define XFRM_CRYPTO_FLAG_SW_FALLBACK   0x0002  /* 使用软件回退 */
#define XFRM_CRYPTO_FLAG_ASYNC         0x0004  /* 使用异步处理 */
#endif

static inline struct net *xs_net(struct xfrm_state *x)
{
	return read_pnet(&x->xs_net);
}

/* xflags - make enum if more show up */
#define XFRM_TIME_DEFER	1
#define XFRM_SOFT_EXPIRE 2

enum {
	XFRM_STATE_VOID,
	XFRM_STATE_ACQ,
	XFRM_STATE_VALID,
	XFRM_STATE_ERROR,
	XFRM_STATE_EXPIRED,
	XFRM_STATE_DEAD
};

struct xfrm_replay {
	void	(*advance)(struct xfrm_state *x, __be32 net_seq);
	int	(*check)(struct xfrm_state *x,
			 struct sk_buff *skb,
			 __be32 net_seq);
	int	(*recheck)(struct xfrm_state *x,
			   struct sk_buff *skb,
			   __be32 net_seq);
	void	(*notify)(struct xfrm_state *x, int event);
	int	(*overflow)(struct xfrm_state *x, struct sk_buff *skb);
};

struct net_device;
struct xfrm_type;
struct xfrm_policy_afinfo {
	unsigned short		family;
	int			(*get_saddr)(struct net *net, xfrm_address_t *saddr, xfrm_address_t *daddr);
	void			(*decode_session)(struct sk_buff *skb,
						  struct flowi *fl,
						  int reverse);
	int			(*get_tos)(const struct flowi *fl);
	void			(*adjust_tcp_mss)(struct sk_buff *skb, int new_mss);
};


extern int xfrm_policy_register_afinfo(struct xfrm_policy_afinfo *afinfo);
extern int xfrm_policy_unregister_afinfo(struct xfrm_policy_afinfo *afinfo);

struct xfrm_tmpl;
extern int __xfrm_state_delete(struct xfrm_state *x);

struct xfrm_state_afinfo {
	unsigned int		family;
	unsigned int		proto;
	__be16			eth_proto;
	struct module		*owner;
	const struct xfrm_type	*type_map[IPPROTO_MAX];
	struct xfrm_mode	*mode_map[XFRM_MODE_MAX];
	int			(*init_flags)(struct xfrm_state *x);
	int			(*state_sort)(struct xfrm_state **dst, struct xfrm_state **src, int n);
	int			(*output)(struct sk_buff *skb);
	int			(*extract_input)(struct xfrm_state *x,
						 struct sk_buff *skb);
	int			(*extract_output)(struct xfrm_state *x,
						  struct sk_buff *skb);
	int			(*transport_finish)(struct sk_buff *skb,
						    int async);
};

extern int xfrm_state_register_afinfo(struct xfrm_state_afinfo *afinfo);
extern int xfrm_state_unregister_afinfo(struct xfrm_state_afinfo *afinfo);

struct xfrm_type {
	char			*description;
	struct module		*owner;
	u8			proto;
	u8			flags;
#define XFRM_TYPE_NON_FRAGMENT	1
#define XFRM_TYPE_REPLAY_PROT	2
#define XFRM_TYPE_LOCAL_COADDR	4
#define XFRM_TYPE_REMOTE_COADDR	8

	int			(*init_state)(struct xfrm_state *x);
	void			(*destructor)(struct xfrm_state *);
	int			(*input)(struct xfrm_state *, struct sk_buff *skb);
	int			(*output)(struct xfrm_state *, struct sk_buff *pskb);
	int			(*reject)(struct xfrm_state *, struct sk_buff *,
					  const struct flowi *);
	int			(*hdr_offset)(struct xfrm_state *, struct sk_buff *, u8 **);
	/* Estimate maximal size of result of transformation of a dgram */
	u32			(*get_mtu)(struct xfrm_state *, int size);
};

struct xfrm_mode {
	/*
	 * Remove encapsulation header.
	 *
	 * The IP header will be moved over the top of the encapsulation
	 * header.
	 *
	 * On entry, the transport header shall point to where the IP header
	 * should be and the network header shall be set to where the IP
	 * header currently is.  skb->data shall point to the start of the
	 * payload.
	 */
	int (*input2)(struct xfrm_state *x, struct sk_buff *skb);

	/*
	 * This is the actual input entry point.
	 *
	 * For transport mode and equivalent this would be identical to
	 * input2 (which does not need to be set).  While tunnel mode
	 * and equivalent would set this to the tunnel encapsulation function
	 * xfrm4_prepare_input that would in turn call input2.
	 */
	int (*input)(struct xfrm_state *x, struct sk_buff *skb);

	/*
	 * Add encapsulation header.
	 *
	 * On exit, the transport header will be set to the start of the
	 * encapsulation header to be filled in by x->type->output and
	 * the mac header will be set to the nextheader (protocol for
	 * IPv4) field of the extension header directly preceding the
	 * encapsulation header, or in its absence, that of the top IP
	 * header.  The value of the network header will always point
	 * to the top IP header while skb->data will point to the payload.
	 */
	int (*output2)(struct xfrm_state *x,struct sk_buff *skb);

	/*
	 * This is the actual output entry point.
	 *
	 * For transport mode and equivalent this would be identical to
	 * output2 (which does not need to be set).  While tunnel mode
	 * and equivalent would set this to a tunnel encapsulation function
	 * (xfrm4_prepare_output or xfrm6_prepare_output) that would in turn
	 * call output2.
	 */
	int (*output)(struct xfrm_state *x, struct sk_buff *skb);

	struct xfrm_state_afinfo *afinfo;
	struct module *owner;
	unsigned int encap;
	int flags;
};

/* Flags for xfrm_mode. */
enum {
	XFRM_MODE_FLAG_TUNNEL = 1,
};

extern int xfrm_register_mode(struct xfrm_mode *mode, int family);

static inline int xfrm_af2proto(unsigned int family)
{
	switch(family) {
	case AF_INET:
		return IPPROTO_IPIP;
	case AF_INET6:
		return IPPROTO_IPV6;
	default:
		return 0;
	}
}

static inline struct xfrm_mode *xfrm_ip2inner_mode(struct xfrm_state *x, int ipproto)
{
	if ((ipproto == IPPROTO_IPIP && x->props.family == AF_INET) ||
	    (ipproto == IPPROTO_IPV6 && x->props.family == AF_INET6))
		return x->inner_mode;
	else
		return x->inner_mode_iaf;
}

#define XFRM_MAX_DEPTH		2

struct xfrm_policy_head {
	struct list_head list;
	rwlock_t		lock;
	int			count;
};

enum {
	XFRM_POLICY_TYPE_FWPOLICY = 0,
	XFRM_POLICY_TYPE_TUNNEL,
};

enum {
	XFRM_SA_DIR_INBOUND = 0,
	XFRM_SA_DIR_OUTBOUND,
	XFRM_SA_DIR_MAX,
};
enum {
	XFRM_SA_TYPE_ROUTE = 0,
	XFRM_SA_TYPE_POLICY,
};
struct xfrm_sa_bundle {
	struct list_head 	list;
	struct rcu_head rcu_head;

	rwlock_t		lock;
	atomic_t		refcnt;

	u8			xfrm_nr; //SA numbers
	u8			state; //XFRM_STATE_ACQ or XFRM_STATE_VALID
	u8			head_room;//for space check. 255 is enough? I think so.
	u8			tail_room;//for space check
	u16			policy_id; //policy based valid noly.
	u16			dying; //.
	u32			ph2_id;//IKE ph2 id
	u32			res;//reserved
	struct net_device *tunn_dev;
	u8			sa_type;	//·�ɻ� ����
	u8			ez_mode;
	u8			side;
	struct timer_list 	timer; //the timer for acquire to ipsecd
	u32	subnet_num;
	struct ipsec_ez_pnet *peer_subnet;
	struct xfrm_selector	selector;
	struct xfrm_state	*xvec_out[XFRM_MAX_DEPTH];	//outbound SA bundle
	struct xfrm_state	*xvec_in[XFRM_MAX_DEPTH];	//inbound SA bundle
	u32 last_rate;
	u32 last_delay;
	u32 last_shake;
};


struct xfrm_policy_conf {
	struct list_head 	list;
	struct xfrm_selector	selector;
};

struct xfrm_policy{
	struct list_head 	list;
//#if 0 /*def CONFIG_NET_NS*/
	struct net		*xp_net;
//#endif

	/* This lock only affects elements except for entry. */
	rwlock_t		lock;
	atomic_t		refcnt;
	u8			invalid;
	u8			enable;
	u8			type;//policy based or tunnel based.
	u8			xfrm_nr;
	u16			policy_id; //noly for policy based valid.
	u16			family;
	u16			res;//reserved for aligned
	u32			num_conf;//the number of configure policies
	u32			num_sab;//the number of sa bundles
	int			in_ifindex;
	int			out_ifindex;	
	struct list_head 	list_conf;
	struct list_head 	list_sab;//sa bundle list

	char			phase2_name[OBJ_NAME_LEN];
};

static inline struct net *xp_net(const struct xfrm_policy *xp)
{
	return read_pnet(&xp->xp_net);
}

#define XFRM_KM_TIMEOUT                30
/* which seqno */
#define XFRM_REPLAY_SEQ		1
#define XFRM_REPLAY_OSEQ	2
#define XFRM_REPLAY_SEQ_MASK	3
/* what happened */
#define XFRM_REPLAY_UPDATE	XFRM_AE_CR
#define XFRM_REPLAY_TIMEOUT	XFRM_AE_CE

/* default aevent timeout in units of 100ms */
#define XFRM_AE_ETIME			10
/* Async Event timer multiplier */
#define XFRM_AE_ETH_M			10
/* default seq threshold size */
#define XFRM_AE_SEQT_SIZE		2

/*
 * This structure is used for the duration where packets are being
 * transformed by IPsec.  As soon as the packet leaves IPsec the
 * area beyond the generic IP part may be overwritten.
 */
struct xfrm_skb_cb {
	union {
		struct inet_skb_parm h4;
//		struct inet6_skb_parm h6;
        } header;

        /* Sequence number for replay protection. */
	union {
		struct {
			__u32 low;
			__u32 hi;
		} output;
		struct {
			__be32 low;
			__be32 hi;
		} input;
	} seq;
};

#define XFRM_SKB_CB(__skb) ((struct xfrm_skb_cb *)&((__skb)->cb[0]))

/*
 * This structure is used by the afinfo prepare_input/prepare_output functions
 * to transmit header information to the mode input/output functions.
 */
struct xfrm_mode_skb_cb {
	union {
		struct inet_skb_parm h4;
//		struct inet6_skb_parm h6;
	} header;

	/* Copied from header for IPv4, always set to zero and DF for IPv6. */
	__be16 id;
	__be16 frag_off;

	/* IP header length (excluding options or extension headers). */
	u8 ihl;

	/* TOS for IPv4, class for IPv6. */
	u8 tos;

	/* TTL for IPv4, hop limitfor IPv6. */
	u8 ttl;

	/* Protocol for IPv4, NH for IPv6. */
	u8 protocol;

	/* Option length for IPv4, zero for IPv6. */
	u8 optlen;

	/* Used by IPv6 only, zero for IPv4. */
	u8 flow_lbl[3];
};

#define XFRM_MODE_SKB_CB(__skb) ((struct xfrm_mode_skb_cb *)&((__skb)->cb[0]))

/*
 * This structure is used by the input processing to locate the SPI and
 * related information.
 */
struct xfrm_spi_skb_cb {
	union {
		struct inet_skb_parm h4;
//		struct inet6_skb_parm h6;
	} header;

	unsigned int daddroff;
	unsigned int family;
};

#define XFRM_SPI_SKB_CB(__skb) ((struct xfrm_spi_skb_cb *)&((__skb)->cb[0]))
extern void xfrm_state_destroy(struct xfrm_state *x);
static inline void xfrm_state_put(struct xfrm_state *x)
{
	if (atomic_dec_and_test(&x->refcnt))
		xfrm_state_destroy(x);
}

static inline void xfrm_state_hold(struct xfrm_state *x)
{
	atomic_inc(&x->refcnt);
}

static inline bool addr_match(const void *token1, const void *token2,
			      int prefixlen)
{
	const __be32 *a1 = token1;
	const __be32 *a2 = token2;
	int pdw;
	int pbi;

	pdw = prefixlen >> 5;	  /* num of whole u32 in prefix */
	pbi = prefixlen &  0x1f;  /* num of bits in incomplete u32 in prefix */

	if (pdw)
		if (memcmp(a1, a2, pdw << 2))
			return false;

	if (pbi) {
		__be32 mask;

		mask = htonl((0xffffffff) << (32 - pbi));

		if ((a1[pdw] ^ a2[pdw]) & mask)
			return false;
	}

	return true;
}

static inline bool addr4_match(__be32 a1, __be32 a2, u8 prefixlen)
{
	/* C99 6.5.7 (3): u32 << 32 is undefined behaviour */
	if (prefixlen == 0)
		return true;
	return !((a1 ^ a2) & htonl(0xFFFFFFFFu << (32 - prefixlen)));
}

static __inline__
__be16 xfrm_flowi_sport(const struct flowi *fl, const union flowi_uli *uli)
{
	__be16 port;
	switch(fl->flowi_proto) {
	case IPPROTO_TCP:
	case IPPROTO_UDP:
	case IPPROTO_UDPLITE:
	case IPPROTO_SCTP:
		port = uli->ports.sport;
		break;
	case IPPROTO_ICMP:
	case IPPROTO_ICMPV6:
		port = htons(uli->icmpt.type);
		break;
#if 0	
	case IPPROTO_MH:
		port = htons(uli->mht.type);
		break;
#endif	
	case IPPROTO_GRE:
		port = htons(ntohl(uli->gre_key) >> 16);
		break;
	default:
		port = 0;	/*XXX*/
	}
	return port;
}

static __inline__
__be16 xfrm_flowi_dport(const struct flowi *fl, const union flowi_uli *uli)
{
	__be16 port;
	switch(fl->flowi_proto) {
	case IPPROTO_TCP:
	case IPPROTO_UDP:
	case IPPROTO_UDPLITE:
	case IPPROTO_SCTP:
		port = uli->ports.dport;
		break;
	case IPPROTO_ICMP:
	case IPPROTO_ICMPV6:
		port = htons(uli->icmpt.code);
		break;
	case IPPROTO_GRE:
		port = htons(ntohl(uli->gre_key) & 0xffff);
		break;
	default:
		port = 0;	/*XXX*/
	}
	return port;
}

extern bool xfrm_selector_match(const struct xfrm_selector *sel,
				const struct flowi *fl,
				unsigned short family);


static inline int
xfrm_addr_any(const xfrm_address_t *addr, unsigned short family)
{
	switch (family) {
	case AF_INET:
		return addr->a4 == 0;
#if 0		
	case AF_INET6:
		return ipv6_addr_any((struct in6_addr *)&addr->a6);
#endif		
	}
	return 0;
}

#if 0
static inline int
__xfrm4_state_addr_cmp(const struct xfrm_tmpl *tmpl, const struct xfrm_state *x)
{
	return	(tmpl->saddr.a4 &&
		 tmpl->saddr.a4 != x->props.saddr.a4);
}


static inline int
__xfrm6_state_addr_cmp(const struct xfrm_tmpl *tmpl, const struct xfrm_state *x)
{
	return	(!ipv6_addr_any((struct in6_addr*)&tmpl->saddr) &&
		 ipv6_addr_cmp((struct in6_addr *)&tmpl->saddr, (struct in6_addr*)&x->props.saddr));
}

static inline int
xfrm_state_addr_cmp(const struct xfrm_tmpl *tmpl, const struct xfrm_state *x, unsigned short family)
{
	switch (family) {
	case AF_INET:
		return __xfrm4_state_addr_cmp(tmpl, x);
	case AF_INET6:
//		return __xfrm6_state_addr_cmp(tmpl, x);
	}
	return !0;
}
#endif

extern int __xfrm_decode_session(struct sk_buff *skb, struct flowi *fl,
				 unsigned int family, int reverse);

static inline int xfrm_decode_session(struct sk_buff *skb, struct flowi *fl,
				      unsigned int family)
{
	return __xfrm_decode_session(skb, fl, family, 0);
}

static inline int xfrm_decode_session_reverse(struct sk_buff *skb,
					      struct flowi *fl,
					      unsigned int family)
{
	return __xfrm_decode_session(skb, fl, family, 1);
}

static __inline__
xfrm_address_t *xfrm_flowi_daddr(const struct flowi *fl, unsigned short family)
{
	switch (family){
	case AF_INET:
		return (xfrm_address_t *)&fl->u.ip4.daddr;
#if 0	
	case AF_INET6:
		return (xfrm_address_t *)&fl->u.ip6.daddr;
#endif		
	}
	return NULL;
}

static __inline__
xfrm_address_t *xfrm_flowi_saddr(const struct flowi *fl, unsigned short family)
{
	switch (family){
	case AF_INET:
		return (xfrm_address_t *)&fl->u.ip4.saddr;
#if 0	
	case AF_INET6:
		return (xfrm_address_t *)&fl->u.ip6.saddr;
#endif		
	}
	return NULL;
}

static __inline__
void xfrm_flowi_addr_get(const struct flowi *fl,
			 xfrm_address_t *saddr, xfrm_address_t *daddr,
			 unsigned short family)
{
	switch(family) {
	case AF_INET:
		dp_memcpy(&saddr->a4, &fl->u.ip4.saddr, sizeof(saddr->a4));
		dp_memcpy(&daddr->a4, &fl->u.ip4.daddr, sizeof(daddr->a4));
		break;
#if 0	
	case AF_INET6:
		*(struct in6_addr *)saddr->a6 = fl->u.ip6.saddr;
		*(struct in6_addr *)daddr->a6 = fl->u.ip6.daddr;
		break;
#endif		
	}
}

static __inline__ int
__xfrm4_state_addr_check(const struct xfrm_state *x,
			 const xfrm_address_t *daddr, const xfrm_address_t *saddr)
{
	if (daddr->a4 == x->id.daddr.a4 &&
	    (saddr->a4 == x->props.saddr.a4 || !saddr->a4 || !x->props.saddr.a4))
		return 1;
	return 0;
}

#if 0
static __inline__ int
__xfrm6_state_addr_check(const struct xfrm_state *x,
			 const xfrm_address_t *daddr, const xfrm_address_t *saddr)
{
	if (!ipv6_addr_cmp((struct in6_addr *)daddr, (struct in6_addr *)&x->id.daddr) &&
	    (!ipv6_addr_cmp((struct in6_addr *)saddr, (struct in6_addr *)&x->props.saddr)|| 
	     ipv6_addr_any((struct in6_addr *)saddr) || 
	     ipv6_addr_any((struct in6_addr *)&x->props.saddr)))
		return 1;
	return 0;
}
#endif

static __inline__ int
xfrm_state_addr_check(const struct xfrm_state *x,
		      const xfrm_address_t *daddr, const xfrm_address_t *saddr,
		      unsigned short family)
{
	switch (family) {
	case AF_INET:
		return __xfrm4_state_addr_check(x, daddr, saddr);
#if 0	
	case AF_INET6:
		return __xfrm6_state_addr_check(x, daddr, saddr);
#endif		
	}
	return 0;
}

static __inline__ int
xfrm_state_addr_flow_check(const struct xfrm_state *x, const struct flowi *fl,
			   unsigned short family)
{
	switch (family) {
	case AF_INET:
		return __xfrm4_state_addr_check(x,
						(const xfrm_address_t *)&fl->u.ip4.daddr,
						(const xfrm_address_t *)&fl->u.ip4.saddr);
#if 0	
	case AF_INET6:
		return __xfrm6_state_addr_check(x,
						(const xfrm_address_t *)&fl->u.ip6.daddr,
						(const xfrm_address_t *)&fl->u.ip6.saddr);
#endif		
	}
	return 0;
}

static inline int xfrm_id_proto_match(u8 proto, u8 userproto)
{
	return (!userproto || proto == userproto ||
		(userproto == IPSEC_PROTO_ANY && (proto == IPPROTO_AH ||
						  proto == IPPROTO_ESP ||
						  proto == IPPROTO_COMP)));
}

int xfrm4_rcv_encap(struct sk_buff *skb, int nexthdr, __be32 spi,
		    int encap_type);


static inline int xfrm4_rcv_spi(struct sk_buff *skb, int nexthdr, __be32 spi)
{
	return xfrm4_rcv_encap(skb, nexthdr, spi, 0);
}

extern void xfrm_init(void);
extern void xfrm4_init(void);
extern int xfrm_state_init(struct net *net);
extern void xfrm_state_fini(struct net *net);
extern void xfrm4_state_init(void);
extern int xfrm_register_type(const struct xfrm_type *type, unsigned short family);

extern struct xfrm_sa_bundle *xfrm_sab_find_by_state(struct xfrm_state *x);
extern int xfrm_state_check_expire(struct xfrm_state *x, struct xfrm_sa_bundle *sab);
extern struct xfrm_state *xfrm_state_alloc(struct net *net);
extern void xfrm_state_insert(struct xfrm_state *x);
extern int xfrm_state_add(struct xfrm_state *x);
extern struct xfrm_state *xfrm_state_lookup(struct net *net, u32 mark,
					    const xfrm_address_t *daddr, __be32 spi,
					    u8 proto, unsigned short family);
int xfrm_input(struct sk_buff *skb, int nexthdr, __be32 spi, int encap_type);
int xfrm_input6(struct sk_buff *skb, int nexthdr, __be32 spi, int encap_type);
int xfrm_output(struct sk_buff *skb);
int xfrm_prepare_input(struct xfrm_state *x, struct sk_buff *skb);
int xfrm_inner_extract_output(struct xfrm_state *x, struct sk_buff *skb);
extern int xfrm4_extract_input(struct xfrm_state *x, struct sk_buff *skb);
extern int xfrm4_extract_output(struct xfrm_state *x, struct sk_buff *skb);
extern int xfrm4_prepare_output(struct xfrm_state *x, struct sk_buff *skb);
extern int xfrm4_output(struct sk_buff *skb);
int xfrm4_transport_finish(struct sk_buff *skb, int async);
extern int xfrm_adjust_tcp_mss(struct sk_buff *skb);

extern struct xfrm_sa_bundle *xfrm_sab_create(struct xfrm_state *x_out, struct xfrm_state *x_in, u32 ph2_id);
extern struct xfrm_sa_bundle *xfrm_tunnel_sab_create(struct xfrm_state *x_out, struct xfrm_state *x_in, char *tunnel_ifname, u32 ph2_id);

enum xfrm_fwpolicy_event {
	FW_POLICY_ADD = 0,
	FW_POLICY_DELETE,
	FW_POLICY_UPDATE
};

#define XFRM_SA_ACQ_TIMEOUT	15*HZ	//15 seconds

extern FLOW_SHARED struct xfrm_policy_head *xfrm_policy_list_head;


struct policy_entry;

extern int xfrm_fwpolicy_process(struct policy_entry *fw_policy, enum xfrm_fwpolicy_event event);
extern struct xfrm_sa_bundle *xfrm_lookup(struct sk_buff *skb, u16 family, int policy_id);
extern void xfrm_sa_acquire2ipsecd(struct xfrm_sa_bundle *sab, struct xfrm_policy *pol);
extern void xfrm_sa_flush2ipsecd(int in_ifindex, int out_ifindex);
extern void xfrm_sab_hold(struct xfrm_sa_bundle *sab);
extern void xfrm_policy_show_all(void);
extern void xfrm_policy_del2ipsecd(int pol_id);
extern int xfrm_policy_switch_set(struct policy_entry *fw_policy);

extern void xfrm_sab_rcu_destroy(struct xfrm_sa_bundle *sab);
extern int g_dp_crypto_card;

static inline void xfrm_sab_put(struct xfrm_sa_bundle *sab)
{
	if (sab && atomic_dec_and_test(&sab->refcnt)) {
		xfrm_sab_rcu_destroy(sab);
		//xfrm_sab_destroy(sab);
	}
}

static inline int xfrm_addr_cmp(const xfrm_address_t *a,
				const xfrm_address_t *b,
				int family)
{
	switch (family) {
	default:
	case AF_INET:
		return (__force u32)a->a4 - (__force u32)b->a4;
#if 1
	case AF_INET6:
		return memcmp(a, b, sizeof(struct in6_addr));
#endif		
	}
}

static inline int xfrm_policy_id2dir(u32 index)
{
	return index & 7;
}

extern void xfrm_pkt_dump(struct sk_buff *skb);
#define IPSEC_DEBUG(fmt, args...)	dp_debug(dp_debug_ipsec, "IPsec: "fmt, ##args)
#define IPSEC_DEBUG_DROP(fmt, args...)	dp_debug(dp_debug_ipsec|dp_debug_drop, "IPsec: "fmt, ##args)

#define ipsec_tmp_syslog(fmt, args...)	 	ngfw_syslog(MODULE_SYSTEM_STATE, LOG_CRIT, fmt, ##args);	/* Raise  IPsec log level to avoid overwriting by other log, e.g link-up/down log */	\

#define DP_IPSEC_TEMP_DEBUG(fmt,args...)		dp_debug(dp_debug_ipsec, "IPsec: "fmt, ##args);  // ipsec_tmp_syslog("IPSEC-TEMP-DEBUG:"fmt,##args)

#define IPSEC_DUMP_MSG(format, arg...)	\
	do { printf_console("msg: " format, ##arg); } while (0)

extern struct xfrm_sa_bundle *xfrm_sab_find_by_ph2_id(struct xfrm_policy *policy, u32 ph2_id);
extern struct xfrm_policy *xfrm_policy_find_by_id(int policy_id);
extern struct xfrm_sa_bundle *xfrm_tunnel_sab_find_by_tunnif_ph2_id(char *tunnel_ifname, u32 ph2_id);
extern struct xfrm_sa_bundle *xfrm_tunnel_sab_find_by_state(struct xfrm_state *x);
extern void xfrm_tunnel_sab_expired(struct xfrm_sa_bundle *sab);

#endif	/* _NET_XFRM_H */
