# dplane cryptodev 测试验证建议

## 📋 测试概述

**目标**: 验证重构后的cryptodev集成功能正确性、性能和稳定性  
**范围**: 编译测试、功能测试、性能测试、压力测试  
**优先级**: 高 - 必须在生产环境使用前完成  

## 🔧 编译测试

### 1. 基础编译测试
```bash
# 清理构建
make clean

# 编译dplane模块
cd dplane
make

# 检查编译警告和错误
make 2>&1 | grep -E "(warning|error)"
```

### 2. 依赖检查
```bash
# 检查DPDK库依赖
ldd dplane/dpdk/dpdk.o | grep rte

# 检查cryptodev符号
nm dplane/net/xfrm/xfrm.o | grep cryptodev
```

### 3. 静态分析
```bash
# 使用cppcheck进行静态分析
cppcheck --enable=all dplane/net/xfrm/xfrm_cryptodev*.c

# 检查内存泄漏风险
valgrind --tool=memcheck --leak-check=full ./test_program
```

## ⚙️ 功能测试

### 1. 初始化测试
```c
// 测试统一初始化
int test_unified_init() {
    int ret = unified_cryptodev_init();
    assert(ret == 0);
    
    // 验证内存池创建
    assert(unified_get_session_pool() != NULL);
    assert(unified_get_op_pool() != NULL);
    
    // 验证设备状态
    assert(rte_cryptodev_count() > 0);
    
    return 0;
}
```

### 2. 零拷贝转换测试
```c
// 测试skb-mbuf零拷贝转换
int test_zerocopy_conversion() {
    struct sk_buff *skb = alloc_test_skb(1500);
    
    // 测试mbuf准备
    struct rte_mbuf *m = xfrm_prepare_mbuf_for_crypto(skb);
    assert(m != NULL);
    assert(m == (struct rte_mbuf *)skb->work);  // 验证零拷贝
    
    // 测试skb恢复
    int ret = xfrm_restore_skb_from_crypto(skb, m);
    assert(ret == 0);
    
    free_test_skb(skb);
    return 0;
}
```

### 3. 加密解密测试
```c
// 测试ESP加密解密
int test_esp_crypto() {
    struct xfrm_state *x = create_test_sa();
    struct sk_buff *skb = create_test_packet();
    
    // 测试加密
    int ret = xfrm_cryptodev_encrypt(x, skb);
    assert(ret == -EINPROGRESS);  // 异步处理
    
    // 模拟轮询完成
    simulate_crypto_completion();
    
    // 验证加密结果
    verify_encrypted_packet(skb);
    
    // 测试解密
    ret = xfrm_cryptodev_decrypt(x, skb);
    assert(ret == -EINPROGRESS);
    
    simulate_crypto_completion();
    verify_decrypted_packet(skb);
    
    cleanup_test_sa(x);
    free_test_skb(skb);
    return 0;
}
```

### 4. 批量处理测试
```c
// 测试批量加密
int test_batch_processing() {
    struct xfrm_state *states[32];
    struct sk_buff *skbs[32];
    
    // 准备测试数据
    for (int i = 0; i < 32; i++) {
        states[i] = create_test_sa();
        skbs[i] = create_test_packet();
    }
    
    // 测试批量加密
    int processed = xfrm_cryptodev_encrypt_batch(states, skbs, 32);
    assert(processed == 32);
    
    // 验证所有数据包都被处理
    for (int i = 0; i < 32; i++) {
        verify_packet_processed(skbs[i]);
    }
    
    cleanup_test_data(states, skbs, 32);
    return 0;
}
```

### 5. 错误处理测试
```c
// 测试错误处理机制
int test_error_handling() {
    struct xfrm_state *x = create_test_sa();
    struct sk_buff *skb = create_test_packet();
    
    // 模拟设备忙错误
    simulate_device_busy();
    int ret = xfrm_cryptodev_handle_error(x, skb, -EBUSY);
    assert(ret == -EAGAIN);
    
    // 模拟内存不足错误
    simulate_out_of_memory();
    ret = xfrm_cryptodev_handle_error(x, skb, -ENOMEM);
    assert(ret == -ENOTSUP);
    assert(x->crypto_flags & XFRM_CRYPTO_FLAG_SW_FALLBACK);
    
    cleanup_test_sa(x);
    free_test_skb(skb);
    return 0;
}
```

## 📊 性能测试

### 1. 吞吐量测试
```bash
#!/bin/bash
# 测试不同数据包大小的吞吐量

for size in 64 128 256 512 1024 1500; do
    echo "Testing packet size: $size bytes"
    ./throughput_test --packet-size=$size --duration=60
done
```

### 2. 延迟测试
```bash
#!/bin/bash
# 测试加密解密延迟

./latency_test --packet-count=10000 --packet-size=1500
```

### 3. 批量处理性能测试
```c
// 测试批量处理性能提升
int test_batch_performance() {
    struct timespec start, end;
    
    // 测试单个处理性能
    clock_gettime(CLOCK_MONOTONIC, &start);
    for (int i = 0; i < 1000; i++) {
        xfrm_cryptodev_encrypt(test_sa, test_skbs[i]);
    }
    clock_gettime(CLOCK_MONOTONIC, &end);
    long single_time = timespec_diff_ns(&start, &end);
    
    // 测试批量处理性能
    clock_gettime(CLOCK_MONOTONIC, &start);
    for (int i = 0; i < 1000; i += 32) {
        xfrm_cryptodev_encrypt_batch(&test_sas[i], &test_skbs[i], 32);
    }
    clock_gettime(CLOCK_MONOTONIC, &end);
    long batch_time = timespec_diff_ns(&start, &end);
    
    // 验证性能提升
    float improvement = (float)single_time / batch_time;
    printf("Batch processing improvement: %.2fx\n", improvement);
    assert(improvement > 1.5);  // 至少50%提升
    
    return 0;
}
```

## 🔄 压力测试

### 1. 长时间稳定性测试
```bash
#!/bin/bash
# 24小时稳定性测试

echo "Starting 24-hour stability test..."
timeout 86400 ./stress_test --continuous --log-interval=3600

# 检查内存泄漏
echo "Checking for memory leaks..."
cat /proc/meminfo | grep -E "(MemFree|MemAvailable)"
```

### 2. 高并发测试
```c
// 多线程并发测试
int test_concurrent_processing() {
    pthread_t threads[16];
    
    // 启动16个并发线程
    for (int i = 0; i < 16; i++) {
        pthread_create(&threads[i], NULL, crypto_worker_thread, &thread_data[i]);
    }
    
    // 等待所有线程完成
    for (int i = 0; i < 16; i++) {
        pthread_join(threads[i], NULL);
    }
    
    // 验证结果一致性
    verify_concurrent_results();
    
    return 0;
}
```

### 3. 错误恢复测试
```c
// 测试错误恢复机制
int test_error_recovery() {
    // 模拟连续错误
    for (int i = 0; i < 15; i++) {
        simulate_crypto_error();
        process_test_packet();
    }
    
    // 验证软件回退激活
    assert(crypto_stats.sw_fallback > 0);
    
    // 模拟错误恢复
    simulate_error_recovery();
    
    // 验证cryptodev重新激活
    process_test_packet();
    assert(last_packet_used_cryptodev());
    
    return 0;
}
```

## 📈 性能基准

### 预期性能指标
| 指标 | 目标值 | 测试方法 |
|------|--------|----------|
| 吞吐量 (64B) | >1M pps | iperf3 + 小包测试 |
| 吞吐量 (1500B) | >100K pps | iperf3 + 大包测试 |
| 延迟 | <100μs | 时间戳测量 |
| 批量效率 | >50% 提升 | 批量vs单个对比 |
| 内存使用 | <10% 增长 | 内存监控 |
| CPU使用 | <5% 增长 | CPU监控 |

### 性能回归检测
```bash
#!/bin/bash
# 性能回归检测脚本

# 运行基准测试
./benchmark_test > current_results.txt

# 与基准对比
python3 compare_performance.py baseline.txt current_results.txt

# 如果性能下降超过5%，报告回归
if [ $? -ne 0 ]; then
    echo "Performance regression detected!"
    exit 1
fi
```

## 🐛 调试和诊断

### 1. 调试工具
```bash
# 启用详细调试日志
echo 1 > /proc/sys/net/xfrm/cryptodev_debug

# 查看cryptodev统计
cat /proc/net/xfrm/cryptodev_stats

# 监控队列状态
watch -n 1 'cat /proc/net/xfrm/cryptodev_queues'
```

### 2. 常见问题诊断
```c
// 诊断函数示例
void diagnose_cryptodev_issues() {
    // 检查设备状态
    if (rte_cryptodev_count() == 0) {
        printf("No cryptodev devices available\n");
        return;
    }
    
    // 检查内存池状态
    if (!unified_get_session_pool()) {
        printf("Session pool not initialized\n");
        return;
    }
    
    // 检查队列状态
    for (int i = 0; i < rte_cryptodev_count(); i++) {
        struct rte_cryptodev_stats stats;
        rte_cryptodev_stats_get(i, &stats);
        printf("Device %d: enqueued=%lu, dequeued=%lu, errors=%lu\n",
               i, stats.enqueued_count, stats.dequeued_count, stats.enqueue_err_count);
    }
}
```

## ✅ 测试检查清单

### 编译测试 ☐
- [ ] 无编译错误
- [ ] 无编译警告
- [ ] 依赖库正确链接
- [ ] 静态分析通过

### 功能测试 ☐
- [ ] 初始化测试通过
- [ ] 零拷贝转换正确
- [ ] 加密解密功能正常
- [ ] 批量处理工作正常
- [ ] 错误处理机制有效

### 性能测试 ☐
- [ ] 吞吐量达到目标
- [ ] 延迟在可接受范围
- [ ] 批量处理有明显提升
- [ ] 内存使用合理
- [ ] CPU使用合理

### 压力测试 ☐
- [ ] 长时间稳定运行
- [ ] 高并发处理正常
- [ ] 错误恢复机制有效
- [ ] 无内存泄漏
- [ ] 无死锁或竞态条件

---

**建议测试顺序**: 编译测试 → 功能测试 → 性能测试 → 压力测试  
**预计测试时间**: 3-5天  
**测试完成标准**: 所有检查项通过，性能指标达标  
