1. 新增文件
1.1 dplane/net/xfrm/xfrm_cryptodev.h
目的：定义 cryptodev 集成所需的数据结构和函数接口

内容：
定义 xfrm_cryptodev_session 结构
定义 xfrm_cryptodev_ctx 全局上下文结构
定义 xfrm_cryptodev_qp 队列对结构
声明初始化、会话管理和数据包处理函数
定义算法映射结构和常量
定义错误码和调试宏

1.2 dplane/net/xfrm/xfrm_cryptodev.c
目的：实现 cryptodev 初始化和全局管理功能
内容：
实现 xfrm_cryptodev_init() 函数初始化 cryptodev 框架
实现设备发现和配置
实现会话池和操作池创建
实现算法映射表初始化
实现队列对映射表初始化
实现 cryptodev 资源清理函数

1.3 dplane/net/xfrm/xfrm_cryptodev_session.c
目的：实现 cryptodev 会话管理功能
内容：
实现会话创建函数 xfrm_cryptodev_session_create()
实现会话销毁函数 xfrm_cryptodev_session_destroy()
实现算法参数转换函数 xfrm_to_cryptodev_xforms()
实现设备选择函数 xfrm_cryptodev_select_device()
实现会话更新函数 xfrm_cryptodev_session_update()

1.4 dplane/net/xfrm/xfrm_cryptodev_ops.c
目的：实现 cryptodev 数据包处理功能
内容：
实现加密函数 xfrm_cryptodev_encrypt()
实现解密函数 xfrm_cryptodev_decrypt()
实现操作参数设置函数 xfrm_set_crypto_op_params()
实现批量操作入队函数 xfrm_cryptodev_enqueue_burst()
实现批量操作出队函数 xfrm_cryptodev_dequeue_burst()
实现完成处理函数 xfrm_cryptodev_process_completed()
实现 skb 和 mbuf 转换函数

2. 修改现有文件
2.1 dplane/net/xfrm/xfrm_state.h
修改点：扩展 xfrm_state 结构以支持 cryptodev
修改内容：
struct xfrm_state {
    /* 现有字段 */
    ...
    
    /* 添加 cryptodev 会话指针 */
    struct xfrm_cryptodev_session *crypto_session;
    
    /* 添加 cryptodev 标志 */
    uint32_t crypto_flags;
};

/* 添加 cryptodev 标志定义 */
#define XFRM_CRYPTO_FLAG_HW_OFFLOAD    0x0001  /* 使用硬件加速 */
#define XFRM_CRYPTO_FLAG_SW_FALLBACK   0x0002  /* 使用软件回退 */
#define XFRM_CRYPTO_FLAG_ASYNC         0x0004  /* 使用异步处理 */

2.2 dplane/net/xfrm/xfrm_state.c
修改点 1：修改 xfrm_state_alloc() 函数初始化 cryptodev 字段
修改内容：
struct xfrm_state *xfrm_state_alloc(struct net *net)
{
    struct xfrm_state *x;
    
    /* 现有代码 */
    ...
    
    /* 初始化 cryptodev 字段 */
    x->crypto_session = NULL;
    x->crypto_flags = 0;
    
    return x;
}

修改点 2：修改 xfrm_init_state() 函数创建 cryptodev 会话
修改内容：
int xfrm_init_state(struct xfrm_state *x)
{
    int err;
    
    /* 现有代码 */
    err = __xfrm_init_state(x, true);
    if (err)
        return err;
    
    /* 如果启用了 cryptodev，创建 cryptodev 会话 */
    if (cryptodev_enabled) {
        err = xfrm_cryptodev_session_create(x);
        if (err) {
            /* 记录错误但继续使用软件加密 */
            IPSEC_DEBUG("Failed to create cryptodev session for SA: %d\n", err);
            x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        }
    }
    
    return 0;
}

修改点 3：修改 __xfrm_state_delete() 函数销毁 cryptodev 会话
修改内容：
int __xfrm_state_delete(struct xfrm_state *x)
{
    int err = 0;
    
    /* 如果有 cryptodev 会话，销毁它 */
    if (x->crypto_session) {
        xfrm_cryptodev_session_destroy(x);
        x->crypto_session = NULL;
    }
    
    /* 现有代码 */
    ...
    
    return err;
}

2.3 dplane/net/xfrm/xfrm_input.c
修改点 1：修改 xfrm_input() 函数使用 cryptodev 进行解密
修改内容：
int xfrm_input(struct sk_buff *skb, int nexthdr, __be32 spi, int encap_type)
{
    /* 现有代码查找 SA */
    ...
    
    /* 找到 SA 后，检查是否有 cryptodev 会话 */
    if (x->crypto_session && !(x->crypto_flags & XFRM_CRYPTO_FLAG_SW_FALLBACK)) {
        /* 使用 cryptodev 进行解密 */
        ret = xfrm_cryptodev_decrypt(x, skb);
        if (ret == 0) {
            /* 数据包已提交给 cryptodev，将在异步完成 */
            /* 注意：需要实现异步完成处理机制 */
            return 0;
        }
        /* 如果 cryptodev 失败，回退到软件路径 */
        IPSEC_DEBUG("Cryptodev decrypt failed, falling back to software: %d\n", ret);
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
    }
    
    /* 现有软件解密代码 */
    ...
}

修改点 2：添加异步完成处理函数
修改内容：
/* 在文件末尾添加 */
void xfrm_input_crypto_done(struct sk_buff *skb, int err)
{
    /* 处理 cryptodev 完成的数据包 */
    if (err) {
        IPSEC_DEBUG_DROP("Cryptodev decrypt failed: %d\n", err);
        kfree_skb(skb);
        return;
    }
    
    /* 继续处理解密后的数据包 */
    /* 注意：需要保存足够的上下文信息以继续处理 */
    ...
}

2.4 dplane/net/xfrm/xfrm_output.c
修改点 1：修改 xfrm_output() 函数使用 cryptodev 进行加密
修改内容：
int xfrm_output(struct sk_buff *skb)
{
    /* 现有代码查找 SA */
    ...
    
    /* 找到 SA 后，检查是否有 cryptodev 会话 */
    if (x->crypto_session && !(x->crypto_flags & XFRM_CRYPTO_FLAG_SW_FALLBACK)) {
        /* 使用 cryptodev 进行加密 */
        ret = xfrm_cryptodev_encrypt(x, skb);
        if (ret == 0) {
            /* 数据包已提交给 cryptodev，将在异步完成 */
            /* 注意：需要实现异步完成处理机制 */
            return 0;
        }
        /* 如果 cryptodev 失败，回退到软件路径 */
        IPSEC_DEBUG("Cryptodev encrypt failed, falling back to software: %d\n", ret);
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
    }
    
    /* 现有软件加密代码 */
    ...
}

修改点 2：添加异步完成处理函数
修改内容：
/* 在文件末尾添加 */
void xfrm_output_crypto_done(struct sk_buff *skb, int err)
{
    /* 处理 cryptodev 完成的数据包 */
    if (err) {
        IPSEC_DEBUG_DROP("Cryptodev encrypt failed: %d\n", err);
        kfree_skb(skb);
        return;
    }
    
    /* 继续处理加密后的数据包 */
    /* 注意：需要保存足够的上下文信息以继续处理 */
    ...
}

2.5 dplane/net/xfrm/xfrm_policy.c
修改点：修改 xfrm_init() 函数初始化 cryptodev
修改内容：
void xfrm_init(void)
{
    /* 现有代码 */
    ...
    
    /* 初始化 cryptodev */
    if (cryptodev_enabled) {
        int ret = xfrm_cryptodev_init();
        if (ret) {
            printf_console("Failed to initialize cryptodev: %d\n", ret);
            /* 禁用 cryptodev */
            cryptodev_enabled = 0;
        }
    }
}

2.6 dplane/Makefile
修改点：添加新文件到编译列表
修改内容：
# 在适当的位置添加新文件
SRCS += net/xfrm/xfrm_cryptodev.c
SRCS += net/xfrm/xfrm_cryptodev_session.c
SRCS += net/xfrm/xfrm_cryptodev_ops.c

# 添加 DPDK cryptodev 库依赖
LDFLAGS_PATH += -lrte_cryptodev

2.7 dplane/include/config.h 或类似配置文件
修改点：添加 cryptodev 配置选项
修改内容：
/* Cryptodev 配置 */
#define CRYPTODEV_ENABLED 1  /* 默认启用 cryptodev */
#define CRYPTODEV_QUEUE_SIZE 1024  /* 队列大小 */
#define CRYPTODEV_BURST_SIZE 32  /* 批处理大小 */
#define CRYPTODEV_SESSION_CACHE_SIZE 128  /* 会话缓存大小 */
#define CRYPTODEV_MAX_SESSIONS 4096  /* 最大会话数 */
#define CRYPTODEV_MAX_OPS 8192  /* 最大操作数 */

3. 添加异步处理机制
3.1 创建轮询线程
新文件：dplane/net/xfrm/xfrm_cryptodev_poll.c
目的：实现 cryptodev 轮询线程，处理完成的加密操作
内容：
/* 轮询线程函数 */
void *xfrm_cryptodev_poll_thread(void *arg)
{
    while (!thread_should_stop) {
        /* 轮询所有队列对 */
        for (i = 0; i < xfrm_cryptodev_ctx.nb_qps; i++) {
            struct xfrm_cryptodev_qp *qp = &xfrm_cryptodev_ctx.qp_table[i];
            
            /* 出队完成的操作 */
            nb_ops = xfrm_cryptodev_dequeue_burst(qp);
            
            /* 处理完成的操作 */
            if (nb_ops > 0)
                xfrm_cryptodev_process_completed(qp, nb_ops);
        }
        
        /* 短暂休眠避免 CPU 占用过高 */
        usleep(10);
    }
    
    return NULL;
}

/* 启动轮询线程 */
int xfrm_cryptodev_start_poll_thread(void)
{
    /* 创建轮询线程 */
    ...
}

/* 停止轮询线程 */
void xfrm_cryptodev_stop_poll_thread(void)
{
    /* 停止轮询线程 */
    ...
}

3.2 完成处理函数
在 xfrm_cryptodev_ops.c 中添加：
/* 处理完成的加密操作 */
void xfrm_cryptodev_process_completed(struct xfrm_cryptodev_qp *qp, uint16_t nb_ops)
{
    int i;
    
    for (i = 0; i < nb_ops; i++) {
        struct rte_crypto_op *op = qp->ops_buffer[i];
        struct rte_mbuf *m = op->sym->m_src;
        struct sk_buff *skb;
        int err = 0;
        
        /* 检查操作状态 */
        if (op->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
            err = -EINVAL;
        }
        
        /* 从 mbuf 恢复 skb */
        skb = mbuf_to_skb(m);
        
        /* 获取方向信息（加密或解密） */
        if (op->sym->cipher.data.direction == RTE_CRYPTO_CIPHER_OP_ENCRYPT) {
            /* 加密完成 */
            xfrm_output_crypto_done(skb, err);
        } else {
            /* 解密完成 */
            xfrm_input_crypto_done(skb, err);
        }
        
        /* 释放加密操作 */
        rte_crypto_op_free(op);
    }
}

4. 添加配置和管理接口
4.1 配置接口
新文件：dplane/net/xfrm/xfrm_cryptodev_config.c
目的：实现 cryptodev 配置接口
内容：
/* 设置 cryptodev 启用状态 */
int xfrm_cryptodev_set_enabled(int enabled)
{
    if (enabled && !cryptodev_enabled) {
        /* 启用 cryptodev */
        int ret = xfrm_cryptodev_init();
        if (ret)
            return ret;
        cryptodev_enabled = 1;
    } else if (!enabled && cryptodev_enabled) {
        /* 禁用 cryptodev */
        xfrm_cryptodev_uninit();
        cryptodev_enabled = 0;
    }
    
    return 0;
}

/* 设置 cryptodev 队列大小 */
int xfrm_cryptodev_set_queue_size(int size)
{
    /* 检查参数有效性 */
    if (size < 64 || size > 4096)
        return -EINVAL;
    
    /* 如果 cryptodev 已启用，需要重新初始化 */
    if (cryptodev_enabled) {
        xfrm_cryptodev_uninit();
        cryptodev_queue_size = size;
        return xfrm_cryptodev_init();
    }
    
    cryptodev_queue_size = size;
    return 0;
}

/* 其他配置函数 */
...
4.2 管理接口
新文件：dplane/net/xfrm/xfrm_cryptodev_mgmt.c
目的：实现 cryptodev 管理接口
内容：
/* 显示 cryptodev 状态 */
void xfrm_cryptodev_show_status(void)
{
    int i;
    
    printf("Cryptodev status:\n");
    printf("  Enabled: %s\n", cryptodev_enabled ? "yes" : "no");
    printf("  Number of devices: %d\n", xfrm_cryptodev_ctx.nb_devs);
    printf("  Number of queue pairs: %d\n", xfrm_cryptodev_ctx.nb_qps);
    
    printf("  Devices:\n");
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        struct rte_cryptodev_info info;
        uint8_t dev_id = xfrm_cryptodev_ctx.dev_ids[i];
        
        rte_cryptodev_info_get(dev_id, &info);
        printf("    Device %d: %s\n", dev_id, info.device_name);
        printf("      Driver: %s\n", info.driver_name);
        printf("      Max sessions: %d\n", info.sym.max_nb_sessions);
    }
}

/* 显示 cryptodev 统计信息 */
void xfrm_cryptodev_show_stats(void)
{
    int i;
    
    printf("Cryptodev statistics:\n");
    
    for (i = 0; i < xfrm_cryptodev_ctx.nb_qps; i++) {
        struct xfrm_cryptodev_qp *qp = &xfrm_cryptodev_ctx.qp_table[i];
        struct rte_cryptodev_stats stats;
        
        rte_cryptodev_stats_get(qp->dev_id, &stats);
        
        printf("  Device %d Queue %d:\n", qp->dev_id, qp->qp_id);
        printf("    Enqueued ops: %"PRIu64"\n", stats.enqueued_count);
        printf("    Dequeued ops: %"PRIu64"\n", stats.dequeued_count);
        printf("    Errors: %"PRIu64"\n", stats.error_count);
    }
}

/* 重置 cryptodev 统计信息 */
void xfrm_cryptodev_reset_stats(void)
{
    int i;
    
    for (i = 0; i < xfrm_cryptodev_ctx.nb_qps; i++) {
        struct xfrm_cryptodev_qp *qp = &xfrm_cryptodev_ctx.qp_table[i];
        rte_cryptodev_stats_reset(qp->dev_id);
    }
    
    printf("Cryptodev statistics reset\n");
}

5. 集成到 dplane 主程序
5.1 dplane/main.c 或主初始化函数
修改点：在主程序初始化中添加 cryptodev 初始化
修改内容：
int main(int argc, char **argv)
{
    /* 现有代码 */
    ...
    
    /* 初始化 xfrm */
    xfrm_init();
    
    /* 如果启用了 cryptodev，启动轮询线程 */
    if (cryptodev_enabled) {
        ret = xfrm_cryptodev_start_poll_thread();
        if (ret) {
            printf_console("Failed to start cryptodev poll thread: %d\n", ret);
            /* 继续运行，但禁用 cryptodev */
            xfrm_cryptodev_set_enabled(0);
        }
    }
    
    /* 继续初始化 */
    ...
}

5.2 清理函数
修改点：在主程序退出时添加 cryptodev 清理
修改内容：
void cleanup(void)
{
    /* 现有代码 */
    ...
    
    /* 如果启用了 cryptodev，停止轮询线程并清理资源 */
    if (cryptodev_enabled) {
        xfrm_cryptodev_stop_poll_thread();
        xfrm_cryptodev_uninit();
    }
    
    /* 继续清理 */
    ...
}

6. 数据结构和上下文保存
6.1 扩展 sk_buff 结构或添加元数据
修改点：为了支持异步处理，需要在 sk_buff 中保存额外信息
修改内容：
/* 在 sk_buff 中添加 cryptodev 相关字段，或者使用现有的 cb[] 数组 */

/* 定义 cryptodev 元数据结构 */
struct xfrm_cryptodev_metadata {
    struct xfrm_state *x;        /* 关联的 SA */
    int dir;                     /* 方向（输入/输出） */
    void *orig_data;             /* 原始数据指针（用于恢复） */
    int orig_len;                /* 原始数据长度 */
    uint32_t seq;                /* 序列号（用于重放保护） */
};

/* 在 sk_buff 中保存/获取元数据的辅助函数 */
static inline void skb_set_cryptodev_metadata(struct sk_buff *skb, struct xfrm_cryptodev_metadata *meta)
{
    /* 使用 skb->cb 数组保存元数据 */
    memcpy(skb->cb, meta, sizeof(*meta));
}

static inline struct xfrm_cryptodev_metadata *skb_get_cryptodev_metadata(struct sk_buff *skb)
{
    /* 从 skb->cb 数组获取元数据 */
    return (struct xfrm_cryptodev_metadata *)skb->cb;
}

6.2 mbuf 和 skb 转换
在 xfrm_cryptodev_ops.c 中添加：
/* 将 sk_buff 转换为 rte_mbuf */
struct rte_mbuf *skb_to_mbuf(struct sk_buff *skb)
{
    struct rte_mbuf *m;
    
    /* 分配 mbuf */
    m = rte_pktmbuf_alloc(xfrm_cryptodev_ctx.mbuf_pool);
    if (!m)
        return NULL;
    
    /* 复制数据 */
    char *data = rte_pktmbuf_append(m, skb->len);
    if (!data) {
        rte_pktmbuf_free(m);
        return NULL;
    }
    
    memcpy(data, skb->data, skb->len);
    
    /* 保存 skb 指针，用于后续恢复 */
    /* 可以使用 mbuf 的私有数据区域或扩展区域 */
    *((struct sk_buff **)m->buf_addr) = skb;
    
    return m;
}

/* 将 rte_mbuf 转换回 sk_buff */
struct sk_buff *mbuf_to_skb(struct rte_mbuf *m)
{
    struct sk_buff *skb;
    
    /* 获取原始 skb 指针 */
    skb = *((struct sk_buff **)m->buf_addr);
    
    /* 更新 skb 数据 */
    /* 注意：这里假设 skb 有足够空间容纳处理后的数据 */
    /* 如果数据大小变化，可能需要调整 skb */
    memcpy(skb->data, rte_pktmbuf_mtod(m, void *), m->data_len);
    
    /* 如果数据长度变化，更新 skb 长度 */
    if (skb->len != m->data_len) {
        skb_trim(skb, m->data_len);
    }
    
    /* 释放 mbuf */
    rte_pktmbuf_free(m);
    
    return skb;
}

7. 算法映射实现
7.1 xfrm_cryptodev.c 中添加算法映射表
修改内容：
/* 加密算法映射表 */
static const struct xfrm_algo_map cipher_algo_map[] = {
    { XFRM_ALGO_AES, RTE_CRYPTO_CIPHER_AES_CBC, "AES-CBC" },
    { XFRM_ALGO_3DES, RTE_CRYPTO_CIPHER_3DES_CBC, "3DES-CBC" },
    { XFRM_ALGO_DES, RTE_CRYPTO_CIPHER_DES_CBC, "DES-CBC" },
    { XFRM_ALGO_NULL, RTE_CRYPTO_CIPHER_NULL, "NULL" },
    { XFRM_ALGO_AES_CTR, RTE_CRYPTO_CIPHER_AES_CTR, "AES-CTR" },
    { XFRM_ALGO_AES_GCM, RTE_CRYPTO_CIPHER_AES_GCM, "AES-GCM" },
    { 0, 0, NULL }
};

/* 认证算法映射表 */
static const struct xfrm_algo_map auth_algo_map[] = {
    { XFRM_ALGO_HMAC_MD5, RTE_CRYPTO_AUTH_MD5_HMAC, "HMAC-MD5" },
    { XFRM_ALGO_HMAC_SHA1, RTE_CRYPTO_AUTH_SHA1_HMAC, "HMAC-SHA1" },
    { XFRM_ALGO_HMAC_SHA256, RTE_CRYPTO_AUTH_SHA256_HMAC, "HMAC-SHA256" },
    { XFRM_ALGO_HMAC_SHA384, RTE_CRYPTO_AUTH_SHA384_HMAC, "HMAC-SHA384" },
    { XFRM_ALGO_HMAC_SHA512, RTE_CRYPTO_AUTH_SHA512_HMAC, "HMAC-SHA512" },
    { XFRM_ALGO_NULL, RTE_CRYPTO_AUTH_NULL, "NULL" },
    { 0, 0, NULL }
};

/* 算法映射函数 */
static int xfrm_to_cryptodev_cipher_algo(uint8_t xfrm_algo)
{
    int i;
    
    for (i = 0; cipher_algo_map[i].name != NULL; i++) {
        if (cipher_algo_map[i].xfrm_algo == xfrm_algo)
            return cipher_algo_map[i].cryptodev_algo;
    }
    
    return -1;  /* 不支持的算法 */
}

static int xfrm_to_cryptodev_auth_algo(uint8_t xfrm_algo)
{
    int i;
    
    for (i = 0; auth_algo_map[i].name != NULL; i++) {
        if (auth_algo_map[i].xfrm_algo == xfrm_algo)
            return auth_algo_map[i].cryptodev_algo;
    }
    
    return -1;  /* 不支持的算法 */
}

7.2 算法能力检查
在 xfrm_cryptodev_session.c 中添加：
/* 检查设备是否支持指定算法 */
static int xfrm_cryptodev_check_capabilities(uint8_t dev_id, 
                                            int cipher_algo, 
                                            int auth_algo)
{
    struct rte_cryptodev_info dev_info;
    const struct rte_cryptodev_capabilities *cap;
    int cipher_found = 0, auth_found = 0;
    
    /* 获取设备信息 */
    rte_cryptodev_info_get(dev_id, &dev_info);
    
    /* 检查设备能力 */
    for (cap = dev_info.capabilities; cap->op != RTE_CRYPTO_OP_TYPE_UNDEFINED; cap++) {
        if (cap->op != RTE_CRYPTO_OP_TYPE_SYMMETRIC)
            continue;
            
        if (cap->sym.xform_type == RTE_CRYPTO_SYM_XFORM_CIPHER && 
            cap->sym.cipher.algo == cipher_algo)
            cipher_found = 1;
            
        if (cap->sym.xform_type == RTE_CRYPTO_SYM_XFORM_AUTH && 
            cap->sym.auth.algo == auth_algo)
            auth_found = 1;
    }
    
    return (cipher_found && auth_found) ? 0 : -1;
}

8. 配置和命令行接口
8.1 添加配置选项解析
修改点：在配置解析函数中添加 cryptodev 选项
修改内容：
/* 在配置解析函数中添加 */
int parse_config(const char *config_file)
{
    /* 现有代码 */
    ...
    
    /* 解析 cryptodev 配置 */
    cryptodev_enabled = config_get_bool(config, "cryptodev_enable", 1);
    cryptodev_queue_size = config_get_int(config, "cryptodev_queue_size", 1024);
    cryptodev_burst_size = config_get_int(config, "cryptodev_burst_size", 32);
    cryptodev_session_cache_size = config_get_int(config, "cryptodev_session_cache_size", 128);
    
    /* 继续解析 */
    ...
}

8.2 添加命令行接口
修改点：在命令处理函数中添加 cryptodev 命令
修改内容：
/* 在命令处理函数中添加 */
int handle_command(const char *cmd)
{
    /* 现有代码 */
    ...
    
    /* 处理 cryptodev 命令 */
    if (strcmp(cmd, "show cryptodev status") == 0) {
        xfrm_cryptodev_show_status();
        return 0;
    }
    
    if (strcmp(cmd, "show cryptodev stats") == 0) {
        xfrm_cryptodev_show_stats();
        return 0;
    }
    
    if (strcmp(cmd, "reset cryptodev stats") == 0) {
        xfrm_cryptodev_reset_stats();
        return 0;
    }
    
    if (strncmp(cmd, "set cryptodev enable ", 21) == 0) {
        int enable = atoi(cmd + 21);
        return xfrm_cryptodev_set_enabled(enable);
    }
    
    /* 继续处理其他命令 */
    ...
}

9. 调试和日志
9.1 添加调试宏和日志函数
在 xfrm_cryptodev.h 中添加：
/* 调试宏 */
#define CRYPTODEV_DEBUG 1  /* 设置为 0 禁用调试输出 */

#if CRYPTODEV_DEBUG
#define CRYPTO_DEBUG(fmt, args...) \
    IPSEC_DEBUG("CRYPTO: " fmt, ##args)
#else
#define CRYPTO_DEBUG(fmt, args...)
#endif

#define CRYPTO_ERROR(fmt, args...) \
    IPSEC_DEBUG_DROP("CRYPTO ERROR: " fmt, ##args)
9.2 添加性能统计
在 xfrm_cryptodev.c 中添加：
/* 性能统计结构 */
struct xfrm_cryptodev_stats {
    uint64_t crypto_ops_submitted;    /* 提交的加密操作数 */
    uint64_t crypto_ops_completed;    /* 完成的加密操作数 */
    uint64_t crypto_ops_failed;       /* 失败的加密操作数 */
    uint64_t sw_fallback_count;       /* 软件回退次数 */
    uint64_t total_process_time;      /* 总处理时间（纳秒） */
    uint64_t max_process_time;        /* 最大处理时间（纳秒） */
};

/* 全局统计变量 */
static struct xfrm_cryptodev_stats crypto_stats;

/* 更新统计信息的函数 */
static inline void update_crypto_stats_submit(void)
{
    crypto_stats.crypto_ops_submitted++;
}

static inline void update_crypto_stats_complete(uint64_t process_time)
{
    crypto_stats.crypto_ops_completed++;
    crypto_stats.total_process_time += process_time;
    if (process_time > crypto_stats.max_process_time)
        crypto_stats.max_process_time = process_time;
}

static inline void update_crypto_stats_fail(void)
{
    crypto_stats.crypto_ops_failed++;
}

static inline void update_crypto_stats_fallback(void)
{
    crypto_stats.sw_fallback_count++;
}

/* 显示统计信息的函数 */
void xfrm_cryptodev_show_perf_stats(void)
{
    printf("Cryptodev Performance Statistics:\n");
    printf("  Operations submitted: %"PRIu64"\n", crypto_stats.crypto_ops_submitted);
    printf("  Operations completed: %"PRIu64"\n", crypto_stats.crypto_ops_completed);
    printf("  Operations failed: %"PRIu64"\n", crypto_stats.crypto_ops_failed);
    printf("  Software fallbacks: %"PRIu64"\n", crypto_stats.sw_fallback_count);
    
    if (crypto_stats.crypto_ops_completed > 0) {
        printf("  Average process time: %"PRIu64" ns\n", 
               crypto_stats.total_process_time / crypto_stats.crypto_ops_completed);
        printf("  Maximum process time: %"PRIu64" ns\n", crypto_stats.max_process_time);
    }
}

10. 错误处理和恢复机制
10.1 错误处理策略
在 xfrm_cryptodev.h 中添加：
/* 错误处理策略 */
#define XFRM_CRYPTO_ERR_POLICY_FALLBACK  0  /* 回退到软件处理 */
#define XFRM_CRYPTO_ERR_POLICY_RETRY     1  /* 重试操作 */
#define XFRM_CRYPTO_ERR_POLICY_DROP      2  /* 丢弃数据包 */

/* 全局错误处理策略 */
static int cryptodev_error_policy = XFRM_CRYPTO_ERR_POLICY_FALLBACK;

/* 设置错误处理策略 */
static inline void xfrm_cryptodev_set_error_policy(int policy)
{
    if (policy >= XFRM_CRYPTO_ERR_POLICY_FALLBACK && 
        policy <= XFRM_CRYPTO_ERR_POLICY_DROP)
        cryptodev_error_policy = policy;
}

/* 根据错误处理策略处理错误 */
static int xfrm_cryptodev_handle_error(struct xfrm_state *x, struct sk_buff *skb, int err)
{
    switch (cryptodev_error_policy) {
    case XFRM_CRYPTO_ERR_POLICY_FALLBACK:
        /* 标记 SA 使用软件回退 */
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_fallback();
        return -EAGAIN;  /* 指示调用者使用软件路径 */
        
    case XFRM_CRYPTO_ERR_POLICY_RETRY:
        /* 重试操作，最多重试 3 次 */
        if (++skb->crypto_retry_count < 3)
            return -EAGAIN;
        /* 超过重试次数，回退到软件路径 */
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_fallback();
        return -EAGAIN;
        
    case XFRM_CRYPTO_ERR_POLICY_DROP:
    default:
        /* 直接丢弃数据包 */
        kfree_skb(skb);
        return 0;  /* 指示调用者数据包已处理 */
    }
}
10.2 健康检查和恢复
在 xfrm_cryptodev_poll.c 中添加：
/* 设备健康检查 */
static void xfrm_cryptodev_health_check(void)
{
    int i;
    
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        uint8_t dev_id = xfrm_cryptodev_ctx.dev_ids[i];
        struct rte_cryptodev_stats stats;
        
        /* 获取设备统计信息 */
        if (rte_cryptodev_stats_get(dev_id, &stats) < 0) {
            CRYPTO_ERROR("Failed to get stats for device %d\n", dev_id);
            continue;
        }
        
        /* 检查错误计数 */
        if (stats.error_count > xfrm_cryptodev_ctx.last_error_count[i]) {
            uint64_t new_errors = stats.error_count - xfrm_cryptodev_ctx.last_error_count[i];
            
            CRYPTO_ERROR("Device %d reported %"PRIu64" new errors\n", 
                        dev_id, new_errors);
            
            /* 如果错误太多，尝试重置设备 */
            if (new_errors > CRYPTODEV_ERROR_THRESHOLD) {
                CRYPTO_ERROR("Attempting to reset device %d\n", dev_id);
                
                /* 停止设备 */
                rte_cryptodev_stop(dev_id);
                
                /* 重新配置设备 */
                /* ... */
                
                /* 重新启动设备 */
                if (rte_cryptodev_start(dev_id) < 0) {
                    CRYPTO_ERROR("Failed to restart device %d\n", dev_id);
                    /* 标记设备不可用 */
                    xfrm_cryptodev_ctx.dev_active[i] = 0;
                } else {
                    CRYPTO_DEBUG("Successfully restarted device %d\n", dev_id);
                }
            }
        }
        
        /* 更新上次错误计数 */
        xfrm_cryptodev_ctx.last_error_count[i] = stats.error_count;
    }
}
11. 总结
以上是在 dplane 中集成 cryptodev 支持的详细修改规划。主要修改点包括：

新增文件：
xfrm_cryptodev.h/.c - 核心 cryptodev 支持
xfrm_cryptodev_session.c - 会话管理
xfrm_cryptodev_ops.c - 加密操作处理
xfrm_cryptodev_poll.c - 异步处理
xfrm_cryptodev_config.c - 配置接口
xfrm_cryptodev_mgmt.c - 管理接口
修改现有文件：
xfrm_state.h/.c - 扩展 SA 结构
xfrm_input.c - 集成解密支持
xfrm_output.c - 集成加密支持
xfrm_policy.c - 初始化 cryptodev
Makefile - 添加新文件
配置和命令处理文件 - 添加 cryptodev 选项
关键功能：
SA 与 cryptodev 会话映射
算法参数转换
异步处理机制
错误处理和恢复
性能统计和调试
这个设计保持了与现有 xfrm 框架的兼容性，同时添加了 cryptodev 加速的能力。通过在 xfrm_state 中添加 cryptodev 会话指针，建立了 SA 与 cryptodev 会话之间的一对一映射关系。

实现时应注意以下几点：
确保正确处理异步完成的数据包
实现适当的错误处理和恢复机制
优化内存使用和数据复制
提供详细的调试和统计信息

dplane 集成 cryptodev 方案中参考 ipsec-gw 的功能对照分析
我设计的 dplane 集成 cryptodev 的修改方案中，有多处参考了 ipsec-gw 的实现。下面我将详细说明方案中的各个部分是如何参考 ipsec-gw 的具体功能：

1. Cryptodev 初始化
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec-secgw.c 中的 cryptodevs_init() 函数
该函数负责初始化 cryptodev 设备、配置队列对、创建哈希表用于算法映射
我们方案中的对应部分：

xfrm_cryptodev_init() 函数
同样实现了设备发现、配置和队列对设置
创建了类似的哈希表用于算法映射
2. 会话池初始化
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec-secgw.c 中的 session_pool_init() 函数
该函数创建加密会话池，用于存储加密会话
我们方案中的对应部分：

在 xfrm_cryptodev_init() 中创建会话池
使用了类似的参数和配置方式
3. 会话创建和管理
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec.c 中的 create_lookaside_session() 函数
该函数负责为 SA 创建对应的 cryptodev 会话
我们方案中的对应部分：

xfrm_cryptodev_session_create() 函数
同样实现了 SA 参数到 cryptodev 参数的转换
使用了类似的会话创建和管理方式
4. 算法映射
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec.c 中的算法参数设置和转换
使用 struct cdev_key 结构进行算法映射
我们方案中的对应部分：

xfrm_to_cryptodev_xforms() 函数
实现了类似的算法映射表和转换逻辑
5. 加密操作准备和提交
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec_process.c 中的 ipsec_prepare_crypto_group() 函数
 ipsec-secgw/ipsec.c 中的 enqueue_cop_burst() 函数
我们方案中的对应部分：

xfrm_cryptodev_encrypt() 和 xfrm_cryptodev_decrypt() 函数
xfrm_cryptodev_enqueue_burst() 函数
实现了类似的加密操作准备和批量提交
6. 异步处理机制
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec_worker.c 中的 ipsec_ev_cryptodev_process() 函数
 ipsec-secgw/ipsec_process.c 中的 ipsec_cqp_process() 函数
我们方案中的对应部分：

xfrm_cryptodev_poll_thread() 函数
xfrm_cryptodev_process_completed() 函数
实现了类似的异步处理和完成处理机制
7. 设备选择和负载均衡
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec-secgw.c 中的 add_mapping() 和 add_cdev_mapping() 函数
这些函数实现了加密设备到核心的映射和负载均衡
我们方案中的对应部分：

xfrm_cryptodev_select_device() 函数
实现了类似的设备选择和负载均衡策略
8. 错误处理和恢复
参考 ipsec-gw 的功能：

 ipsec-secgw/ipsec_worker.c 中的错误处理逻辑
检查加密操作状态并相应处理
我们方案中的对应部分：

xfrm_cryptodev_handle_error() 函数
实现了类似的错误处理策略和恢复机制
9. 数据包转换
参考 ipsec-gw 的功能：

ipsec-gw 中使用 mbuf 结构直接处理数据包
在  ipsec-secgw/ipsec_worker.c 中有数据包处理逻辑
我们方案中的对应部分：

skb_to_mbuf() 和 mbuf_to_skb() 函数
实现了 sk_buff 和 mbuf 之间的转换，以适配 dplane 的数据包结构
10. 统计和管理接口
参考 ipsec-gw 的功能：

ipsec-gw 中的统计收集和显示功能
设备管理和配置接口
我们方案中的对应部分：

xfrm_cryptodev_show_stats() 和相关函数
实现了类似的统计收集和管理接口
总结
我们的修改方案主要参考了 ipsec-gw 中的以下核心功能：

Cryptodev 初始化和配置：设备发现、配置和队列设置
会话管理：会话池创建、会话创建和销毁
算法映射：将 IPsec 算法映射到 cryptodev 算法
数据包处理：加密操作准备、提交和完成处理
异步处理机制：轮询和事件处理
负载均衡：设备选择和核心映射
错误处理：操作状态检查和恢复机制