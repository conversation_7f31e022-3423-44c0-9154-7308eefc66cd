/*
 * xfrm_cryptodev_zerocopy.h
 *
 * Description: 零拷贝的skb-mbuf转换头文件
 */

#ifndef _XFRM_CRYPTODEV_ZEROCOPY_H
#define _XFRM_CRYPTODEV_ZEROCOPY_H

#include <linux/skbuff.h>
#include <rte_mbuf.h>
#include <net/xfrm.h>

/* 常量定义 */
#define CRYPTODEV_MAX_PACKET_SIZE   9000   /* 最大支持的数据包大小 */
#define CRYPTODEV_MIN_HEADROOM      64     /* 最小头部空间 */
#define CRYPTODEV_MIN_TAILROOM      64     /* 最小尾部空间 */

/* 异步上下文结构 */
struct xfrm_crypto_async_context {
    struct xfrm_state *x;
    struct sk_buff *skb;
    int direction;  /* XFRM_POLICY_IN 或 XFRM_POLICY_OUT */
    uint32_t seq;
    void *orig_data;
    uint32_t orig_len;
};

/* 函数声明 */

/**
 * 为cryptodev操作准备mbuf（零拷贝）
 * 
 * @param skb 源sk_buff
 * @return 准备好的mbuf指针，失败返回NULL
 */
struct rte_mbuf *xfrm_prepare_mbuf_for_crypto(struct sk_buff *skb);

/**
 * 从cryptodev操作完成后恢复skb（零拷贝）
 * 
 * @param skb 目标sk_buff
 * @param m 处理完成的mbuf
 * @return 0成功，负数表示错误
 */
int xfrm_restore_skb_from_crypto(struct sk_buff *skb, struct rte_mbuf *m);

/**
 * 在mbuf中保存异步上下文
 * 
 * @param m mbuf指针
 * @param x xfrm_state指针
 * @param skb sk_buff指针
 * @param direction 处理方向
 * @return 0成功，负数表示错误
 */
int xfrm_save_async_context(struct rte_mbuf *m, struct xfrm_state *x, 
                           struct sk_buff *skb, int direction);

/**
 * 从mbuf中获取异步上下文
 * 
 * @param m mbuf指针
 * @return 异步上下文指针
 */
struct xfrm_crypto_async_context *xfrm_get_async_context(struct rte_mbuf *m);

/**
 * 清理异步上下文
 * 
 * @param m mbuf指针
 */
void xfrm_cleanup_async_context(struct rte_mbuf *m);

/**
 * 检查数据包是否适合使用cryptodev
 * 
 * @param skb sk_buff指针
 * @return 1适合，0不适合
 */
int xfrm_packet_suitable_for_cryptodev(struct sk_buff *skb);

/**
 * 获取ESP头指针
 * 
 * @param m mbuf指针
 * @param ip_version IP版本（4或6）
 * @return ESP头指针
 */
struct ip_esp_hdr *xfrm_get_esp_header(struct rte_mbuf *m, int ip_version);

/**
 * 计算ESP载荷偏移和长度
 * 
 * @param m mbuf指针
 * @param x xfrm_state指针
 * @param data_offset 输出：加密数据偏移
 * @param data_length 输出：加密数据长度
 * @param auth_offset 输出：认证数据偏移
 * @param auth_length 输出：认证数据长度
 * @return 0成功，负数表示错误
 */
int xfrm_calculate_esp_offsets(struct rte_mbuf *m, struct xfrm_state *x,
                              uint32_t *data_offset, uint32_t *data_length,
                              uint32_t *auth_offset, uint32_t *auth_length);

#endif /* _XFRM_CRYPTODEV_ZEROCOPY_H */
