EXPERIMENTAL {
	global:

	# added in 21.11
	rte_gpu_add_child;
	rte_gpu_callback_register;
	rte_gpu_callback_unregister;
	rte_gpu_close;
	rte_gpu_comm_cleanup_list;
	rte_gpu_comm_create_flag;
	rte_gpu_comm_create_list;
	rte_gpu_comm_destroy_flag;
	rte_gpu_comm_destroy_list;
	rte_gpu_comm_get_flag_value;
	rte_gpu_comm_get_status;
	rte_gpu_comm_populate_list_pkts;
	rte_gpu_comm_set_flag;
	rte_gpu_comm_set_status;
	rte_gpu_count_avail;
	rte_gpu_find_next;
	rte_gpu_info_get;
	rte_gpu_init;
	rte_gpu_is_valid;
	rte_gpu_mem_alloc;
	rte_gpu_mem_cpu_map;
	rte_gpu_mem_free;
	rte_gpu_mem_register;
	rte_gpu_mem_cpu_unmap;
	rte_gpu_mem_unregister;
	rte_gpu_wmb;
};

INTERNAL {
	global:

	rte_gpu_allocate;
	rte_gpu_attach;
	rte_gpu_complete_new;
	rte_gpu_get_by_name;
	rte_gpu_notify;
	rte_gpu_release;

	local: *;
};
