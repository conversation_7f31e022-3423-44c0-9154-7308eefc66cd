DPDK_24 {
	global:

	rte_cfgfile_add_entry;
	rte_cfgfile_add_section;
	rte_cfgfile_close;
	rte_cfgfile_create;
	rte_cfgfile_get_entry;
	rte_cfgfile_has_entry;
	rte_cfgfile_has_section;
	rte_cfgfile_load;
	rte_cfgfile_load_with_params;
	rte_cfgfile_num_sections;
	rte_cfgfile_save;
	rte_cfgfile_section_entries;
	rte_cfgfile_section_entries_by_index;
	rte_cfgfile_section_num_entries;
	rte_cfgfile_section_num_entries_by_index;
	rte_cfgfile_sections;
	rte_cfgfile_set_entry;

	local: *;
};
