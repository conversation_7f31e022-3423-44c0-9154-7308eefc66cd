DPDK_24 {
	global:

	rte_bbdev_allocate;
	rte_bbdev_callback_register;
	rte_bbdev_callback_unregister;
	rte_bbdev_close;
	rte_bbdev_count;
	rte_bbdev_dec_op_alloc_bulk;
	rte_bbdev_dec_op_free_bulk;
	rte_bbdev_dequeue_dec_ops;
	rte_bbdev_dequeue_enc_ops;
	rte_bbdev_dequeue_fft_ops;
	rte_bbdev_device_status_str;
	rte_bbdev_devices;
	rte_bbdev_enc_op_alloc_bulk;
	rte_bbdev_enc_op_free_bulk;
	rte_bbdev_enqueue_dec_ops;
	rte_bbdev_enqueue_enc_ops;
	rte_bbdev_enqueue_fft_ops;
	rte_bbdev_enqueue_status_str;
	rte_bbdev_fft_op_alloc_bulk;
	rte_bbdev_fft_op_free_bulk;
	rte_bbdev_find_next;
	rte_bbdev_get_named_dev;
	rte_bbdev_info_get;
	rte_bbdev_intr_enable;
	rte_bbdev_is_valid;
	rte_bbdev_op_pool_create;
	rte_bbdev_op_type_str;
	rte_bbdev_pmd_callback_process;
	rte_bbdev_queue_configure;
	rte_bbdev_queue_info_get;
	rte_bbdev_queue_intr_ctl;
	rte_bbdev_queue_intr_disable;
	rte_bbdev_queue_intr_enable;
	rte_bbdev_queue_start;
	rte_bbdev_queue_stop;
	rte_bbdev_release;
	rte_bbdev_setup_queues;
	rte_bbdev_start;
	rte_bbdev_stats_get;
	rte_bbdev_stats_reset;
	rte_bbdev_stop;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 23.11
	rte_bbdev_dequeue_mldts_ops;
	rte_bbdev_enqueue_mldts_ops;
	rte_bbdev_mldts_op_alloc_bulk;
	rte_bbdev_mldts_op_free_bulk;
};
