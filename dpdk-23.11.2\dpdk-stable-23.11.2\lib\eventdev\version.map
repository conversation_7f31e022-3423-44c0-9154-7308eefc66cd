DPDK_24 {
	global:

	__rte_eventdev_trace_crypto_adapter_enqueue;
	__rte_eventdev_trace_deq_burst;
	__rte_eventdev_trace_enq_burst;
	__rte_eventdev_trace_eth_tx_adapter_enqueue;
	__rte_eventdev_trace_maintain;
	__rte_eventdev_trace_timer_arm_burst;
	__rte_eventdev_trace_timer_arm_tmo_tick_burst;
	__rte_eventdev_trace_timer_cancel_burst;
	rte_event_crypto_adapter_caps_get;
	rte_event_crypto_adapter_create;
	rte_event_crypto_adapter_create_ext;
	rte_event_crypto_adapter_event_port_get;
	rte_event_crypto_adapter_free;
	rte_event_crypto_adapter_queue_pair_add;
	rte_event_crypto_adapter_queue_pair_del;
	rte_event_crypto_adapter_service_id_get;
	rte_event_crypto_adapter_start;
	rte_event_crypto_adapter_stats_get;
	rte_event_crypto_adapter_stats_reset;
	rte_event_crypto_adapter_stop;
	rte_event_crypto_adapter_vector_limits_get;
	rte_event_dequeue_timeout_ticks;
	rte_event_dev_attr_get;
	rte_event_dev_close;
	rte_event_dev_configure;
	rte_event_dev_count;
	rte_event_dev_dump;
	rte_event_dev_get_dev_id;
	rte_event_dev_info_get;
	rte_event_dev_selftest;
	rte_event_dev_service_id_get;
	rte_event_dev_socket_id;
	rte_event_dev_start;
	rte_event_dev_stop;
	rte_event_dev_stop_flush_callback_register;
	rte_event_dev_xstats_by_name_get;
	rte_event_dev_xstats_get;
	rte_event_dev_xstats_names_get;
	rte_event_dev_xstats_reset;
	rte_event_eth_rx_adapter_caps_get;
	rte_event_eth_rx_adapter_cb_register;
	rte_event_eth_rx_adapter_create;
	rte_event_eth_rx_adapter_create_ext;
	rte_event_eth_rx_adapter_create_with_params;
	rte_event_eth_rx_adapter_event_port_get;
	rte_event_eth_rx_adapter_free;
	rte_event_eth_rx_adapter_instance_get;
	rte_event_eth_rx_adapter_queue_add;
	rte_event_eth_rx_adapter_queue_conf_get;
	rte_event_eth_rx_adapter_queue_del;
	rte_event_eth_rx_adapter_queue_stats_get;
	rte_event_eth_rx_adapter_queue_stats_reset;
	rte_event_eth_rx_adapter_service_id_get;
	rte_event_eth_rx_adapter_start;
	rte_event_eth_rx_adapter_stats_get;
	rte_event_eth_rx_adapter_stats_reset;
	rte_event_eth_rx_adapter_stop;
	rte_event_eth_rx_adapter_vector_limits_get;
	rte_event_eth_tx_adapter_caps_get;
	rte_event_eth_tx_adapter_create;
	rte_event_eth_tx_adapter_create_ext;
	rte_event_eth_tx_adapter_event_port_get;
	rte_event_eth_tx_adapter_free;
	rte_event_eth_tx_adapter_instance_get;
	rte_event_eth_tx_adapter_queue_add;
	rte_event_eth_tx_adapter_queue_del;
	rte_event_eth_tx_adapter_queue_start;
	rte_event_eth_tx_adapter_queue_stop;
	rte_event_eth_tx_adapter_service_id_get;
	rte_event_eth_tx_adapter_start;
	rte_event_eth_tx_adapter_stats_get;
	rte_event_eth_tx_adapter_stats_reset;
	rte_event_eth_tx_adapter_stop;
	rte_event_fp_ops;
	rte_event_port_attr_get;
	rte_event_port_default_conf_get;
	rte_event_port_link;
	rte_event_port_links_get;
	rte_event_port_quiesce;
	rte_event_port_setup;
	rte_event_port_unlink;
	rte_event_port_unlinks_in_progress;
	rte_event_queue_attr_get;
	rte_event_queue_attr_set;
	rte_event_queue_default_conf_get;
	rte_event_queue_setup;
	rte_event_ring_create;
	rte_event_ring_free;
	rte_event_ring_init;
	rte_event_ring_lookup;
	rte_event_timer_adapter_caps_get;
	rte_event_timer_adapter_create;
	rte_event_timer_adapter_create_ext;
	rte_event_timer_adapter_free;
	rte_event_timer_adapter_get_info;
	rte_event_timer_adapter_lookup;
	rte_event_timer_adapter_service_id_get;
	rte_event_timer_adapter_start;
	rte_event_timer_adapter_stats_get;
	rte_event_timer_adapter_stats_reset;
	rte_event_timer_adapter_stop;
	rte_event_timer_arm_burst;
	rte_event_timer_arm_tmo_tick_burst;
	rte_event_timer_cancel_burst;
	rte_event_vector_pool_create;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 23.03
	rte_event_crypto_adapter_runtime_params_get;
	rte_event_crypto_adapter_runtime_params_init;
	rte_event_crypto_adapter_runtime_params_set;
	rte_event_eth_rx_adapter_runtime_params_get;
	rte_event_eth_rx_adapter_runtime_params_init;
	rte_event_eth_rx_adapter_runtime_params_set;
	rte_event_eth_tx_adapter_runtime_params_get;
	rte_event_eth_tx_adapter_runtime_params_init;
	rte_event_eth_tx_adapter_runtime_params_set;
	rte_event_timer_remaining_ticks_get;

	# added in 23.11
	rte_event_dma_adapter_caps_get;
	rte_event_dma_adapter_create;
	rte_event_dma_adapter_create_ext;
	rte_event_dma_adapter_enqueue;
	rte_event_dma_adapter_event_port_get;
	rte_event_dma_adapter_free;
	rte_event_dma_adapter_runtime_params_get;
	rte_event_dma_adapter_runtime_params_init;
	rte_event_dma_adapter_runtime_params_set;
	rte_event_dma_adapter_service_id_get;
	rte_event_dma_adapter_start;
	rte_event_dma_adapter_stats_get;
	rte_event_dma_adapter_stats_reset;
	rte_event_dma_adapter_stop;
	rte_event_dma_adapter_vchan_add;
	rte_event_dma_adapter_vchan_del;
	rte_event_eth_rx_adapter_create_ext_with_params;
	rte_event_port_profile_links_set;
	rte_event_port_profile_unlink;
	rte_event_port_profile_links_get;
	__rte_eventdev_trace_port_profile_switch;
};

INTERNAL {
	global:

	event_dev_fp_ops_reset;
	event_dev_fp_ops_set;
	event_dev_probing_finish;
	rte_event_pmd_allocate;
	rte_event_pmd_get_named_dev;
	rte_event_pmd_is_valid_dev;
	rte_event_pmd_pci_probe;
	rte_event_pmd_pci_probe_named;
	rte_event_pmd_pci_remove;
	rte_event_pmd_release;
	rte_event_pmd_selftest_seqn_dynfield_offset;
	rte_event_pmd_vdev_init;
	rte_event_pmd_vdev_uninit;
	rte_eventdevs;
};
