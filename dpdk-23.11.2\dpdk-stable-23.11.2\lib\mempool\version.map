DPDK_24 {
	global:

	rte_mempool_audit;
	rte_mempool_avail_count;
	rte_mempool_cache_create;
	rte_mempool_cache_free;
	rte_mempool_calc_obj_size;
	rte_mempool_check_cookies;
	rte_mempool_contig_blocks_check_cookies;
	rte_mempool_create;
	rte_mempool_create_empty;
	rte_mempool_dump;
	rte_mempool_free;
	rte_mempool_get_page_size;
	rte_mempool_in_use_count;
	rte_mempool_list_dump;
	rte_mempool_lookup;
	rte_mempool_mem_iter;
	rte_mempool_obj_iter;
	rte_mempool_op_calc_mem_size_default;
	rte_mempool_op_calc_mem_size_helper;
	rte_mempool_op_populate_default;
	rte_mempool_op_populate_helper;
	rte_mempool_ops_get_info;
	rte_mempool_ops_table;
	rte_mempool_populate_anon;
	rte_mempool_populate_default;
	rte_mempool_populate_iova;
	rte_mempool_populate_virt;
	rte_mempool_register_ops;
	rte_mempool_set_ops_byname;
	rte_mempool_walk;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 20.05
	__rte_mempool_trace_ops_dequeue_bulk;
	__rte_mempool_trace_ops_dequeue_contig_blocks;
	__rte_mempool_trace_ops_enqueue_bulk;
	__rte_mempool_trace_generic_put;
	__rte_mempool_trace_put_bulk;
	__rte_mempool_trace_generic_get;
	__rte_mempool_trace_get_bulk;
	__rte_mempool_trace_get_contig_blocks;
	__rte_mempool_trace_default_cache;
	__rte_mempool_trace_cache_flush;
};

INTERNAL {
	global:

	# added in 21.11
	rte_mempool_event_callback_register;
	rte_mempool_event_callback_unregister;
};
