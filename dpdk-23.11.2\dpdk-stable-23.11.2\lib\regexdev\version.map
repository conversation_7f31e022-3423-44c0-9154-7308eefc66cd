EXPERIMENTAL {
	global:

	rte_regex_devices;
	rte_regexdev_attr_get;
	rte_regexdev_attr_set;
	rte_regexdev_close;
	rte_regexdev_configure;
	rte_regexdev_count;
	rte_regexdev_dequeue_burst;
	rte_regexdev_dump;
	rte_regexdev_enqueue_burst;
	rte_regexdev_get_dev_id;
	rte_regexdev_info_get;
	rte_regexdev_is_valid_dev;
	rte_regexdev_logtype;
	rte_regexdev_queue_pair_setup;
	rte_regexdev_rule_db_compile_activate;
	rte_regexdev_rule_db_export;
	rte_regexdev_rule_db_import;
	rte_regexdev_rule_db_update;
	rte_regexdev_selftest;
	rte_regexdev_start;
	rte_regexdev_stop;
	rte_regexdev_xstats_by_name_get;
	rte_regexdev_xstats_get;
	rte_regexdev_xstats_names_get;
	rte_regexdev_xstats_reset;

	local: *;
};

INTERNAL {
	rte_regexdev_get_device_by_name;
	rte_regexdev_register;
	rte_regexdev_unregister;
};
