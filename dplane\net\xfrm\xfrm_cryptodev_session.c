/*
 * xfrm_cryptodev_session.c
 *
 * Description: DPDK cryptodev session management for xfrm framework
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"

/* 检查设备是否支持指定算法 */
static int xfrm_cryptodev_check_capabilities(uint8_t dev_id,
                                           int cipher_algo,
                                           int auth_algo)
{
    struct rte_cryptodev_info dev_info;
    const struct rte_cryptodev_capabilities *cap;
    int cipher_found = 0, auth_found = 0;

    /* 获取设备信息 */
    rte_cryptodev_info_get(dev_id, &dev_info);

    /* 检查设备能力 */
    for (cap = dev_info.capabilities; cap->op != RTE_CRYPTO_OP_TYPE_UNDEFINED; cap++) {
        if (cap->op != RTE_CRYPTO_OP_TYPE_SYMMETRIC)
            continue;

        if (cap->sym.xform_type == RTE_CRYPTO_SYM_XFORM_CIPHER &&
            cap->sym.cipher.algo == cipher_algo)
            cipher_found = 1;

        if (cap->sym.xform_type == RTE_CRYPTO_SYM_XFORM_AUTH &&
            cap->sym.auth.algo == auth_algo)
            auth_found = 1;
    }

    return (cipher_found && auth_found) ? 0 : -1;
}

/* 选择合适的 cryptodev 设备和队列对 */
int xfrm_cryptodev_select_device(struct xfrm_state *x, uint16_t *dev_id, uint16_t *qp_id)
{
    struct xfrm_cdev_key key = { 0 };
    unsigned long cdev_id_qp = 0;
    int32_t ret;
    enum rte_crypto_cipher_algorithm cipher_algo = RTE_CRYPTO_CIPHER_NULL;
    enum rte_crypto_auth_algorithm auth_algo = RTE_CRYPTO_AUTH_NULL;
    uint8_t i;

    /* 获取加密算法 - 注意：xfrm_enc_alg 结构体中没有 alg_name 和 alg_key_len 成员 */
    if (x->ealg) {
        /* 使用 props.ealgo 和 enc_key_len 替代 */
        char alg_name[64] = {0};

        /* 根据算法 ID 获取算法名称 */
        switch (x->props.ealgo) {
        case IPSEC_ESP_AES:
            strcpy(alg_name, "cbc(aes)");
            break;
        case IPSEC_ESP_3DES:
            strcpy(alg_name, "cbc(des3_ede)");
            break;
        case IPSEC_ESP_DES:
            strcpy(alg_name, "cbc(des)");
            break;
        case IPSEC_ESP_NULL:
            strcpy(alg_name, "ecb(cipher_null)");
            break;
        default:
            CRYPTO_ERROR("Unsupported cipher algorithm ID: %d\n", x->props.ealgo);
            return -EINVAL;
        }

        const struct xfrm_cipher_algo_map *cipher_map =
            xfrm_find_cipher_algo(alg_name, x->enc_key_len);
        if (cipher_map)
            cipher_algo = cipher_map->cipher_algo;
    }

    /* 获取认证算法 - 注意：xfrm_auth_alg 结构体中没有 alg_name 和 alg_key_len 成员 */
    if (x->aalg) {
        /* 使用 props.aalgo 和 auth_key_len 替代 */
        char alg_name[64] = {0};

        /* 根据算法 ID 获取算法名称 */
        switch (x->props.aalgo) {
        case IPSEC_AUTH_SHA1:
            strcpy(alg_name, "hmac(sha1)");
            break;
        case IPSEC_AUTH_MD5:
            strcpy(alg_name, "hmac(md5)");
            break;
        case IPSEC_AUTH_SHA256:
            strcpy(alg_name, "hmac(sha256)");
            break;
        case IPSEC_AUTH_SHA384:
            strcpy(alg_name, "hmac(sha384)");
            break;
        case IPSEC_AUTH_SHA512:
            strcpy(alg_name, "hmac(sha512)");
            break;
        case IPSEC_AUTH_NULL:
            strcpy(alg_name, "digest_null");
            break;
        default:
            CRYPTO_ERROR("Unsupported auth algorithm ID: %d\n", x->props.aalgo);
            return -EINVAL;
        }

        const struct xfrm_auth_algo_map *auth_map =
            xfrm_find_auth_algo(alg_name, x->auth_key_len);
        if (auth_map)
            auth_algo = auth_map->auth_algo;
    }

    /* 如果算法不支持，返回错误 */
    if (cipher_algo == RTE_CRYPTO_CIPHER_LIST_END || auth_algo == RTE_CRYPTO_AUTH_LIST_END) {
        CRYPTO_ERROR("Unsupported algorithm: cipher_algo=%d, auth_algo=%d\n",
                   x->props.ealgo, x->props.aalgo);
        return -ENOTSUP;
    }

    /* 设置查找键 */
    key.lcore_id = rte_lcore_id();
    key.cipher_algo = cipher_algo;
    key.auth_algo = auth_algo;

    /* 查找支持这些算法的设备 */
    ret = rte_hash_lookup_data(xfrm_cryptodev_ctx.algo_map, &key, (void **)&cdev_id_qp);
    if (ret >= 0) {
        /* 找到匹配的设备和队列对 */
        *dev_id = xfrm_cryptodev_ctx.qp_table[cdev_id_qp].dev_id;
        *qp_id = xfrm_cryptodev_ctx.qp_table[cdev_id_qp].qp_id;
        return 0;
    }

    /* 没有找到匹配的设备，尝试找一个支持这些算法的设备 */
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        uint8_t dev_id_tmp = xfrm_cryptodev_ctx.dev_ids[i];

        /* 检查设备是否活动 */
        if (!xfrm_cryptodev_ctx.dev_active[i])
            continue;

        /* 检查设备是否支持这些算法 */
        if (xfrm_cryptodev_check_capabilities(dev_id_tmp, cipher_algo, auth_algo) == 0) {
            /* 找到支持的设备，使用第一个队列对 */
            *dev_id = dev_id_tmp;
            *qp_id = 0;  /* 使用第一个队列对 */

            /* 添加到映射表中 */
            cdev_id_qp = i * CRYPTODEV_MAX_QPS + 0;
            rte_hash_add_key_data(xfrm_cryptodev_ctx.algo_map, &key, (void *)(uintptr_t)cdev_id_qp);

            return 0;
        }
    }

    CRYPTO_ERROR("No suitable cryptodev found for algorithms: cipher_algo=%d, auth_algo=%d\n",
               x->props.ealgo, x->props.aalgo);
    return -ENODEV;
}

/* 声明外部函数 */
extern int xfrm_set_crypto_xforms(struct xfrm_state *x, struct rte_crypto_sym_xform **xforms);

/* 为 xfrm 状态创建 cryptodev 会话 */
int xfrm_cryptodev_session_create(struct xfrm_state *x)
{
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_sym_xform *xforms = NULL;
    uint16_t dev_id, qp_id;
    int ret;

    CRYPTO_DEBUG("Creating cryptodev session for SA: spi=%u\n", x->id.spi);

    /* 分配会话结构 */
    crypto_session = kmalloc(sizeof(*crypto_session), MOD_XFRM_STATE);
    if (!crypto_session)
        return -ENOMEM;

    memset(crypto_session, 0, sizeof(*crypto_session));

    /* 将 xfrm 算法参数转换为 cryptodev 参数 */
    ret = xfrm_set_crypto_xforms(x, &xforms);
    if (ret) {
        kfree(crypto_session);
        return ret;
    }

    /* 选择合适的 cryptodev 设备和队列对 */
    ret = xfrm_cryptodev_select_device(x, &dev_id, &qp_id);
    if (ret) {
        kfree(xforms);
        kfree(crypto_session);
        return ret;
    }

    /* 创建 cryptodev 会话 */
    crypto_session->session = rte_cryptodev_sym_session_create(
        dev_id, xforms, xfrm_cryptodev_ctx.session_pool);
    if (!crypto_session->session) {
        CRYPTO_ERROR("Failed to create cryptodev session\n");
        kfree(xforms);
        kfree(crypto_session);
        return -EINVAL;
    }

    /* 保存会话信息 */
    crypto_session->dev_id = dev_id;
    crypto_session->qp_id = qp_id;
    crypto_session->xforms = xforms;
    crypto_session->sess_type = RTE_CRYPTO_OP_WITH_SESSION;

    /* 创建cryptodev上下文包装 */
    struct xfrm_cryptodev_context *ctx = kmalloc(sizeof(*ctx), MOD_XFRM_STATE);
    if (!ctx) {
        rte_cryptodev_sym_session_free(dev_id, crypto_session->session);
        kfree(crypto_session);
        return -ENOMEM;
    }

    /* 设置上下文头部 */
    ctx->header.type = XFRM_CONTEXT_TYPE_CRYPTODEV;
    ctx->header.size = sizeof(*ctx);
    ctx->session = crypto_session;

    /* 将包装的上下文与 xfrm_state 关联 */
    x->context = ctx;

    CRYPTO_DEBUG("Created cryptodev session for SA: spi=%u, dev_id=%u, qp_id=%u\n",
               x->id.spi, dev_id, qp_id);

    return 0;
}

/* 销毁 xfrm 状态的 cryptodev 会话 */
void xfrm_cryptodev_session_destroy(struct xfrm_state *x)
{
    struct xfrm_cryptodev_context *ctx;
    struct xfrm_cryptodev_session *crypto_session;

    if (!xfrm_has_cryptodev_session(x))
        return;

    ctx = (struct xfrm_cryptodev_context *)x->context;
    crypto_session = ctx->session;

    if (!crypto_session)
        return;

    CRYPTO_DEBUG("Destroying cryptodev session for SA: spi=%u\n", x->id.spi);

    /* 释放 cryptodev 会话 */
    if (crypto_session->session) {
        rte_cryptodev_sym_session_free(crypto_session->dev_id, crypto_session->session);
        crypto_session->session = NULL;
    }

    /* 释放转换结构 */
    if (crypto_session->xforms) {
        kfree(crypto_session->xforms);
        crypto_session->xforms = NULL;
    }

    /* 释放会话结构 */
    kfree(crypto_session);

    /* 清理上下文包装 */
    kfree(ctx);
    x->context = NULL;

    CRYPTO_DEBUG("Destroyed cryptodev session for SA: spi=%u\n", x->id.spi);
}
