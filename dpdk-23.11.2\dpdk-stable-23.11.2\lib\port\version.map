DPDK_24 {
	global:

	rte_port_ethdev_reader_ops;
	rte_port_ethdev_writer_nodrop_ops;
	rte_port_ethdev_writer_ops;
	rte_port_fd_reader_ops;
	rte_port_fd_writer_nodrop_ops;
	rte_port_fd_writer_ops;
	rte_port_ring_multi_reader_ops;
	rte_port_ring_multi_writer_nodrop_ops;
	rte_port_ring_multi_writer_ops;
	rte_port_ring_reader_ipv4_frag_ops;
	rte_port_ring_reader_ipv6_frag_ops;
	rte_port_ring_reader_ops;
	rte_port_ring_writer_ipv4_ras_ops;
	rte_port_ring_writer_ipv6_ras_ops;
	rte_port_ring_writer_nodrop_ops;
	rte_port_ring_writer_ops;
	rte_port_sched_reader_ops;
	rte_port_sched_writer_ops;
	rte_port_sink_ops;
	rte_port_source_ops;
	rte_port_sym_crypto_reader_ops;
	rte_port_sym_crypto_writer_nodrop_ops;
	rte_port_sym_crypto_writer_ops;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 19.11
	rte_port_eventdev_reader_ops;
	rte_port_eventdev_writer_nodrop_ops;
	rte_port_eventdev_writer_ops;

	# added in 20.11
	rte_swx_port_ethdev_reader_ops;
	rte_swx_port_ethdev_writer_ops;
	rte_swx_port_sink_ops;
	rte_swx_port_source_ops;

	# added in 21.05
	rte_swx_port_fd_reader_ops;
	rte_swx_port_fd_writer_ops;
	rte_swx_port_ring_reader_ops;
	rte_swx_port_ring_writer_ops;
};
