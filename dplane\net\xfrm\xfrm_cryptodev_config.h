/*
 * xfrm_cryptodev_config.h
 *
 * Description: Configuration definitions for cryptodev
 */

#ifndef __XFRM_CRYPTODEV_CONFIG_H__
#define __XFRM_CRYPTODEV_CONFIG_H__

/* 统计信息结构 */
struct xfrm_cryptodev_stats {
    uint64_t submitted;      /* 提交的操作数 */
    uint64_t completed;      /* 完成的操作数 */
    uint64_t failed;         /* 失败的操作数 */
    uint64_t sw_fallback;    /* 软件回退次数 */
    uint64_t total_time_ns;  /* 总处理时间（纳秒） */
};

/* 全局变量声明 */
extern struct xfrm_cryptodev_stats crypto_stats;

/* Cryptodev 配置选项 */
#define CRYPTODEV_ENABLED 1  /* 默认启用 cryptodev */
#define CRYPTODEV_QUEUE_SIZE 1024  /* 队列大小 */
#define CRYPTODEV_BURST_SIZE 32  /* 批处理大小 */
#define CRYPTODEV_SESSION_CACHE_SIZE 128  /* 会话缓存大小 */
#define CRYPTODEV_MAX_SESSIONS 4096  /* 最大会话数 */
#define CRYPTODEV_MAX_OPS 8192  /* 最大操作数 */
#define CRYPTODEV_MAX_QPS 16  /* 每个设备的最大队列对数 */
#define CRYPTODEV_ERROR_THRESHOLD 100  /* 错误阈值，超过此值尝试重置设备 */
#define CRYPTODEV_MAX_CONSECUTIVE_ERRORS 10  /* 最大连续错误数 */
#define CRYPTODEV_MAX_DEVS 8  /* 最大设备数 */

/* 调试级别 */
#define CRYPTO_DEBUG_LEVEL_NONE    0
#define CRYPTO_DEBUG_LEVEL_ERROR   1
#define CRYPTO_DEBUG_LEVEL_INFO    2
#define CRYPTO_DEBUG_LEVEL_DEBUG   3

/* 错误处理策略 */
#define XFRM_CRYPTO_ERR_POLICY_FALLBACK  0  /* 回退到软件处理 */
#define XFRM_CRYPTO_ERR_POLICY_RETRY     1  /* 重试操作 */
#define XFRM_CRYPTO_ERR_POLICY_DROP      2  /* 丢弃数据包 */

/* 函数声明 */
/* 配置函数 */
int set_cryptodev_enabled(int enabled);
void set_cryptodev_debug_level(int level);
void set_cryptodev_sw_fallback(int enabled);
void set_cryptodev_stats_interval(int interval);

/* 获取配置函数 */
int get_cryptodev_enabled(void);
int get_cryptodev_debug_level(void);
int get_cryptodev_sw_fallback(void);
int get_cryptodev_stats_interval(void);

/* 统计函数 */
void update_crypto_stats_submit(void);
void update_crypto_stats_complete(uint64_t time_ns);
void update_crypto_stats_fail(void);
void update_crypto_stats_sw_fallback(void);
void reset_crypto_stats(void);
void print_crypto_stats(void);

/* 设备管理函数 */
void print_cryptodev_info(void);
int reset_cryptodev_device(uint8_t dev_id);

/* 命令行处理函数 */
int cryptodev_cmd_handler(int argc, char **argv);

#endif /* __XFRM_CRYPTODEV_CONFIG_H__ */
