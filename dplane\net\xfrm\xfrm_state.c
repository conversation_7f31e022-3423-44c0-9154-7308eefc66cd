/*
 * xfrm_state.c
 *
 * Changes:
 *	<PERSON><PERSON><PERSON> @USAGI
 * 	Kazunori MIYAZAWA @USAGI
 * 	<PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>@ipinfusion.com>
 * 		IPv6 support
 * 	YOSHIFUJI Hideaki @USAGI
 * 		Split up af-specific functions
 *	<PERSON> <<EMAIL>>
 *		Add UDP Encapsulation
 *
 */

#include <linux/workqueue.h>
#include <net/xfrm.h>
#include <linux/pfkeyv2.h>
#include <linux/ipsec.h>
#include <linux/module.h>
#include <linux/ktime.h>
#include <linux/slab.h>
#include <linux/interrupt.h>
#include <linux/kernel.h>
#include "linux/err.h"

#include "xfrm_hash.h"
#include "xfrm_cryptodev.h"

/* 使用 xfrm_cryptodev_config.c 中定义的 cryptodev_enabled 变量 */
extern int cryptodev_enabled;

/* Each xfrm_state may be linked to two tables:

   1. Hash table by (spi,daddr,ah/esp) to find SA by SPI. (input,ctl)
   2. Hash table by (daddr,family,reqid) to find what SAs exist for given
      destination/tunnel endpoint. (output)
 */

FLOW_SHARED static DEFINE_SPINLOCK(xfrm_state_lock);
FLOW_SHARED static struct xfrm_state_afinfo *xfrm_state_afinfos[NPROTO];

static struct xfrm_state_afinfo *xfrm_state_get_afinfo(unsigned int family);

#define XFRM_OSEQ_HARD_LIMIT		4294967295UL		/* 2^32 -1, the max value in u32. the max valid output-sequence */
#define MAX_XFRM_PKT_PER_SEC	200000		/* max packet can be transited on one SA per secend, this is a estimates value (In our test environment, it can be reach 15w) */
#define XFRM_OSEQ_AVAIL_LEFT	2000000 /* (10 * MAX_XFRM_PKT_PER_SEC) . available oseq left, that is, it will be expired after 10 seconds,
											(if the transmission rate is MAX_XFRM_PKT_PER_SEC) */
#define XFRM_OSEQ_SOFT_LIMIT	(XFRM_OSEQ_HARD_LIMIT - XFRM_OSEQ_AVAIL_LEFT)


static inline unsigned int
xfrm_spi_hash(struct net *net, const xfrm_address_t *daddr,
	      __be32 spi, u8 proto, unsigned short family)
{
	return __xfrm_spi_hash(daddr, spi, proto, family, net->xfrm.state_hmask);
}

int xfrm_register_type(const struct xfrm_type *type, unsigned short family)
{
	struct xfrm_state_afinfo *afinfo = xfrm_state_get_afinfo(family);
	const struct xfrm_type **typemap;
	int err = 0;

	if (unlikely(afinfo == NULL))
		return -EAFNOSUPPORT;
	typemap = afinfo->type_map;

	if (likely(typemap[type->proto] == NULL))
		typemap[type->proto] = type;
	else
		err = -EEXIST;

	return err;
}

static const struct xfrm_type *xfrm_get_type(u8 proto, unsigned short family)
{
	struct xfrm_state_afinfo *afinfo;
	const struct xfrm_type **typemap;
	const struct xfrm_type *type;

	afinfo = xfrm_state_get_afinfo(family);
	if (unlikely(afinfo == NULL))
		return NULL;
	typemap = afinfo->type_map;

	type = typemap[proto];

	return type;
}

static void xfrm_put_type(const struct xfrm_type *type)
{
//	module_put(type->owner);
}

int xfrm_register_mode(struct xfrm_mode *mode, int family)
{
	struct xfrm_state_afinfo *afinfo;
	struct xfrm_mode **modemap;
	int err;

	if (unlikely(mode->encap >= XFRM_MODE_MAX))
		return -EINVAL;

	afinfo = xfrm_state_get_afinfo(family);
	if (unlikely(afinfo == NULL))
		return -EAFNOSUPPORT;

	err = -EEXIST;
	modemap = afinfo->mode_map;
	if (modemap[mode->encap])
		goto out;

	mode->afinfo = afinfo;
	modemap[mode->encap] = mode;
	err = 0;

out:
	return err;
}

static struct xfrm_mode *xfrm_get_mode(unsigned int encap, int family)
{
	struct xfrm_state_afinfo *afinfo;
	struct xfrm_mode *mode;

	if (unlikely(encap >= XFRM_MODE_MAX))
		return NULL;

	afinfo = xfrm_state_get_afinfo(family);
	if (unlikely(afinfo == NULL))
		return NULL;

	mode = afinfo->mode_map[encap];

	return mode;
}

static void xfrm_put_mode(struct xfrm_mode *mode)
{
//	module_put(mode->owner);
}

void xfrm_state_destroy(struct xfrm_state *x)
{
	if(x->auth_key)
		kfree(x->auth_key);
	if(x->enc_key)
		kfree(x->enc_key);
	if(x->context)
		kfree(x->context);
	kfree(x->encap);
	if (x->coaddr)
		kfree(x->coaddr);

	kfree(x);
}

struct xfrm_state *xfrm_state_alloc(struct net *net)
{
	struct xfrm_state *x;

	x = kmalloc(sizeof(struct xfrm_state), MOD_XFRM_STATE);

	if (x) {
		memset(x,0,sizeof(struct xfrm_state));

		write_pnet(&x->xs_net, net);
		atomic_set(&x->refcnt, 1);
		INIT_HLIST_NODE(&x->byspi);
		x->curlft.add_time = jiffies/HZ;
		x->lft.soft_byte_limit = XFRM_INF;
		x->lft.soft_packet_limit = XFRM_INF;
		x->lft.hard_byte_limit = XFRM_INF;
		x->lft.hard_packet_limit = XFRM_INF;
		x->inner_mode = NULL;
		x->inner_mode_iaf = NULL;
		spin_lock_init(&x->lock);
	}
	return x;
}

int __xfrm_state_delete(struct xfrm_state *x)
{
	struct net *net = &init_net;
	int err = 0;

	/* 如果有 cryptodev 会话，销毁它 */
	if (x->context) {
		xfrm_cryptodev_session_destroy(x);
		x->context = NULL;
	}

	if (x->state != XFRM_STATE_DEAD) {
		x->state = XFRM_STATE_DEAD;
		spin_lock(&xfrm_state_lock);
		if (x->id.spi)
			hlist_del(&x->byspi);
		net->xfrm.state_num--;
		spin_unlock(&xfrm_state_lock);

		/* All xfrm_state objects are created by xfrm_state_alloc.
		 * The xfrm_state_alloc call gives a reference, and that
		 * is what we are dropping here.
		 */
		xfrm_state_put(x);
		err = 0;
	}

	return err;
}

int xfrm_state_delete(struct xfrm_state *x)
{
	int err;

	spin_lock_bh(&x->lock);
	err = __xfrm_state_delete(x);
	spin_unlock_bh(&x->lock);

	return err;
}

void xfrm_state_flush(struct net *net)
{
	int i;

	spin_lock_bh(&xfrm_state_lock);
	for (i = 0; i <= net->xfrm.state_hmask; i++) {
		struct hlist_node *entry, *tmp;
		struct xfrm_state *x;
		hlist_for_each_entry_safe(x, entry, tmp,net->xfrm.state_byspi+i, byspi) {
			hlist_del(&x->byspi);
			xfrm_state_put(x);
		}
	}
	net->xfrm.state_num = 0;
	spin_unlock_bh(&xfrm_state_lock);
}

static struct xfrm_state *__xfrm_state_lookup(struct net *net, u32 mark,
					      const xfrm_address_t *daddr,
					      __be32 spi, u8 proto,
					      unsigned short family)
{
	unsigned int h = xfrm_spi_hash(net, daddr, spi, proto, family);
	struct xfrm_state *x;
	struct hlist_node *entry;

	hlist_for_each_entry(x, entry, net->xfrm.state_byspi+h, byspi) {
		if (x->props.family != family ||
		    x->id.spi       != spi ||
		    x->id.proto     != proto ||
		    xfrm_addr_cmp(&x->id.daddr, daddr, family))
			continue;

		xfrm_state_hold(x);
		return x;
	}

	return NULL;
}

static inline struct xfrm_state *
__xfrm_state_locate(struct xfrm_state *x, int use_spi, int family)
{
	struct net *net = xs_net(x);
	u32 mark = 0;

	if (use_spi)
		return __xfrm_state_lookup(net, mark, &x->id.daddr,
					   x->id.spi, x->id.proto, family);
	return NULL;
}

static void __xfrm_state_insert(struct xfrm_state *x)
{
	struct net *net = xs_net(x);
	unsigned int h;

	if (x->id.spi) {
		h = xfrm_spi_hash(net, &x->id.daddr, x->id.spi, x->id.proto,
				  x->props.family);

		hlist_add_head(&x->byspi, net->xfrm.state_byspi+h);
	}

	net->xfrm.state_num++;
}

void xfrm_state_insert(struct xfrm_state *x)
{
	spin_lock_bh(&xfrm_state_lock);
	__xfrm_state_insert(x);
	spin_unlock_bh(&xfrm_state_lock);
}

int xfrm_state_add(struct xfrm_state *x)
{
	struct xfrm_state *x1, *to_put;
	int family;
	int err;
	int use_spi = 1;

	family = x->props.family;

	to_put = NULL;

	spin_lock_bh(&xfrm_state_lock);

	x1 = __xfrm_state_locate(x, use_spi, family);
	if (x1) {
		to_put = x1;
		x1 = NULL;
		err = -EEXIST;
		goto out;
	}

	__xfrm_state_insert(x);
	err = 0;

out:
	spin_unlock_bh(&xfrm_state_lock);

	if (x1) {
		xfrm_state_delete(x1);
		xfrm_state_put(x1);
	}

	if (to_put)
		xfrm_state_put(to_put);

	return err;
}

int xfrm_state_check_expire(struct xfrm_state *x, struct xfrm_sa_bundle *sab)
{
	int need_put = 0;

	if (!x->curlft.use_time)
		x->curlft.use_time = jiffies/HZ;

	if (x->state != XFRM_STATE_VALID ||(sab &&  sab->state != XFRM_STATE_VALID))
		return -EINVAL;

	if ((x->curlft.bytes >= x->lft.hard_byte_limit && 0 != x->lft.hard_byte_limit)
		|| x->replay.oseq >= XFRM_OSEQ_HARD_LIMIT) {
		x->state = XFRM_STATE_EXPIRED;
		if (!sab) {
			sab = xfrm_sab_find_by_state(x);
			need_put = 1;
		}
		if (sab) {
			sab->state = XFRM_STATE_EXPIRED;
			xfrm_sa_del2ipsecd(sab, x->id.spi);
			xfrm_sab_expired(sab);
			if (need_put)
				xfrm_sab_put(sab);
		}
		return -EINVAL;
	}

	if ((x->curlft.bytes >= x->lft.soft_byte_limit && 0 != x->lft.soft_byte_limit)
		|| x->replay.oseq >= XFRM_OSEQ_SOFT_LIMIT) {
		if (!sab) {
		 	sab = xfrm_sab_find_by_state(x);
			need_put = 1;
		}

		if (sab) {
			if (!sab->dying) {
				x->dying = 1;
				sab->dying = 1;
				xfrm_sa_soft_expire2ipsecd(sab);
			}
			if (need_put)
				xfrm_sab_put(sab);
		}
	}

	return 0;
}

struct xfrm_state *
xfrm_state_lookup(struct net *net, u32 mark, const xfrm_address_t *daddr, __be32 spi,
		  u8 proto, unsigned short family)
{
	struct xfrm_state *x;

	spin_lock_bh(&xfrm_state_lock);
	x = __xfrm_state_lookup(net, mark, daddr, spi, proto, family);
	spin_unlock_bh(&xfrm_state_lock);
	return x;
}

int xfrm_state_register_afinfo(struct xfrm_state_afinfo *afinfo)
{
	int err = 0;

	if (unlikely(afinfo == NULL))
		return -EINVAL;

	if (unlikely(afinfo->family >= NPROTO))
		return -EAFNOSUPPORT;

	if (unlikely(xfrm_state_afinfos[afinfo->family] != NULL))
		err = -ENOBUFS;
	else
		xfrm_state_afinfos[afinfo->family] = afinfo;

	return err;
}

static struct xfrm_state_afinfo *xfrm_state_get_afinfo(unsigned int family)
{
	if (unlikely(family >= NPROTO))
		return NULL;

	return xfrm_state_afinfos[family];
}

int __xfrm_init_state(struct xfrm_state *x, bool init_replay)
{
	struct xfrm_state_afinfo *afinfo;
	struct xfrm_mode *inner_mode;
	int family = x->props.family;
	int err;

	err = -EAFNOSUPPORT;
	afinfo = xfrm_state_get_afinfo(family);
	if (!afinfo) {
		DP_IPSEC_TEMP_DEBUG(" Init SA, get af info failed, family:%d.\n",family);
		goto error;
	}

	err = 0;
	if (afinfo->init_flags)
		err = afinfo->init_flags(x);

	if (err) {
		DP_IPSEC_TEMP_DEBUG(" Init SA flags failed.\n");
		goto error;
	}

	err = -EPROTONOSUPPORT;

	inner_mode = xfrm_get_mode(x->props.mode, x->sel.family);
	if (inner_mode == NULL) {
		DP_IPSEC_TEMP_DEBUG(" get inner mode failed. mode:%d, family:%d\n",x->props.mode,x->sel.family);
		goto error;
	}

	if (!(inner_mode->flags & XFRM_MODE_FLAG_TUNNEL) &&
	    family != x->sel.family) {
		DP_IPSEC_TEMP_DEBUG(" mode not support.\n");
		xfrm_put_mode(inner_mode);
		goto error;
	}

	x->inner_mode = inner_mode;

	x->type = xfrm_get_type(x->id.proto, family);
	if (x->type == NULL) {
		DP_IPSEC_TEMP_DEBUG(" get type failed. proto:%d, family:%d.\n",x->id.proto,family);
		goto error;
	}

	err = x->type->init_state(x);
	if (err) {
		DP_IPSEC_TEMP_DEBUG(" type init SA failed:%d.\n",err);
		goto error;
	}

	x->outer_mode = xfrm_get_mode(x->props.mode, family);
	if (x->outer_mode == NULL) {
		DP_IPSEC_TEMP_DEBUG(" get outer mode failed. mode:%d, family:%d\n",x->props.mode,x->sel.family);
		err = -EPROTONOSUPPORT;
		goto error;
	}

	if (init_replay) {
		err = xfrm_init_replay(x);
		if (err) {
			DP_IPSEC_TEMP_DEBUG(" SA init replay failed:%d\n",err);
			goto error;
		}
	}

	IPSEC_DEBUG(" Init SA success!\n");

	x->state = XFRM_STATE_VALID;

error:
	return err;
}

int xfrm_init_state(struct xfrm_state *x)
{
	int err;

	/* 现有代码 */
	err = __xfrm_init_state(x, true);
	if (err)
		return err;

	/* 如果启用了 cryptodev，创建 cryptodev 会话 */
#ifdef ENABLE_CRYPTODEV
	if (cryptodev_enabled) {
		err = xfrm_cryptodev_session_create(x);
		if (err) {
			/* 记录错误但继续使用软件加密 */
			IPSEC_DEBUG("Failed to create cryptodev session for SA: %d\n", err);
			/* 设置软件回退标志 */
			x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
		}
	}
#endif

	return 0;
}

int __net_init xfrm_state_init(struct net *net)
{
	unsigned int sz;

	sz = sizeof(struct hlist_head) * 8;
	net->xfrm.state_byspi = xfrm_hash_alloc(sz);
	if (!net->xfrm.state_byspi)
		goto out_byspi;
	net->xfrm.state_hmask = ((sz / sizeof(struct hlist_head)) - 1);

	net->xfrm.state_num = 0;
	return 0;

out_byspi:
	return -ENOMEM;
}


