/*
 * xfrm_cryptodev.c
 *
 * Description: DPDK cryptodev integration for xfrm framework
 */

/* 定义 _GNU_SOURCE 宏，用于 RTLD_DEFAULT */
#define _GNU_SOURCE

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>

#include <rte_mbuf.h>
#include <rte_version.h>
#include <dlfcn.h>

#include "xfrm_cryptodev.h"

/* 全局变量 */
struct xfrm_cryptodev_ctx xfrm_cryptodev_ctx;
/* 使用 xfrm_cryptodev_config.c 中定义的 cryptodev_enabled 变量 */
extern int cryptodev_enabled;
int cryptodev_error_policy = XFRM_CRYPTO_ERR_POLICY_FALLBACK;
/* 使用 xfrm_cryptodev_config.c 中定义的 crypto_stats 变量 */
extern struct xfrm_cryptodev_stats crypto_stats;

/* 使用 xfrm_cryptodev_config.h 中定义的 xfrm_cryptodev_stats 结构体 */

/* 全局统计变量 - 使用 xfrm_cryptodev_config.c 中定义的变量 */

/* 使用 xfrm_cryptodev.h 中定义的结构体 */

/* 加密算法映射表 */
static const struct xfrm_cipher_algo_map cipher_algo_map[] = {
    { "cbc(aes)", RTE_CRYPTO_CIPHER_AES_CBC, 16, 16, 16 },
    { "cbc(aes)", RTE_CRYPTO_CIPHER_AES_CBC, 24, 16, 16 },
    { "cbc(aes)", RTE_CRYPTO_CIPHER_AES_CBC, 32, 16, 16 },
    { "cbc(des3_ede)", RTE_CRYPTO_CIPHER_3DES_CBC, 24, 8, 8 },
    { "cbc(des)", RTE_CRYPTO_CIPHER_DES_CBC, 8, 8, 8 },
    { "rfc3686(ctr(aes))", RTE_CRYPTO_CIPHER_AES_CTR, 16, 8, 16 },
    { "rfc3686(ctr(aes))", RTE_CRYPTO_CIPHER_AES_CTR, 24, 8, 16 },
    { "rfc3686(ctr(aes))", RTE_CRYPTO_CIPHER_AES_CTR, 32, 8, 16 },
    { NULL, RTE_CRYPTO_CIPHER_LIST_END, 0, 0, 0 }
};

/* 认证算法映射表 */
static const struct xfrm_auth_algo_map auth_algo_map[] = {
    { "hmac(sha1)", RTE_CRYPTO_AUTH_SHA1_HMAC, 20, 20, 64 },
    { "hmac(sha256)", RTE_CRYPTO_AUTH_SHA256_HMAC, 32, 32, 64 },
    { "hmac(sha384)", RTE_CRYPTO_AUTH_SHA384_HMAC, 48, 48, 128 },
    { "hmac(sha512)", RTE_CRYPTO_AUTH_SHA512_HMAC, 64, 64, 128 },
    { "hmac(md5)", RTE_CRYPTO_AUTH_MD5_HMAC, 16, 16, 64 },
    { "xcbc(aes)", RTE_CRYPTO_AUTH_AES_XCBC_MAC, 16, 16, 16 },
    { NULL, RTE_CRYPTO_AUTH_LIST_END, 0, 0, 0 }
};

/* AEAD 算法映射表 */
static const struct xfrm_aead_algo_map aead_algo_map[] = {
    { "rfc4106(gcm(aes))", RTE_CRYPTO_AEAD_AES_GCM, 20, 16, 8, 8 },
    { "rfc4106(gcm(aes))", RTE_CRYPTO_AEAD_AES_GCM, 28, 16, 8, 8 },
    { "rfc4106(gcm(aes))", RTE_CRYPTO_AEAD_AES_GCM, 36, 16, 8, 8 },
    { "rfc4309(ccm(aes))", RTE_CRYPTO_AEAD_AES_CCM, 19, 16, 8, 8 },
    { "rfc4309(ccm(aes))", RTE_CRYPTO_AEAD_AES_CCM, 27, 16, 8, 8 },
    { "rfc4309(ccm(aes))", RTE_CRYPTO_AEAD_AES_CCM, 35, 16, 8, 8 },
    { NULL, RTE_CRYPTO_AEAD_LIST_END, 0, 0, 0, 0 }
};

/* 使用 xfrm_algo_map.c 中定义的函数 */

/* 更新统计信息的函数 - 使用 xfrm_cryptodev_config.c 中定义的函数 */
/* 这些函数在 xfrm_cryptodev_config.c 中已经定义，这里不再重复定义 */
extern void update_crypto_stats_submit(void);
extern void update_crypto_stats_complete(uint64_t process_time);
extern void update_crypto_stats_fail(void);

/* 使用 xfrm_cryptodev_config.c 中定义的函数 */
extern void update_crypto_stats_sw_fallback(void);

/* 辅助函数：从skb的cb数组获取重试计数 */
static inline int skb_get_crypto_retry_count(struct sk_buff *skb)
{
    /* 使用skb->cb数组的第一个字节存储重试计数 */
    return (int)skb->cb[0];
}

/* 辅助函数：设置skb的重试计数 */
static inline void skb_set_crypto_retry_count(struct sk_buff *skb, int count)
{
    /* 使用skb->cb数组的第一个字节存储重试计数 */
    skb->cb[0] = (unsigned char)count;
}

/* 错误处理函数（增强版） */
int xfrm_cryptodev_handle_error(struct xfrm_state *x, struct sk_buff *skb, int err)
{
    static unsigned long last_error_time = 0;
    static int consecutive_errors = 0;
    unsigned long current_time = jiffies;

    CRYPTO_ERROR("Cryptodev error occurred: %d for SA spi=%u", err, x->id.spi);

    /* 更新错误统计 */
    update_crypto_stats_fail();

    /* 检查错误类型和处理策略 */
    switch (err) {
    case -ENOTSUP:
        /* 不支持的操作，直接回退到软件 */
        CRYPTO_DEBUG("Operation not supported, falling back to software");
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        return -ENOTSUP;  /* 返回原始错误，调用方处理回退 */

    case -EBUSY:
    case -ENOMEM:
        /* 资源不足，可以重试 */
        if (time_after(current_time, last_error_time + HZ)) {
            consecutive_errors = 0;
        }
        consecutive_errors++;
        last_error_time = current_time;

        if (consecutive_errors >= CRYPTODEV_MAX_CONSECUTIVE_ERRORS) {
            CRYPTO_ERROR("Too many consecutive errors (%d), falling back to software",
                        consecutive_errors);
            x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
            consecutive_errors = 0;
            return err;  /* 返回原始错误，调用方处理回退 */
        }

        /* 可以重试 */
        return -EAGAIN;

    case -EINVAL:
    case -EIO:
    default:
        /* 严重错误，回退到软件 */
        CRYPTO_ERROR("Serious error (%d), falling back to software", err);
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        return err;  /* 返回原始错误，调用方处理回退 */
    }
}

    /* 错误频率控制 */
    if (time_after(current_time, last_error_time + HZ)) {
        consecutive_errors = 0;  /* 重置错误计数 */
    }
    consecutive_errors++;
    last_error_time = current_time;

    /* 如果连续错误过多，暂时禁用cryptodev */
    if (consecutive_errors > CRYPTODEV_MAX_CONSECUTIVE_ERRORS) {
        CRYPTO_ERROR("Too many consecutive errors (%d), temporarily disabling cryptodev",
                    consecutive_errors);
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_sw_fallback();
        return -ENOTSUP;
    }

    /* 根据错误类型进行处理 */
    switch (err) {
    case -EBUSY:
        /* 设备忙，可以重试 */
        CRYPTO_DEBUG("Device busy, will retry");
        return -EAGAIN;

    case -ENOMEM:
        /* 内存不足，回退到软件加密 */
        CRYPTO_WARNING("Out of memory, falling back to software crypto");
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_sw_fallback();
        return -ENOTSUP;

    case -ENODEV:
        /* 设备不可用，禁用cryptodev */
        CRYPTO_ERROR("Cryptodev not available, disabling");
        cryptodev_enabled = 0;
        return -ENOTSUP;

    case -EINVAL:
        /* 参数错误，可能是算法不支持 */
        CRYPTO_WARNING("Invalid parameters, marking SA for software fallback");
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_sw_fallback();
        return -ENOTSUP;

    case -ENOSPC:
        /* 队列满，稍后重试 */
        CRYPTO_DEBUG("Queue full, will retry later");
        return -EAGAIN;

    case -EIO:
        /* I/O错误，可能是硬件问题 */
        CRYPTO_ERROR("I/O error, falling back to software");
        x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
        update_crypto_stats_sw_fallback();
        return -ENOTSUP;

    default:
        /* 其他错误，根据策略处理 */
        switch (cryptodev_error_policy) {
        case XFRM_CRYPTO_ERR_POLICY_FALLBACK:
            x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
            update_crypto_stats_sw_fallback();
            CRYPTO_DEBUG("Falling back to software crypto for SA %p", x);
            return -ENOTSUP;

        case XFRM_CRYPTO_ERR_POLICY_RETRY:
            {
                int retry_count = skb_get_crypto_retry_count(skb);
                if (retry_count < 3) {
                    skb_set_crypto_retry_count(skb, retry_count + 1);
                    CRYPTO_DEBUG("Retrying crypto operation (attempt %d)", retry_count + 1);
                    return -EAGAIN;
                }
                /* 超过重试次数，回退到软件路径 */
                x->crypto_flags |= XFRM_CRYPTO_FLAG_SW_FALLBACK;
                update_crypto_stats_sw_fallback();
                CRYPTO_DEBUG("Max retries exceeded, falling back to software crypto");
                return -ENOTSUP;
            }

        case XFRM_CRYPTO_ERR_POLICY_DROP:
        default:
            /* 直接丢弃数据包 */
            CRYPTO_ERROR("Dropping packet due to crypto error: %d", err);
            kfree_skb(skb);
            return 0;  /* 指示调用者数据包已处理 */
        }
    }
}

/* 初始化队列对映射表 */
static int xfrm_cryptodev_init_qp_table(void)
{
    uint16_t nb_qps = 0;
    uint8_t cdev_id, nb_devs = 0;
    int openssl_found = 0;

    /* 计算设备数量和队列对总数 */
    for (cdev_id = 0; cdev_id < rte_cryptodev_count(); cdev_id++) {
        struct rte_cryptodev_info cdev_info;

        rte_cryptodev_info_get(cdev_id, &cdev_info);

        /* 检查是否是 OpenSSL PMD */
        if (strcmp(cdev_info.driver_name, "crypto_openssl") == 0) {
            CRYPTO_DEBUG("Found OpenSSL PMD at device %u\n", cdev_id);
            openssl_found = 1;
        }

        nb_qps += cdev_info.max_nb_queue_pairs;
        nb_devs++;
    }

    if (nb_devs == 0) {
        CRYPTO_ERROR("No cryptodev devices available\n");
        return -ENODEV;
    }

    /* 如果没有找到 OpenSSL PMD，打印警告 */
    if (!openssl_found) {
        CRYPTO_DEBUG("OpenSSL PMD not found. If no hardware crypto device is available, crypto operations may fail.\n");
    }

    /* 分配队列对表 */
    xfrm_cryptodev_ctx.qp_table = kmalloc(sizeof(struct xfrm_cryptodev_qp) * nb_qps, GFP_KERNEL);
    if (!xfrm_cryptodev_ctx.qp_table)
        return -ENOMEM;

    /* 分配设备 ID 列表 */
    xfrm_cryptodev_ctx.dev_ids = kmalloc(sizeof(uint8_t) * nb_devs, GFP_KERNEL);
    if (!xfrm_cryptodev_ctx.dev_ids) {
        kfree(xfrm_cryptodev_ctx.qp_table);
        return -ENOMEM;
    }

    /* 分配设备活动状态列表 */
    xfrm_cryptodev_ctx.dev_active = kmalloc(sizeof(uint8_t) * nb_devs, GFP_KERNEL);
    if (!xfrm_cryptodev_ctx.dev_active) {
        kfree(xfrm_cryptodev_ctx.dev_ids);
        kfree(xfrm_cryptodev_ctx.qp_table);
        return -ENOMEM;
    }

    /* 分配错误计数列表 */
    xfrm_cryptodev_ctx.last_error_count = kmalloc(sizeof(uint64_t) * nb_devs, GFP_KERNEL);
    if (!xfrm_cryptodev_ctx.last_error_count) {
        kfree(xfrm_cryptodev_ctx.dev_active);
        kfree(xfrm_cryptodev_ctx.dev_ids);
        kfree(xfrm_cryptodev_ctx.qp_table);
        return -ENOMEM;
    }

    /* 初始化设备列表 */
    nb_devs = 0;
    for (cdev_id = 0; cdev_id < rte_cryptodev_count(); cdev_id++) {
        xfrm_cryptodev_ctx.dev_ids[nb_devs] = cdev_id;
        xfrm_cryptodev_ctx.dev_active[nb_devs] = 1;
        xfrm_cryptodev_ctx.last_error_count[nb_devs] = 0;
        nb_devs++;
    }

    xfrm_cryptodev_ctx.nb_devs = nb_devs;

    return 0;
}

/* 释放队列对映射表 */
static void xfrm_cryptodev_free_qp_table(void)
{
    kfree(xfrm_cryptodev_ctx.last_error_count);
    kfree(xfrm_cryptodev_ctx.dev_active);
    kfree(xfrm_cryptodev_ctx.dev_ids);
    kfree(xfrm_cryptodev_ctx.qp_table);
}

/* 初始化算法映射表 */
static int xfrm_cryptodev_init_algo_map(void)
{
    struct rte_hash_parameters params = { 0 };

    params.entries = CRYPTODEV_MAP_ENTRIES;
    params.key_len = sizeof(struct xfrm_cdev_key);
    params.hash_func = rte_jhash;
    params.hash_func_init_val = 0;
    params.socket_id = rte_socket_id();
    params.name = "xfrm_crypto_algo_map";

    xfrm_cryptodev_ctx.algo_map = rte_hash_create(&params);
    if (!xfrm_cryptodev_ctx.algo_map)
        return -ENOMEM;

    return 0;
}

/* 创建 OpenSSL cryptodev 设备 */
static int xfrm_cryptodev_create_openssl_device(void)
{
    uint8_t dev_id;
    int ret;

    /* 检查是否已经有 OpenSSL 设备 */
    for (dev_id = 0; dev_id < rte_cryptodev_count(); dev_id++) {
        struct rte_cryptodev_info info;
        rte_cryptodev_info_get(dev_id, &info);
        if (strcmp(info.driver_name, "crypto_openssl") == 0) {
            CRYPTO_DEBUG("OpenSSL PMD already exists at device %u\n", dev_id);
            return 0;
        }
    }

    /* 创建 OpenSSL 设备 */
    CRYPTO_DEBUG("Creating OpenSSL PMD device\n");

    /* 检查 rte_vdev_init 函数是否可用 */
    void *vdev_func = dlsym(RTLD_DEFAULT, "rte_vdev_init");
    if (!vdev_func) {
        CRYPTO_ERROR("rte_vdev_init function is not available\n");
        CRYPTO_ERROR("DPDK was compiled without vdev support\n");
        CRYPTO_ERROR("Cannot create virtual cryptodev devices\n");
        return -ENOTSUP;  /* 返回不支持的错误 */
    }

    ret = rte_vdev_init("crypto_openssl", NULL);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to create OpenSSL PMD device: %d\n", ret);
        return ret;
    }

    CRYPTO_DEBUG("OpenSSL PMD device created successfully\n");
    return 0;
}

/* 初始化 xfrm cryptodev 上下文（由统一初始化函数调用） */
int xfrm_cryptodev_init_ctx(struct xfrm_cryptodev_ctx *ctx)
{
    int ret;

    CRYPTO_DEBUG("Initializing xfrm cryptodev context\n");

    if (!ctx) {
        CRYPTO_ERROR("Invalid context pointer\n");
        return -EINVAL;
    }

    /* 复制上下文 */
    memcpy(&xfrm_cryptodev_ctx, ctx, sizeof(xfrm_cryptodev_ctx));

    /* 初始化队列对映射表 */
    ret = xfrm_cryptodev_init_qp_table();
    if (ret) {
        CRYPTO_ERROR("Failed to initialize QP table: %d\n", ret);
        return ret;
    }

    /* 初始化算法映射表 */
    ret = xfrm_cryptodev_init_algo_map();
    if (ret) {
        CRYPTO_ERROR("Failed to initialize algorithm map: %d\n", ret);
        xfrm_cryptodev_free_qp_table();
        return ret;
    }

    /* 验证内存池已由统一初始化函数创建 */
    if (!xfrm_cryptodev_ctx.session_pool) {
        CRYPTO_ERROR("Session pool not initialized by unified init\n");
        xfrm_cryptodev_free_algo_map();
        xfrm_cryptodev_free_qp_table();
        return -EINVAL;
    }

    if (!xfrm_cryptodev_ctx.op_pool) {
        CRYPTO_ERROR("Operation pool not initialized by unified init\n");
        xfrm_cryptodev_free_algo_map();
        xfrm_cryptodev_free_qp_table();
        return -EINVAL;
    }

    /* 初始化统计信息 */
    reset_crypto_stats();

    CRYPTO_DEBUG("Cryptodev initialization complete\n");

    return 0;
}

/* 清理 xfrm cryptodev 上下文（由统一清理函数调用） */
void xfrm_cryptodev_uninit_ctx(void)
{
    CRYPTO_DEBUG("Uninitializing xfrm cryptodev context\n");

    /* 清理xfrm特定资源 */
    if (xfrm_cryptodev_ctx.algo_map) {
        rte_hash_free(xfrm_cryptodev_ctx.algo_map);
        xfrm_cryptodev_ctx.algo_map = NULL;
    }
    xfrm_cryptodev_free_qp_table();

    memset(&xfrm_cryptodev_ctx, 0, sizeof(xfrm_cryptodev_ctx));
}

/* 显示 cryptodev 状态 */
void xfrm_cryptodev_show_status(void)
{
    int i;

    printf("Cryptodev status:\n");
    printf("  Enabled: %s\n", cryptodev_enabled ? "yes" : "no");
    printf("  Number of devices: %d\n", xfrm_cryptodev_ctx.nb_devs);
    printf("  Number of queue pairs: %d\n", xfrm_cryptodev_ctx.nb_qps);

    printf("  Devices:\n");
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        struct rte_cryptodev_info info;
        uint8_t dev_id = xfrm_cryptodev_ctx.dev_ids[i];

        rte_cryptodev_info_get(dev_id, &info);
        printf("    Device %d\n", dev_id);
        printf("      Driver: %s\n", info.driver_name);
        printf("      Max sessions: %d\n", info.sym.max_nb_sessions);
        printf("      Active: %s\n", xfrm_cryptodev_ctx.dev_active[i] ? "yes" : "no");
    }
}

/* 显示 cryptodev 性能统计 */
void xfrm_cryptodev_show_perf_stats(void)
{
    /* 使用 xfrm_cryptodev_config.c 中的 print_crypto_stats 函数 */
    print_crypto_stats();
}

/* 设置 cryptodev 启用状态 */
int xfrm_cryptodev_set_enabled(int enabled)
{
    if (enabled && !cryptodev_enabled) {
        /* 启用 cryptodev */
        int ret = xfrm_cryptodev_init();
        if (ret)
            return ret;
        cryptodev_enabled = 1;
    } else if (!enabled && cryptodev_enabled) {
        /* 禁用 cryptodev */
        xfrm_cryptodev_uninit();
        cryptodev_enabled = 0;
    }

    return 0;
}
