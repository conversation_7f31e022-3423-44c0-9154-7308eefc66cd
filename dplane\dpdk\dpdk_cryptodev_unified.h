/*
 * dpdk_cryptodev_unified.h
 *
 * Description: 统一的DPDK cryptodev初始化头文件
 */

#ifndef __DPDK_CRYPTODEV_UNIFIED_H__
#define __DPDK_CRYPTODEV_UNIFIED_H__

#include <rte_cryptodev.h>
#include <rte_mempool.h>

/* 函数声明 */

/**
 * 统一的cryptodev初始化函数
 * 
 * 这个函数整合了原来分散在多个文件中的初始化逻辑：
 * - 创建cryptodev设备（OpenSSL PMD）
 * - 创建内存池（会话池和操作池）
 * - 配置和启动设备
 * - 初始化xfrm cryptodev上下文
 * 
 * @return 0 成功，负数表示错误
 */
int unified_cryptodev_init(void);

/**
 * 统一的cryptodev清理函数
 * 
 * 清理所有cryptodev相关资源
 */
void unified_cryptodev_uninit(void);

/**
 * 获取会话池指针
 * 
 * @return 会话池指针，如果未初始化则返回NULL
 */
struct rte_mempool *unified_get_session_pool(void);

/**
 * 获取操作池指针
 *
 * @return 操作池指针，如果未初始化则返回NULL
 */
struct rte_mempool *unified_get_op_pool(void);

/**
 * 获取mbuf池指针
 *
 * @return mbuf池指针，如果未初始化则返回NULL
 */
struct rte_mempool *unified_get_mbuf_pool(void);

/**
 * 检查cryptodev是否已初始化
 * 
 * @return 1 已初始化，0 未初始化
 */
int unified_cryptodev_is_initialized(void);

#endif /* __DPDK_CRYPTODEV_UNIFIED_H__ */
