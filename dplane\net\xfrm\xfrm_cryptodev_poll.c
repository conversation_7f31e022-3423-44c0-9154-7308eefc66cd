/*
 * xfrm_cryptodev_poll.c
 *
 * Description: DPDK cryptodev polling for xfrm framework
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"

/* 设备健康检查 */
void xfrm_cryptodev_health_check(void)
{
    int i;

    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        uint8_t dev_id = xfrm_cryptodev_ctx.dev_ids[i];
        struct rte_cryptodev_stats stats;

        /* 获取设备统计信息 */
        if (rte_cryptodev_stats_get(dev_id, &stats) < 0) {
            CRYPTO_ERROR("Failed to get stats for device %d\n", dev_id);
            continue;
        }

        /* 检查错误计数 - 注意：在某些版本的 DPDK 中，rte_cryptodev_stats 结构体可能没有 error_count 成员 */
        /* 使用 dequeue_count - enqueue_count 作为错误指标 */
        uint64_t error_estimate = 0;
        if (stats.dequeued_count < stats.enqueued_count) {
            error_estimate = stats.enqueued_count - stats.dequeued_count;
        }

        if (error_estimate > xfrm_cryptodev_ctx.last_error_count[i]) {
            uint64_t new_errors = error_estimate - xfrm_cryptodev_ctx.last_error_count[i];

            CRYPTO_ERROR("Device %d reported estimated %"PRIu64" new errors\n",
                        dev_id, new_errors);

            /* 如果错误太多，尝试重置设备 */
            if (new_errors > CRYPTODEV_ERROR_THRESHOLD) {
                CRYPTO_ERROR("Attempting to reset device %d\n", dev_id);

                /* 停止设备 */
                rte_cryptodev_stop(dev_id);

                /* 重新配置设备 */
                struct rte_cryptodev_config config = {
                    .nb_queue_pairs = 1,
                    .socket_id = rte_socket_id()
                };

                struct rte_cryptodev_qp_conf qp_conf = {
                    .nb_descriptors = CRYPTODEV_QUEUE_SIZE,
                    .mp_session = xfrm_cryptodev_ctx.session_pool
                };

                /* 重新配置设备 */
                if (rte_cryptodev_configure(dev_id, &config) < 0) {
                    CRYPTO_ERROR("Failed to reconfigure device %d\n", dev_id);
                    xfrm_cryptodev_ctx.dev_active[i] = 0;
                    continue;
                }

                /* 重新配置队列对 */
                if (rte_cryptodev_queue_pair_setup(dev_id, 0, &qp_conf, rte_socket_id()) < 0) {
                    CRYPTO_ERROR("Failed to setup queue pair for device %d\n", dev_id);
                    xfrm_cryptodev_ctx.dev_active[i] = 0;
                    continue;
                }

                /* 重新启动设备 */
                if (rte_cryptodev_start(dev_id) < 0) {
                    CRYPTO_ERROR("Failed to restart device %d\n", dev_id);
                    /* 标记设备不可用 */
                    xfrm_cryptodev_ctx.dev_active[i] = 0;
                } else {
                    CRYPTO_DEBUG("Successfully restarted device %d\n", dev_id);
                }
            }
        }

        /* 更新上次错误计数 */
        xfrm_cryptodev_ctx.last_error_count[i] = error_estimate;
    }
}

/* 轮询加密设备并处理完成的操作 */
int xfrm_cryptodev_poll(void)
{
    int i;
    uint16_t nb_ops;
    int total_ops = 0;

    /* 轮询所有队列对 */
    for (i = 0; i < xfrm_cryptodev_ctx.nb_qps; i++) {
        struct xfrm_cryptodev_qp *qp = &xfrm_cryptodev_ctx.qp_table[i];

        /* 检查设备是否活动 */
        if (!xfrm_cryptodev_ctx.dev_active[i / CRYPTODEV_MAX_QPS])
            continue;

        /* 出队完成的操作 */
        nb_ops = xfrm_cryptodev_dequeue_burst(qp);

        /* 处理完成的操作 */
        if (nb_ops > 0) {
            xfrm_cryptodev_process_completed(qp, nb_ops);
            total_ops += nb_ops;
        }
    }

    /* 定期进行健康检查 */
    static int health_check_counter = 0;
    if (++health_check_counter >= CRYPTODEV_HEALTH_CHECK_INTERVAL) {
        xfrm_cryptodev_health_check();
        health_check_counter = 0;
    }

    return total_ops;
}
