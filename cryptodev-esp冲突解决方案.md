# Cryptodev与ESP冲突解决方案

## 📋 解决思路

**核心思想**: 通过全局配置来区分cryptodev和ESP的处理流程，两者可以共用数据结构，但处理逻辑完全分离。

## 🔧 实现方案

### **方案1：全局配置控制**

#### 1.1 添加全局配置变量
```c
// 在 dplane/net/xfrm/xfrm_cryptodev_config.h 中添加
typedef enum {
    XFRM_CRYPTO_MODE_SOFTWARE = 0,  /* 使用原有ESP软件实现 */
    XFRM_CRYPTO_MODE_CRYPTODEV = 1,  /* 使用cryptodev硬件加速 */
    XFRM_CRYPTO_MODE_AUTO = 2        /* 自动选择（优先硬件） */
} xfrm_crypto_mode_t;

/* 全局配置变量 */
extern xfrm_crypto_mode_t xfrm_crypto_mode;
extern int cryptodev_enabled;

/* 配置函数 */
int xfrm_set_crypto_mode(xfrm_crypto_mode_t mode);
xfrm_crypto_mode_t xfrm_get_crypto_mode(void);
```

#### 1.2 实现配置管理
```c
// 在 dplane/net/xfrm/xfrm_cryptodev_config.c 中实现
xfrm_crypto_mode_t xfrm_crypto_mode = XFRM_CRYPTO_MODE_SOFTWARE;  /* 默认软件模式 */

int xfrm_set_crypto_mode(xfrm_crypto_mode_t mode)
{
    switch (mode) {
    case XFRM_CRYPTO_MODE_SOFTWARE:
        xfrm_crypto_mode = mode;
        cryptodev_enabled = 0;
        printk(KERN_INFO "XFRM: Switched to software crypto mode\n");
        break;
        
    case XFRM_CRYPTO_MODE_CRYPTODEV:
        if (!cryptodev_available()) {
            printk(KERN_WARNING "XFRM: Cryptodev not available, staying in software mode\n");
            return -ENODEV;
        }
        xfrm_crypto_mode = mode;
        cryptodev_enabled = 1;
        printk(KERN_INFO "XFRM: Switched to cryptodev hardware mode\n");
        break;
        
    case XFRM_CRYPTO_MODE_AUTO:
        xfrm_crypto_mode = mode;
        cryptodev_enabled = cryptodev_available() ? 1 : 0;
        printk(KERN_INFO "XFRM: Auto mode - using %s\n", 
               cryptodev_enabled ? "cryptodev" : "software");
        break;
        
    default:
        return -EINVAL;
    }
    
    return 0;
}

xfrm_crypto_mode_t xfrm_get_crypto_mode(void)
{
    return xfrm_crypto_mode;
}
```

### **方案2：处理流程分离**

#### 2.1 修改xfrm_input.c的处理逻辑
```c
// 在 dplane/net/xfrm/xfrm_input.c 中修改
static int xfrm_input_one(struct sk_buff *skb, int nexthdr)
{
    // ... 原有代码 ...
    
    /* 根据全局配置选择处理路径 */
    switch (xfrm_crypto_mode) {
    case XFRM_CRYPTO_MODE_CRYPTODEV:
        /* 纯cryptodev模式 - x->context存储cryptodev会话 */
        if (x->context && cryptodev_enabled) {
            int ret = xfrm_cryptodev_decrypt(x, skb);
            if (ret == 0) {
                /* cryptodev处理成功 */
                goto crypto_success;
            } else if (ret == -EINPROGRESS) {
                /* 异步处理中 */
                xfrm_state_hold(x);
                return 0;
            } else {
                /* cryptodev失败，记录错误但继续软件路径 */
                IPSEC_DEBUG("Cryptodev decrypt failed: %d, falling back to software\n", ret);
                /* 清理cryptodev上下文，准备软件处理 */
                xfrm_cryptodev_cleanup_context(x);
            }
        }
        /* 如果没有cryptodev会话或失败，回退到软件 */
        break;
        
    case XFRM_CRYPTO_MODE_AUTO:
        /* 自动模式 - 优先尝试cryptodev */
        if (x->context && cryptodev_enabled && xfrm_has_cryptodev_session(x)) {
            int ret = xfrm_cryptodev_decrypt(x, skb);
            if (ret == 0) {
                goto crypto_success;
            } else if (ret == -EINPROGRESS) {
                xfrm_state_hold(x);
                return 0;
            }
            /* cryptodev失败，继续软件路径 */
            IPSEC_DEBUG("Cryptodev failed, using software fallback\n");
        }
        break;
        
    case XFRM_CRYPTO_MODE_SOFTWARE:
    default:
        /* 纯软件模式 - x->context存储ESP上下文 */
        break;
    }
    
    /* 软件处理路径 - 原有ESP实现 */
    nexthdr = x->type->input(x, skb);
    
crypto_success:
    // ... 后续处理 ...
}
```

#### 2.2 修改xfrm_output.c的处理逻辑
```c
// 在 dplane/net/xfrm/xfrm_output.c 中修改
static int xfrm_output_one(struct sk_buff *skb, int err)
{
    // ... 原有代码 ...
    
    /* 根据全局配置选择处理路径 */
    switch (xfrm_crypto_mode) {
    case XFRM_CRYPTO_MODE_CRYPTODEV:
        /* 纯cryptodev模式 */
        if (x->context && cryptodev_enabled) {
            err = xfrm_cryptodev_encrypt(x, skb);
            if (err == 0) {
                goto crypto_success;
            } else if (err == -EINPROGRESS) {
                xfrm_state_hold(x);
                return 0;
            } else {
                IPSEC_DEBUG("Cryptodev encrypt failed: %d, falling back to software\n", err);
                xfrm_cryptodev_cleanup_context(x);
            }
        }
        break;
        
    case XFRM_CRYPTO_MODE_AUTO:
        /* 自动模式 */
        if (x->context && cryptodev_enabled && xfrm_has_cryptodev_session(x)) {
            err = xfrm_cryptodev_encrypt(x, skb);
            if (err == 0) {
                goto crypto_success;
            } else if (err == -EINPROGRESS) {
                xfrm_state_hold(x);
                return 0;
            }
            IPSEC_DEBUG("Cryptodev failed, using software fallback\n");
        }
        break;
        
    case XFRM_CRYPTO_MODE_SOFTWARE:
    default:
        /* 纯软件模式 */
        break;
    }
    
    /* 软件处理路径 - 原有ESP实现 */
    err = x->type->output(x, skb);
    
crypto_success:
    // ... 后续处理 ...
}
```

### **方案3：上下文管理策略**

#### 3.1 添加上下文类型标识
```c
// 在 dplane/net/xfrm/xfrm_cryptodev.h 中添加
#define XFRM_CONTEXT_TYPE_ESP       0x45535000  /* 'ESP\0' */
#define XFRM_CONTEXT_TYPE_CRYPTODEV 0x43525950  /* 'CRYP' */

/* 通用上下文头部 */
struct xfrm_context_header {
    uint32_t type;      /* 上下文类型标识 */
    uint32_t size;      /* 上下文大小 */
};

/* cryptodev上下文包装 */
struct xfrm_cryptodev_context {
    struct xfrm_context_header header;
    struct xfrm_cryptodev_session *session;
    /* 其他cryptodev相关数据 */
};
```

#### 3.2 实现上下文检查函数
```c
// 在 dplane/net/xfrm/xfrm_cryptodev.c 中实现
bool xfrm_has_cryptodev_session(struct xfrm_state *x)
{
    struct xfrm_context_header *header;
    
    if (!x->context) {
        return false;
    }
    
    header = (struct xfrm_context_header *)x->context;
    return (header->type == XFRM_CONTEXT_TYPE_CRYPTODEV);
}

struct xfrm_cryptodev_session *xfrm_get_cryptodev_session(struct xfrm_state *x)
{
    struct xfrm_cryptodev_context *ctx;
    
    if (!xfrm_has_cryptodev_session(x)) {
        return NULL;
    }
    
    ctx = (struct xfrm_cryptodev_context *)x->context;
    return ctx->session;
}

void xfrm_cryptodev_cleanup_context(struct xfrm_state *x)
{
    if (xfrm_has_cryptodev_session(x)) {
        /* 清理cryptodev上下文，为软件处理做准备 */
        kfree(x->context);
        x->context = NULL;
    }
}
```

### **方案4：配置接口**

#### 4.1 命令行配置接口
```c
// 在 dplane/net/xfrm/xfrm_cryptodev_cli.c 中添加
static ssize_t crypto_mode_store(struct device *dev,
                                 struct device_attribute *attr,
                                 const char *buf, size_t count)
{
    xfrm_crypto_mode_t mode;
    
    if (strncmp(buf, "software", 8) == 0) {
        mode = XFRM_CRYPTO_MODE_SOFTWARE;
    } else if (strncmp(buf, "cryptodev", 9) == 0) {
        mode = XFRM_CRYPTO_MODE_CRYPTODEV;
    } else if (strncmp(buf, "auto", 4) == 0) {
        mode = XFRM_CRYPTO_MODE_AUTO;
    } else {
        return -EINVAL;
    }
    
    int ret = xfrm_set_crypto_mode(mode);
    return ret ? ret : count;
}

static ssize_t crypto_mode_show(struct device *dev,
                               struct device_attribute *attr,
                               char *buf)
{
    const char *mode_str;
    
    switch (xfrm_get_crypto_mode()) {
    case XFRM_CRYPTO_MODE_SOFTWARE:
        mode_str = "software";
        break;
    case XFRM_CRYPTO_MODE_CRYPTODEV:
        mode_str = "cryptodev";
        break;
    case XFRM_CRYPTO_MODE_AUTO:
        mode_str = "auto";
        break;
    default:
        mode_str = "unknown";
        break;
    }
    
    return sprintf(buf, "%s\n", mode_str);
}

static DEVICE_ATTR(crypto_mode, 0644, crypto_mode_show, crypto_mode_store);
```

#### 4.2 运行时切换支持
```c
// 支持运行时动态切换模式
int xfrm_switch_crypto_mode(xfrm_crypto_mode_t new_mode)
{
    xfrm_crypto_mode_t old_mode = xfrm_crypto_mode;
    int ret;
    
    /* 设置新模式 */
    ret = xfrm_set_crypto_mode(new_mode);
    if (ret < 0) {
        return ret;
    }
    
    /* 如果从cryptodev切换到软件模式，清理现有的cryptodev会话 */
    if (old_mode != XFRM_CRYPTO_MODE_SOFTWARE && 
        new_mode == XFRM_CRYPTO_MODE_SOFTWARE) {
        xfrm_cryptodev_cleanup_all_sessions();
    }
    
    return 0;
}
```

## 🎯 优势分析

### **1. 完全隔离**
- ESP和cryptodev处理流程完全分离
- 不会相互干扰
- 可以独立测试和调试

### **2. 灵活配置**
- 支持三种模式：纯软件、纯硬件、自动选择
- 支持运行时动态切换
- 支持命令行配置

### **3. 向后兼容**
- 默认使用软件模式，保持原有行为
- 不影响现有ESP功能
- 渐进式迁移

### **4. 容错能力**
- cryptodev失败时自动回退到软件
- 错误处理清晰
- 系统稳定性高

## 📅 实施步骤

### **第1步：添加配置框架**
1. 添加全局配置变量和枚举
2. 实现配置管理函数
3. 添加命令行接口

### **第2步：修改处理流程**
1. 修改xfrm_input.c的处理逻辑
2. 修改xfrm_output.c的处理逻辑
3. 添加上下文检查函数

### **第3步：测试验证**
1. 测试纯软件模式（默认）
2. 测试纯cryptodev模式
3. 测试自动模式和回退机制

---

**方案优点**: 简单、清晰、安全、兼容  
**实施难度**: 低  
**风险等级**: 极低  
**推荐指数**: ⭐⭐⭐⭐⭐  
