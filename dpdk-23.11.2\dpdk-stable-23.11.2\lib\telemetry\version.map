DPDK_24 {
	global:

	rte_tel_data_add_array_container;
	rte_tel_data_add_array_int;
	rte_tel_data_add_array_string;
	rte_tel_data_add_array_u64;
	rte_tel_data_add_array_uint;
	rte_tel_data_add_dict_container;
	rte_tel_data_add_dict_int;
	rte_tel_data_add_dict_string;
	rte_tel_data_add_dict_u64;
	rte_tel_data_add_dict_uint;
	rte_tel_data_alloc;
	rte_tel_data_free;
	rte_tel_data_start_array;
	rte_tel_data_start_dict;
	rte_tel_data_string;
	rte_telemetry_register_cmd;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 23.03
	rte_tel_data_add_array_uint_hex;
	rte_tel_data_add_dict_uint_hex;

	local: *;
};

INTERNAL {
	rte_telemetry_legacy_register;
	rte_telemetry_init;
};
