DPDK_24 {
	global:

	__rte_panic;
	eal_parse_sysfs_value; # WINDOWS_NO_EXPORT
	eal_timer_source; # WINDOWS_NO_EXPORT
	per_lcore__lcore_id;
	per_lcore__rte_errno;
	per_lcore__thread_id;
	rte_bus_dump;
	rte_bus_find;
	rte_bus_find_by_device;
	rte_bus_find_by_name;
	rte_bus_get_iommu_class;
	rte_bus_name;
	rte_bus_probe;
	rte_bus_scan;
	rte_calloc;
	rte_calloc_socket;
	rte_class_find;
	rte_class_find_by_name;
	rte_class_register;
	rte_class_unregister;
	rte_cpu_get_flag_enabled;
	rte_cpu_get_flag_name;
	rte_cpu_is_supported; # WINDOWS_NO_EXPORT
	rte_cycles_vmware_tsc_map; # WINDOWS_NO_EXPORT
	rte_delay_us;
	rte_delay_us_block;
	rte_delay_us_callback_register;
	rte_delay_us_sleep;
	rte_dev_bus;
	rte_dev_bus_info;
	rte_dev_devargs;
	rte_dev_dma_map;
	rte_dev_dma_unmap;
	rte_dev_driver;
	rte_dev_event_callback_process;
	rte_dev_event_callback_register;
	rte_dev_event_callback_unregister;
	rte_dev_event_monitor_start;
	rte_dev_event_monitor_stop;
	rte_dev_hotplug_handle_disable;
	rte_dev_hotplug_handle_enable;
	rte_dev_is_probed;
	rte_dev_iterator_init;
	rte_dev_iterator_next;
	rte_dev_name;
	rte_dev_numa_node;
	rte_dev_probe;
	rte_dev_remove;
	rte_devargs_add;
	rte_devargs_dump;
	rte_devargs_insert;
	rte_devargs_next;
	rte_devargs_parse;
	rte_devargs_parsef;
	rte_devargs_remove;
	rte_devargs_reset;
	rte_devargs_type_count;
	rte_drand;
	rte_driver_name;
	rte_dump_physmem_layout;
	rte_dump_stack;
	rte_dump_tailq;
	rte_eal_alarm_cancel;
	rte_eal_alarm_set;
	rte_eal_cleanup;
	rte_eal_create_uio_dev; # WINDOWS_NO_EXPORT
	rte_eal_get_lcore_state;
	rte_eal_get_physmem_size;
	rte_eal_get_runtime_dir;
	rte_eal_has_hugepages;
	rte_eal_has_pci;
	rte_eal_hotplug_add;
	rte_eal_hotplug_remove;
	rte_eal_hpet_init; # WINDOWS_NO_EXPORT
	rte_eal_init;
	rte_eal_iopl_init; # WINDOWS_NO_EXPORT
	rte_eal_iova_mode;
	rte_eal_lcore_role;
	rte_eal_mbuf_user_pool_ops;
	rte_eal_mp_remote_launch;
	rte_eal_mp_wait_lcore;
	rte_eal_primary_proc_alive; # WINDOWS_NO_EXPORT
	rte_eal_process_type;
	rte_eal_remote_launch;
	rte_eal_tailq_lookup;
	rte_eal_tailq_register;
	rte_eal_using_phys_addrs;
	rte_eal_vfio_get_vf_token; # WINDOWS_NO_EXPORT
	rte_eal_vfio_intr_mode; # WINDOWS_NO_EXPORT
	rte_eal_wait_lcore;
	rte_epoll_ctl;
	rte_epoll_wait;
	rte_epoll_wait_interruptible;
	rte_exit;
	rte_extmem_attach;
	rte_extmem_detach;
	rte_extmem_register;
	rte_extmem_unregister;
	rte_fbarray_attach;
	rte_fbarray_destroy;
	rte_fbarray_detach;
	rte_fbarray_dump_metadata;
	rte_fbarray_find_biggest_free;
	rte_fbarray_find_biggest_used;
	rte_fbarray_find_contig_free;
	rte_fbarray_find_contig_used;
	rte_fbarray_find_idx;
	rte_fbarray_find_next_free;
	rte_fbarray_find_next_n_free;
	rte_fbarray_find_next_n_used;
	rte_fbarray_find_next_used;
	rte_fbarray_find_prev_free;
	rte_fbarray_find_prev_n_free;
	rte_fbarray_find_prev_n_used;
	rte_fbarray_find_prev_used;
	rte_fbarray_find_rev_biggest_free;
	rte_fbarray_find_rev_biggest_used;
	rte_fbarray_find_rev_contig_free;
	rte_fbarray_find_rev_contig_used;
	rte_fbarray_get;
	rte_fbarray_init;
	rte_fbarray_is_used;
	rte_fbarray_set_free;
	rte_fbarray_set_used;
	rte_free;
	rte_get_hpet_cycles; # WINDOWS_NO_EXPORT
	rte_get_hpet_hz; # WINDOWS_NO_EXPORT
	rte_get_main_lcore;
	rte_get_next_lcore;
	rte_get_tsc_hz;
	rte_hexdump;
	rte_hypervisor_get;
	rte_hypervisor_get_name; # WINDOWS_NO_EXPORT
	rte_intr_ack;
	rte_intr_callback_register;
	rte_intr_callback_unregister;
	rte_intr_callback_unregister_pending;
	rte_intr_callback_unregister_sync;
	rte_intr_disable;
	rte_intr_enable;
	rte_intr_fd_get;
	rte_intr_fd_set;
	rte_intr_instance_alloc;
	rte_intr_instance_free;
	rte_intr_type_get;
	rte_intr_type_set;
	rte_keepalive_create; # WINDOWS_NO_EXPORT
	rte_keepalive_dispatch_pings; # WINDOWS_NO_EXPORT
	rte_keepalive_mark_alive; # WINDOWS_NO_EXPORT
	rte_keepalive_mark_sleep; # WINDOWS_NO_EXPORT
	rte_keepalive_register_core; # WINDOWS_NO_EXPORT
	rte_keepalive_register_relay_callback; # WINDOWS_NO_EXPORT
	rte_lcore_callback_register;
	rte_lcore_callback_unregister;
	rte_lcore_count;
	rte_lcore_cpuset;
	rte_lcore_dump;
	rte_lcore_has_role;
	rte_lcore_index;
	rte_lcore_is_enabled;
	rte_lcore_iterate;
	rte_lcore_to_cpu_id;
	rte_lcore_to_socket_id;
	rte_malloc;
	rte_malloc_dump_heaps;
	rte_malloc_dump_stats;
	rte_malloc_get_socket_stats;
	rte_malloc_heap_create;
	rte_malloc_heap_destroy;
	rte_malloc_heap_get_socket;
	rte_malloc_heap_memory_add;
	rte_malloc_heap_memory_attach;
	rte_malloc_heap_memory_detach;
	rte_malloc_heap_memory_remove;
	rte_malloc_heap_socket_is_external;
	rte_malloc_socket;
	rte_malloc_validate;
	rte_malloc_virt2iova;
	rte_mcfg_get_single_file_segments;
	rte_mcfg_mem_read_lock;
	rte_mcfg_mem_read_unlock;
	rte_mcfg_mem_write_lock;
	rte_mcfg_mem_write_unlock;
	rte_mcfg_mempool_read_lock;
	rte_mcfg_mempool_read_unlock;
	rte_mcfg_mempool_write_lock;
	rte_mcfg_mempool_write_unlock;
	rte_mcfg_tailq_read_lock;
	rte_mcfg_tailq_read_unlock;
	rte_mcfg_tailq_write_lock;
	rte_mcfg_tailq_write_unlock;
	rte_mcfg_timer_lock;
	rte_mcfg_timer_unlock;
	rte_mem_alloc_validator_register;
	rte_mem_alloc_validator_unregister;
	rte_mem_check_dma_mask;
	rte_mem_check_dma_mask_thread_unsafe;
	rte_mem_event_callback_register;
	rte_mem_event_callback_unregister;
	rte_mem_iova2virt;
	rte_mem_lock_page;
	rte_mem_set_dma_mask;
	rte_mem_virt2iova;
	rte_mem_virt2memseg;
	rte_mem_virt2memseg_list;
	rte_mem_virt2phy;
	rte_memdump;
	rte_memory_get_nchannel;
	rte_memory_get_nrank;
	rte_memseg_contig_walk;
	rte_memseg_contig_walk_thread_unsafe;
	rte_memseg_get_fd;
	rte_memseg_get_fd_offset;
	rte_memseg_get_fd_offset_thread_unsafe;
	rte_memseg_get_fd_thread_unsafe;
	rte_memseg_list_walk;
	rte_memseg_list_walk_thread_unsafe;
	rte_memseg_walk;
	rte_memseg_walk_thread_unsafe;
	rte_memzone_dump;
	rte_memzone_free;
	rte_memzone_lookup;
	rte_memzone_reserve;
	rte_memzone_reserve_aligned;
	rte_memzone_reserve_bounded;
	rte_memzone_walk;
	rte_mp_action_register;
	rte_mp_action_unregister;
	rte_mp_disable;
	rte_mp_reply;
	rte_mp_request_async;
	rte_mp_request_sync;
	rte_mp_sendmsg;
	rte_power_monitor; # WINDOWS_NO_EXPORT
	rte_power_monitor_multi; # WINDOWS_NO_EXPORT
	rte_power_monitor_wakeup; # WINDOWS_NO_EXPORT
	rte_power_pause; # WINDOWS_NO_EXPORT
	rte_rand;
	rte_rand_max;
	rte_realloc;
	rte_realloc_socket;
	rte_reciprocal_value;
	rte_reciprocal_value_u64;
	rte_rtm_supported;
	rte_service_attr_get;
	rte_service_attr_reset_all;
	rte_service_component_register;
	rte_service_component_runstate_set;
	rte_service_component_unregister;
	rte_service_dump;
	rte_service_finalize;
	rte_service_get_by_name;
	rte_service_get_count;
	rte_service_get_name;
	rte_service_lcore_add;
	rte_service_lcore_attr_get;
	rte_service_lcore_attr_reset_all;
	rte_service_lcore_count;
	rte_service_lcore_count_services;
	rte_service_lcore_del;
	rte_service_lcore_list;
	rte_service_lcore_may_be_active;
	rte_service_lcore_reset_all;
	rte_service_lcore_start;
	rte_service_lcore_stop;
	rte_service_map_lcore_get;
	rte_service_map_lcore_set;
	rte_service_may_be_active;
	rte_service_probe_capability;
	rte_service_run_iter_on_app_lcore;
	rte_service_runstate_get;
	rte_service_runstate_set;
	rte_service_set_runstate_mapped_check;
	rte_service_set_stats_enable;
	rte_service_start_with_defaults;
	rte_set_application_usage_hook;
	rte_socket_count;
	rte_socket_id;
	rte_socket_id_by_idx;
	rte_srand;
	rte_str_to_size;
	rte_strerror;
	rte_strscpy;
	rte_strsplit;
	rte_sys_gettid;
	rte_thread_attr_get_affinity;
	rte_thread_attr_init;
	rte_thread_attr_set_affinity;
	rte_thread_attr_set_priority;
	rte_thread_create;
	rte_thread_create_control;
	rte_thread_detach;
	rte_thread_equal;
	rte_thread_get_affinity;
	rte_thread_get_affinity_by_id;
	rte_thread_get_priority;
	rte_thread_is_intr;
	rte_thread_join;
	rte_thread_key_create;
	rte_thread_key_delete;
	rte_thread_register;
	rte_thread_self;
	rte_thread_set_affinity;
	rte_thread_set_affinity_by_id;
	rte_thread_set_name;
	rte_thread_set_priority;
	rte_thread_unregister;
	rte_thread_value_get;
	rte_thread_value_set;
	rte_uuid_compare;
	rte_uuid_is_null;
	rte_uuid_parse;
	rte_uuid_unparse;
	rte_vect_get_max_simd_bitwidth;
	rte_vect_set_max_simd_bitwidth;
	rte_version;
	rte_version_minor;
	rte_version_month;
	rte_version_prefix;
	rte_version_release;
	rte_version_suffix;
	rte_version_year;
	rte_vfio_clear_group; # WINDOWS_NO_EXPORT
	rte_vfio_container_create; # WINDOWS_NO_EXPORT
	rte_vfio_container_destroy; # WINDOWS_NO_EXPORT
	rte_vfio_container_dma_map;
	rte_vfio_container_dma_unmap;
	rte_vfio_container_group_bind; # WINDOWS_NO_EXPORT
	rte_vfio_container_group_unbind; # WINDOWS_NO_EXPORT
	rte_vfio_enable; # WINDOWS_NO_EXPORT
	rte_vfio_get_container_fd; # WINDOWS_NO_EXPORT
	rte_vfio_get_group_fd; # WINDOWS_NO_EXPORT
	rte_vfio_get_group_num; # WINDOWS_NO_EXPORT
	rte_vfio_is_enabled; # WINDOWS_NO_EXPORT
	rte_vfio_noiommu_is_enabled; # WINDOWS_NO_EXPORT
	rte_vfio_release_device; # WINDOWS_NO_EXPORT
	rte_vfio_setup_device; # WINDOWS_NO_EXPORT
	rte_zmalloc;
	rte_zmalloc_socket;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 20.05
	__rte_eal_trace_generic_double;
	__rte_eal_trace_generic_float;
	__rte_eal_trace_generic_func;
	__rte_eal_trace_generic_i16;
	__rte_eal_trace_generic_i32;
	__rte_eal_trace_generic_i64;
	__rte_eal_trace_generic_i8;
	__rte_eal_trace_generic_int;
	__rte_eal_trace_generic_long;
	__rte_eal_trace_generic_ptr;
	__rte_eal_trace_generic_str;
	__rte_eal_trace_generic_u16;
	__rte_eal_trace_generic_u32;
	__rte_eal_trace_generic_u64;
	__rte_eal_trace_generic_u8;
	__rte_eal_trace_generic_void;
	__rte_trace_mem_per_thread_alloc;
	__rte_trace_point_emit_field;
	__rte_trace_point_register;
	per_lcore_trace_mem;
	per_lcore_trace_point_sz;
	rte_trace_dump; # WINDOWS_NO_EXPORT
	rte_trace_is_enabled; # WINDOWS_NO_EXPORT
	rte_trace_metadata_dump; # WINDOWS_NO_EXPORT
	rte_trace_mode_get; # WINDOWS_NO_EXPORT
	rte_trace_mode_set; # WINDOWS_NO_EXPORT
	rte_trace_pattern; # WINDOWS_NO_EXPORT
	rte_trace_point_disable; # WINDOWS_NO_EXPORT
	rte_trace_point_enable; # WINDOWS_NO_EXPORT
	rte_trace_point_is_enabled; # WINDOWS_NO_EXPORT
	rte_trace_point_lookup; # WINDOWS_NO_EXPORT
	rte_trace_regexp; # WINDOWS_NO_EXPORT
	rte_trace_save; # WINDOWS_NO_EXPORT

	# added in 20.11
	__rte_eal_trace_generic_size_t; # WINDOWS_NO_EXPORT
	rte_cpu_get_intrinsics_support; # WINDOWS_NO_EXPORT

	# added in 23.03
	rte_lcore_register_usage_cb;
	__rte_eal_trace_generic_blob;

	# added in 23.07
	rte_memzone_max_get;
	rte_memzone_max_set;
};

INTERNAL {
	global:

	rte_bus_register;
	rte_bus_unregister;
	rte_eal_get_baseaddr;
	rte_eal_parse_coremask;
	rte_firmware_read;
	rte_intr_allow_others;
	rte_intr_cap_multiple;
	rte_intr_dev_fd_get;
	rte_intr_dev_fd_set;
	rte_intr_dp_is_en;
	rte_intr_efd_counter_size_set;
	rte_intr_efd_counter_size_get;
	rte_intr_efd_disable;
	rte_intr_efd_enable;
	rte_intr_efds_index_get;
	rte_intr_efds_index_set;
	rte_intr_elist_index_get;
	rte_intr_elist_index_set;
	rte_intr_event_list_update;
	rte_intr_free_epoll_fd;
	rte_intr_instance_dup;
	rte_intr_instance_windows_handle_get;
	rte_intr_instance_windows_handle_set;
	rte_intr_max_intr_get;
	rte_intr_max_intr_set;
	rte_intr_nb_efd_get;
	rte_intr_nb_efd_set;
	rte_intr_nb_intr_get;
	rte_intr_rx_ctl;
	rte_intr_tls_epfd;
	rte_intr_vec_list_alloc;
	rte_intr_vec_list_free;
	rte_intr_vec_list_index_get;
	rte_intr_vec_list_index_set;
	rte_mcfg_ethdev_get_lock;
	rte_mcfg_mem_get_lock;
	rte_mcfg_mempool_get_lock;
	rte_mcfg_tailq_get_lock;
	rte_mcfg_timer_get_lock;
	rte_mem_lock;
	rte_mem_map;
	rte_mem_page_size;
	rte_mem_unmap;
	rte_thread_create_internal_control;
	rte_thread_set_prefixed_name;
};
