DPDK_24 {
	global:

	rte_pipeline_ah_packet_drop;
	rte_pipeline_ah_packet_hijack;
	rte_pipeline_check;
	rte_pipeline_create;
	rte_pipeline_flush;
	rte_pipeline_free;
	rte_pipeline_port_in_connect_to_table;
	rte_pipeline_port_in_create;
	rte_pipeline_port_in_disable;
	rte_pipeline_port_in_enable;
	rte_pipeline_port_in_stats_read;
	rte_pipeline_port_out_create;
	rte_pipeline_port_out_packet_insert;
	rte_pipeline_port_out_stats_read;
	rte_pipeline_run;
	rte_pipeline_table_create;
	rte_pipeline_table_default_entry_add;
	rte_pipeline_table_default_entry_delete;
	rte_pipeline_table_entry_add;
	rte_pipeline_table_entry_add_bulk;
	rte_pipeline_table_entry_delete;
	rte_pipeline_table_entry_delete_bulk;
	rte_pipeline_table_stats_read;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 18.05
	rte_port_in_action_apply;
	rte_port_in_action_create;
	rte_port_in_action_free;
	rte_port_in_action_params_get;
	rte_port_in_action_profile_action_register;
	rte_port_in_action_profile_create;
	rte_port_in_action_profile_free;
	rte_port_in_action_profile_freeze;
	rte_table_action_apply;
	rte_table_action_create;
	rte_table_action_dscp_table_update;
	rte_table_action_free;
	rte_table_action_meter_profile_add;
	rte_table_action_meter_profile_delete;
	rte_table_action_meter_read;
	rte_table_action_profile_action_register;
	rte_table_action_profile_create;
	rte_table_action_profile_free;
	rte_table_action_profile_freeze;
	rte_table_action_stats_read;
	rte_table_action_table_params_get;
	rte_table_action_time_read;
	rte_table_action_ttl_read;

	# added in 18.11
	rte_table_action_crypto_sym_session_get;

	# added in 20.11
	rte_swx_ctl_action_arg_info_get;
	rte_swx_ctl_action_info_get;
	rte_swx_ctl_pipeline_abort;
	rte_swx_ctl_pipeline_commit;
	rte_swx_ctl_pipeline_create;
	rte_swx_ctl_pipeline_free;
	rte_swx_ctl_pipeline_info_get;
	rte_swx_ctl_pipeline_mirroring_session_set;
	rte_swx_ctl_pipeline_numa_node_get;
	rte_swx_ctl_pipeline_port_in_stats_read;
	rte_swx_ctl_pipeline_port_out_stats_read;
	rte_swx_ctl_pipeline_table_default_entry_add;
	rte_swx_ctl_pipeline_table_entry_add;
	rte_swx_ctl_pipeline_table_entry_delete;
	rte_swx_ctl_pipeline_table_entry_read;
	rte_swx_ctl_pipeline_table_fprintf;
	rte_swx_ctl_table_action_info_get;
	rte_swx_ctl_table_info_get;
	rte_swx_ctl_table_match_field_info_get;
	rte_swx_ctl_table_ops_get;
	rte_swx_pipeline_action_config;
	rte_swx_pipeline_build;
	rte_swx_pipeline_config;
	rte_swx_pipeline_extern_func_register;
	rte_swx_pipeline_extern_object_config;
	rte_swx_pipeline_extern_type_member_func_register;
	rte_swx_pipeline_extern_type_register;
	rte_swx_pipeline_flush;
	rte_swx_pipeline_free;
	rte_swx_pipeline_instructions_config;
	rte_swx_pipeline_mirroring_config;
	rte_swx_pipeline_packet_header_register;
	rte_swx_pipeline_packet_metadata_register;
	rte_swx_pipeline_port_in_config;
	rte_swx_pipeline_port_in_type_register;
	rte_swx_pipeline_port_out_config;
	rte_swx_pipeline_port_out_type_register;
	rte_swx_pipeline_run;
	rte_swx_pipeline_struct_type_register;
	rte_swx_pipeline_table_config;
	rte_swx_pipeline_table_state_get;
	rte_swx_pipeline_table_state_set;
	rte_swx_pipeline_table_type_register;

	# added in 21.05
	rte_swx_ctl_metarray_info_get;
	rte_swx_ctl_meter_profile_add;
	rte_swx_ctl_meter_profile_delete;
	rte_swx_ctl_meter_reset;
	rte_swx_ctl_meter_set;
	rte_swx_ctl_meter_stats_read;
	rte_swx_ctl_pipeline_regarray_read;
	rte_swx_ctl_pipeline_regarray_write;
	rte_swx_ctl_pipeline_table_stats_read;
	rte_swx_ctl_regarray_info_get;
	rte_swx_pipeline_metarray_config;
	rte_swx_pipeline_regarray_config;

	# added in 21.08
	rte_swx_pipeline_selector_config;
	rte_swx_ctl_pipeline_selector_fprintf;
	rte_swx_ctl_pipeline_selector_group_add;
	rte_swx_ctl_pipeline_selector_group_delete;
	rte_swx_ctl_pipeline_selector_group_member_add;
	rte_swx_ctl_pipeline_selector_group_member_delete;
	rte_swx_ctl_pipeline_selector_stats_read;
	rte_swx_ctl_selector_info_get;
	rte_swx_ctl_selector_field_info_get;
	rte_swx_ctl_selector_group_id_field_info_get;
	rte_swx_ctl_selector_member_id_field_info_get;

	# added in 21.11
	rte_swx_ctl_pipeline_learner_default_entry_add;
	rte_swx_ctl_pipeline_learner_default_entry_read;
	rte_swx_ctl_pipeline_learner_stats_read;
	rte_swx_ctl_learner_action_info_get;
	rte_swx_ctl_learner_info_get;
	rte_swx_ctl_learner_match_field_info_get;
	rte_swx_pipeline_learner_config;

	# added in 22.07
	rte_swx_ctl_pipeline_learner_timeout_get;
	rte_swx_ctl_pipeline_learner_timeout_set;
	rte_swx_pipeline_hash_func_register;

	# added in 22.11
	rte_swx_ctl_meter_reset_with_key;
	rte_swx_ctl_meter_set_with_key;
	rte_swx_ctl_meter_stats_read_with_key;
	rte_swx_ctl_pipeline_find;
	rte_swx_ctl_pipeline_regarray_read_with_key;
	rte_swx_ctl_pipeline_regarray_write_with_key;
	rte_swx_pipeline_build_from_lib;
	rte_swx_pipeline_codegen;
	rte_swx_pipeline_find;

	# added in 23.03
	rte_swx_ctl_pipeline_rss_key_read;
	rte_swx_ctl_pipeline_rss_key_size_read;
	rte_swx_ctl_pipeline_rss_key_write;
	rte_swx_ctl_rss_info_get;
	rte_swx_ipsec_create;
	rte_swx_ipsec_find;
	rte_swx_ipsec_free;
	rte_swx_ipsec_run;
	rte_swx_ipsec_sa_add;
	rte_swx_ipsec_sa_delete;
	rte_swx_ipsec_sa_read;
	rte_swx_pipeline_rss_config;
};
