/*
 * xfrm_cryptodev_batch.h
 *
 * Description: 高效的批量cryptodev操作头文件
 */

#ifndef _XFRM_CRYPTODEV_BATCH_H
#define _XFRM_CRYPTODEV_BATCH_H

#include <linux/skbuff.h>
#include <net/xfrm.h>

/* 批量处理常量 */
#define CRYPTODEV_BATCH_SIZE            32    /* 每批最大操作数 */
#define CRYPTODEV_MAX_BATCH_CONTEXTS    8     /* 最大批量上下文数 */

/* 批量处理统计信息 */
struct xfrm_crypto_batch_stats {
    uint32_t total_contexts;        /* 总上下文数 */
    uint32_t active_contexts;       /* 活跃上下文数 */
    uint32_t pending_operations;    /* 待处理操作数 */
};

/* 函数声明 */

/**
 * 初始化批量处理上下文
 * 
 * @return 0成功，负数表示错误
 */
int xfrm_cryptodev_batch_init(void);

/**
 * 批量加密处理
 * 
 * @param states xfrm_state指针数组
 * @param skbs sk_buff指针数组
 * @param count 数组大小
 * @return 成功处理的数据包数量
 */
int xfrm_cryptodev_encrypt_batch(struct xfrm_state **states, struct sk_buff **skbs, 
                                int count);

/**
 * 批量解密处理
 * 
 * @param states xfrm_state指针数组
 * @param skbs sk_buff指针数组
 * @param count 数组大小
 * @return 成功处理的数据包数量
 */
int xfrm_cryptodev_decrypt_batch(struct xfrm_state **states, struct sk_buff **skbs, 
                                int count);

/**
 * 强制刷新所有待处理的批量操作
 * 
 * @return 刷新的操作数量
 */
int xfrm_cryptodev_batch_flush(void);

/**
 * 获取批量处理统计信息
 * 
 * @param stats 输出统计信息
 */
void xfrm_cryptodev_batch_stats(struct xfrm_crypto_batch_stats *stats);

#endif /* _XFRM_CRYPTODEV_BATCH_H */
