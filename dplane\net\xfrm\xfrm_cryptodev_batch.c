/*
 * xfrm_cryptodev_batch.c
 *
 * Description: 高效的批量cryptodev操作实现
 */

#include <linux/skbuff.h>
#include <rte_cryptodev.h>
#include <rte_crypto.h>
#include <net/xfrm.h>

#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_zerocopy.h"
#include "xfrm_cryptodev_batch.h"

/* 批量操作上下文 */
struct xfrm_crypto_batch_ctx {
    struct rte_crypto_op *ops[CRYPTODEV_BATCH_SIZE];
    struct sk_buff *skbs[CRYPTODEV_BATCH_SIZE];
    struct xfrm_state *states[CRYPTODEV_BATCH_SIZE];
    int count;
    int direction;
    uint8_t dev_id;
    uint16_t qp_id;
};

/* 全局批量上下文池 */
static struct xfrm_crypto_batch_ctx batch_contexts[CRYPTODEV_MAX_BATCH_CONTEXTS];
static int batch_ctx_initialized = 0;

/* 初始化批量处理上下文 */
int xfrm_cryptodev_batch_init(void)
{
    int i;
    
    if (batch_ctx_initialized) {
        return 0;
    }
    
    /* 初始化所有批量上下文 */
    for (i = 0; i < CRYPTODEV_MAX_BATCH_CONTEXTS; i++) {
        memset(&batch_contexts[i], 0, sizeof(batch_contexts[i]));
    }
    
    batch_ctx_initialized = 1;
    CRYPTO_INFO("Batch processing contexts initialized");
    
    return 0;
}

/* 获取可用的批量上下文 */
static struct xfrm_crypto_batch_ctx *get_available_batch_ctx(void)
{
    int i;
    
    for (i = 0; i < CRYPTODEV_MAX_BATCH_CONTEXTS; i++) {
        if (batch_contexts[i].count == 0) {
            return &batch_contexts[i];
        }
    }
    
    return NULL;  /* 没有可用的上下文 */
}

/* 准备批量操作 */
static int prepare_batch_operation(struct xfrm_crypto_batch_ctx *ctx, 
                                  struct xfrm_state *x, struct sk_buff *skb, 
                                  int direction)
{
    struct rte_crypto_op *op;
    int ret;
    
    /* 检查批量上下文是否已满 */
    if (ctx->count >= CRYPTODEV_BATCH_SIZE) {
        return -ENOSPC;
    }
    
    /* 检查数据包是否适合cryptodev处理 */
    if (!xfrm_packet_suitable_for_cryptodev(skb)) {
        return -ENOTSUP;
    }
    
    /* 分配crypto操作 */
    op = rte_crypto_op_alloc(unified_get_op_pool(), RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation for batch");
        return -ENOMEM;
    }
    
    /* 设置操作参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }
    
    /* 保存异步上下文 */
    ret = xfrm_save_async_context(op->sym->m_src, x, skb, direction);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to save async context: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }
    
    /* 添加到批量上下文 */
    ctx->ops[ctx->count] = op;
    ctx->skbs[ctx->count] = skb;
    ctx->states[ctx->count] = x;
    ctx->count++;
    
    /* 设置设备ID和队列ID（第一个操作时设置） */
    if (ctx->count == 1) {
        struct xfrm_cryptodev_session *crypto_session;

        ctx->direction = direction;

        /* 从会话中获取设备ID和队列ID */
        crypto_session = (struct xfrm_cryptodev_session *)x->context;
        if (crypto_session) {
            ctx->dev_id = crypto_session->dev_id;
            ctx->qp_id = crypto_session->qp_id;
        } else {
            CRYPTO_ERROR("No cryptodev session available for batch processing");
            return -EINVAL;
        }
    }
    
    CRYPTO_DEBUG("Added operation to batch: count=%d", ctx->count);
    
    return 0;
}

/* 提交批量操作 */
static int submit_batch_operations(struct xfrm_crypto_batch_ctx *ctx)
{
    uint16_t enqueued;
    int i;
    
    if (ctx->count == 0) {
        return 0;
    }
    
    CRYPTO_DEBUG("Submitting batch of %d operations to device %u queue %u",
                ctx->count, ctx->dev_id, ctx->qp_id);

    /* 批量提交操作 - 利用DPDK 23的优化 */
    enqueued = rte_cryptodev_enqueue_burst(ctx->dev_id, ctx->qp_id,
                                          ctx->ops, ctx->count);

    /* DPDK 23中，如果enqueue_burst返回值小于请求数量，
     * 通常意味着队列已满，可以考虑立即重试一次 */
    
    if (enqueued != ctx->count) {
        CRYPTO_ERROR("Failed to enqueue all operations: %u/%d", enqueued, ctx->count);
        
        /* 清理未提交的操作 */
        for (i = enqueued; i < ctx->count; i++) {
            xfrm_cleanup_async_context(ctx->ops[i]->sym->m_src);
            rte_crypto_op_free(ctx->ops[i]);
        }
        
        /* 重置上下文 */
        ctx->count = 0;
        
        return -EIO;
    }
    
    /* 重置批量上下文 */
    ctx->count = 0;
    
    CRYPTO_INFO("Successfully submitted batch of %u operations", enqueued);
    
    return enqueued;
}

/* 批量加密处理 */
int xfrm_cryptodev_encrypt_batch(struct xfrm_state **states, struct sk_buff **skbs, 
                                int count)
{
    struct xfrm_crypto_batch_ctx *ctx;
    int i, ret, processed = 0;
    
    if (!states || !skbs || count <= 0) {
        return -EINVAL;
    }
    
    CRYPTO_DEBUG("Processing batch encryption for %d packets", count);
    
    /* 获取批量上下文 */
    ctx = get_available_batch_ctx();
    if (!ctx) {
        CRYPTO_WARNING("No available batch context, processing individually");
        /* 回退到单个处理 */
        for (i = 0; i < count; i++) {
            ret = xfrm_cryptodev_encrypt(states[i], skbs[i]);
            if (ret == -EINPROGRESS) {
                processed++;
            }
        }
        return processed;
    }
    
    /* 准备批量操作 */
    for (i = 0; i < count; i++) {
        ret = prepare_batch_operation(ctx, states[i], skbs[i], XFRM_POLICY_OUT);
        if (ret < 0) {
            if (ret == -ENOSPC) {
                /* 批量已满，提交当前批量 */
                submit_batch_operations(ctx);
                /* 重新尝试添加当前操作 */
                ret = prepare_batch_operation(ctx, states[i], skbs[i], XFRM_POLICY_OUT);
            }
            
            if (ret < 0) {
                CRYPTO_DEBUG("Skipping packet %d due to error: %d", i, ret);
                continue;
            }
        }
        processed++;
    }
    
    /* 提交剩余的操作 */
    if (ctx->count > 0) {
        submit_batch_operations(ctx);
    }
    
    CRYPTO_DEBUG("Batch encryption completed: %d/%d packets processed", processed, count);
    
    return processed;
}

/* 批量解密处理 */
int xfrm_cryptodev_decrypt_batch(struct xfrm_state **states, struct sk_buff **skbs, 
                                int count)
{
    struct xfrm_crypto_batch_ctx *ctx;
    int i, ret, processed = 0;
    
    if (!states || !skbs || count <= 0) {
        return -EINVAL;
    }
    
    CRYPTO_DEBUG("Processing batch decryption for %d packets", count);
    
    /* 获取批量上下文 */
    ctx = get_available_batch_ctx();
    if (!ctx) {
        CRYPTO_WARNING("No available batch context, processing individually");
        /* 回退到单个处理 */
        for (i = 0; i < count; i++) {
            ret = xfrm_cryptodev_decrypt(states[i], skbs[i]);
            if (ret == -EINPROGRESS) {
                processed++;
            }
        }
        return processed;
    }
    
    /* 准备批量操作 */
    for (i = 0; i < count; i++) {
        ret = prepare_batch_operation(ctx, states[i], skbs[i], XFRM_POLICY_IN);
        if (ret < 0) {
            if (ret == -ENOSPC) {
                /* 批量已满，提交当前批量 */
                submit_batch_operations(ctx);
                /* 重新尝试添加当前操作 */
                ret = prepare_batch_operation(ctx, states[i], skbs[i], XFRM_POLICY_IN);
            }
            
            if (ret < 0) {
                CRYPTO_DEBUG("Skipping packet %d due to error: %d", i, ret);
                continue;
            }
        }
        processed++;
    }
    
    /* 提交剩余的操作 */
    if (ctx->count > 0) {
        submit_batch_operations(ctx);
    }
    
    CRYPTO_DEBUG("Batch decryption completed: %d/%d packets processed", processed, count);
    
    return processed;
}

/* 强制刷新所有待处理的批量操作 */
int xfrm_cryptodev_batch_flush(void)
{
    int i, total_flushed = 0;
    
    for (i = 0; i < CRYPTODEV_MAX_BATCH_CONTEXTS; i++) {
        if (batch_contexts[i].count > 0) {
            int flushed = submit_batch_operations(&batch_contexts[i]);
            if (flushed > 0) {
                total_flushed += flushed;
            }
        }
    }
    
    if (total_flushed > 0) {
        CRYPTO_DEBUG("Flushed %d pending batch operations", total_flushed);
    }
    
    return total_flushed;
}

/* 获取批量处理统计信息 */
void xfrm_cryptodev_batch_stats(struct xfrm_crypto_batch_stats *stats)
{
    int i;
    
    if (!stats) {
        return;
    }
    
    memset(stats, 0, sizeof(*stats));
    
    for (i = 0; i < CRYPTODEV_MAX_BATCH_CONTEXTS; i++) {
        stats->total_contexts++;
        if (batch_contexts[i].count > 0) {
            stats->active_contexts++;
            stats->pending_operations += batch_contexts[i].count;
        }
    }
}
