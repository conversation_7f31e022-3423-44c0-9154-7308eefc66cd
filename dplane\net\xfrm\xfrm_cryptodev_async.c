/*
 * xfrm_cryptodev_async.c
 *
 * Description: 优化的异步cryptodev操作处理
 */

#include <linux/skbuff.h>
#include <linux/workqueue.h>
#include <linux/spinlock.h>
#include <rte_cryptodev.h>
#include <rte_crypto.h>
#include <net/xfrm.h>

#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_zerocopy.h"
#include "xfrm_cryptodev_async.h"

/* 异步完成队列 */
struct xfrm_crypto_completion_queue {
    struct rte_crypto_op *completed_ops[CRYPTODEV_COMPLETION_QUEUE_SIZE];
    int head;
    int tail;
    int count;
    spinlock_t lock;
};

/* 全局完成队列 */
static struct xfrm_crypto_completion_queue completion_queues[CRYPTODEV_MAX_DEVS];
static int async_processing_initialized = 0;

/* 工作队列用于处理完成的操作 */
static struct workqueue_struct *crypto_completion_wq;
static struct work_struct completion_work;

/* 初始化异步处理 */
int xfrm_cryptodev_async_init(void)
{
    int i;
    
    if (async_processing_initialized) {
        return 0;
    }
    
    /* 初始化完成队列 */
    for (i = 0; i < CRYPTODEV_MAX_DEVS; i++) {
        completion_queues[i].head = 0;
        completion_queues[i].tail = 0;
        completion_queues[i].count = 0;
        spin_lock_init(&completion_queues[i].lock);
    }
    
    /* 创建工作队列 */
    crypto_completion_wq = create_singlethread_workqueue("crypto_completion");
    if (!crypto_completion_wq) {
        CRYPTO_ERROR("Failed to create completion workqueue");
        return -ENOMEM;
    }
    
    /* 初始化工作项 */
    INIT_WORK(&completion_work, xfrm_crypto_completion_worker);
    
    async_processing_initialized = 1;
    CRYPTO_INFO("Async processing initialized");
    
    return 0;
}

/* 清理异步处理 */
void xfrm_cryptodev_async_uninit(void)
{
    if (!async_processing_initialized) {
        return;
    }
    
    /* 取消待处理的工作 */
    cancel_work_sync(&completion_work);
    
    /* 销毁工作队列 */
    if (crypto_completion_wq) {
        destroy_workqueue(crypto_completion_wq);
        crypto_completion_wq = NULL;
    }
    
    async_processing_initialized = 0;
    CRYPTO_INFO("Async processing uninitialized");
}

/* 将完成的操作添加到队列 */
static int enqueue_completed_op(uint8_t dev_id, struct rte_crypto_op *op)
{
    struct xfrm_crypto_completion_queue *queue;
    unsigned long flags;
    
    if (dev_id >= CRYPTODEV_MAX_DEVS) {
        return -EINVAL;
    }
    
    queue = &completion_queues[dev_id];
    
    spin_lock_irqsave(&queue->lock, flags);
    
    /* 检查队列是否已满 */
    if (queue->count >= CRYPTODEV_COMPLETION_QUEUE_SIZE) {
        spin_unlock_irqrestore(&queue->lock, flags);
        CRYPTO_ERROR("Completion queue full for device %u", dev_id);
        return -ENOSPC;
    }
    
    /* 添加到队列 */
    queue->completed_ops[queue->tail] = op;
    queue->tail = (queue->tail + 1) % CRYPTODEV_COMPLETION_QUEUE_SIZE;
    queue->count++;
    
    spin_unlock_irqrestore(&queue->lock, flags);
    
    return 0;
}

/* 从队列中取出完成的操作 */
static struct rte_crypto_op *dequeue_completed_op(uint8_t dev_id)
{
    struct xfrm_crypto_completion_queue *queue;
    struct rte_crypto_op *op = NULL;
    unsigned long flags;
    
    if (dev_id >= CRYPTODEV_MAX_DEVS) {
        return NULL;
    }
    
    queue = &completion_queues[dev_id];
    
    spin_lock_irqsave(&queue->lock, flags);
    
    if (queue->count > 0) {
        op = queue->completed_ops[queue->head];
        queue->head = (queue->head + 1) % CRYPTODEV_COMPLETION_QUEUE_SIZE;
        queue->count--;
    }
    
    spin_unlock_irqrestore(&queue->lock, flags);
    
    return op;
}

/* 处理单个完成的操作 */
static void process_completed_operation(struct rte_crypto_op *op)
{
    struct xfrm_crypto_async_context *ctx;
    struct rte_mbuf *m;
    struct sk_buff *skb;
    struct xfrm_state *x;
    int ret;
    
    if (!op || !op->sym || !op->sym->m_src) {
        CRYPTO_ERROR("Invalid completed operation");
        return;
    }
    
    m = op->sym->m_src;
    
    /* 获取异步上下文 */
    ctx = xfrm_get_async_context(m);
    if (!ctx) {
        CRYPTO_ERROR("No async context found in completed operation");
        goto cleanup;
    }
    
    skb = ctx->skb;
    x = ctx->x;

    /* 检查xfrm_state是否仍然有效 */
    if (!x) {
        CRYPTO_ERROR("Invalid xfrm_state in async context");
        goto cleanup;
    }

    /* 检查操作状态 */
    if (op->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
        CRYPTO_ERROR("Crypto operation failed with status: %d", op->status);
        
        /* 处理错误 */
        ret = xfrm_cryptodev_handle_error(x, skb, -EIO);
        if (ret < 0) {
            CRYPTO_ERROR("Error handling failed: %d", ret);
            /* 丢弃数据包 */
            kfree_skb(skb);
        }
        goto cleanup;
    }
    
    /* 恢复skb（数据拷贝） */
    ret = xfrm_restore_skb_from_crypto(skb, m, op);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to restore SKB from crypto: %d", ret);
        kfree_skb(skb);
        goto cleanup;
    }
    
    /* 调用相应的完成处理函数 */
    if (ctx->direction == XFRM_POLICY_OUT) {
        xfrm_output_crypto_done(skb, 0);  /* 0表示成功 */
    } else {
        xfrm_input_crypto_done(skb, 0);   /* 0表示成功 */
    }
    
    CRYPTO_DEBUG("Crypto operation completed successfully: direction=%d, seq=%u",
                ctx->direction, ctx->seq);

cleanup:
    /* 清理异步上下文 */
    xfrm_cleanup_async_context(m);
    
    /* 释放crypto操作 */
    rte_crypto_op_free(op);
}

/* 工作队列处理函数 */
void xfrm_crypto_completion_worker(struct work_struct *work)
{
    struct rte_crypto_op *op;
    int dev_id, processed = 0;
    
    /* 处理所有设备的完成队列 */
    for (dev_id = 0; dev_id < CRYPTODEV_MAX_DEVS; dev_id++) {
        while ((op = dequeue_completed_op(dev_id)) != NULL) {
            process_completed_operation(op);
            processed++;
            
            /* 避免长时间占用CPU */
            if (processed >= CRYPTODEV_MAX_PROCESS_PER_WORK) {
                /* 重新调度工作以处理剩余的操作 */
                queue_work(crypto_completion_wq, &completion_work);
                return;
            }
        }
    }
    
    if (processed > 0) {
        CRYPTO_DEBUG("Processed %d completed crypto operations", processed);
    }
}

/* 轮询cryptodev设备并处理完成的操作 */
int xfrm_cryptodev_poll_completions(uint8_t dev_id, uint16_t qp_id)
{
    struct rte_crypto_op *dequeued_ops[CRYPTODEV_POLL_BURST_SIZE];
    uint16_t nb_dequeued;
    int i, ret, total_processed = 0;
    
    /* 从cryptodev设备出队完成的操作 */
    nb_dequeued = rte_cryptodev_dequeue_burst(dev_id, qp_id, 
                                             dequeued_ops, CRYPTODEV_POLL_BURST_SIZE);
    
    if (nb_dequeued == 0) {
        return 0;  /* 没有完成的操作 */
    }
    
    CRYPTO_DEBUG("Dequeued %u completed operations from device %u queue %u", 
                nb_dequeued, dev_id, qp_id);
    
    /* 将完成的操作添加到完成队列 */
    for (i = 0; i < nb_dequeued; i++) {
        ret = enqueue_completed_op(dev_id, dequeued_ops[i]);
        if (ret < 0) {
            CRYPTO_ERROR("Failed to enqueue completed operation: %d", ret);
            /* 直接处理这个操作 */
            process_completed_operation(dequeued_ops[i]);
        }
        total_processed++;
    }
    
    /* 调度工作队列处理完成的操作 */
    if (total_processed > 0) {
        queue_work(crypto_completion_wq, &completion_work);
    }
    
    return total_processed;
}

/* 获取异步处理统计信息 */
void xfrm_cryptodev_async_stats(struct xfrm_crypto_async_stats *stats)
{
    int i;
    unsigned long flags;
    
    if (!stats) {
        return;
    }
    
    memset(stats, 0, sizeof(*stats));
    
    for (i = 0; i < CRYPTODEV_MAX_DEVS; i++) {
        struct xfrm_crypto_completion_queue *queue = &completion_queues[i];
        
        spin_lock_irqsave(&queue->lock, flags);
        stats->total_pending += queue->count;
        if (queue->count > 0) {
            stats->active_queues++;
        }
        spin_unlock_irqrestore(&queue->lock, flags);
    }
    
    stats->workqueue_active = (crypto_completion_wq != NULL);
}
