/*
 * xfrm_cryptodev_async.c
 *
 * Description: 异步cryptodev操作管理
 */

#include <linux/kernel.h>
#include <linux/slab.h>
#include <linux/list.h>
#include <linux/spinlock.h>
#include <linux/time.h>
#include <linux/timer.h>
#include <linux/workqueue.h>
#include <rte_cycles.h>
#include <rte_malloc.h>

#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_config.h"

/* 异步上下文池 */
static struct kmem_cache *async_ctx_cache = NULL;

/* 待处理的异步操作列表 */
static LIST_HEAD(pending_async_ops);
static DEFINE_SPINLOCK(pending_ops_lock);

/* 轮询定时器和工作队列 */
static struct timer_list poll_timer;
static struct workqueue_struct *poll_workqueue;
static struct work_struct poll_work;
static int poll_enabled = 0;

/* 轮询间隔（毫秒） */
#define POLL_INTERVAL_MS  1

/* 异步操作统计 */
static struct {
    uint64_t total_submitted;
    uint64_t total_completed;
    uint64_t total_failed;
    uint64_t total_timeout;
    uint64_t current_pending;
    uint64_t max_pending;

    /* 性能统计 */
    uint64_t total_latency_cycles;  /* 总延迟周期数 */
    uint64_t min_latency_cycles;    /* 最小延迟 */
    uint64_t max_latency_cycles;    /* 最大延迟 */
    uint64_t encrypt_count;         /* 加密操作数 */
    uint64_t decrypt_count;         /* 解密操作数 */

    /* 吞吐量统计 */
    uint64_t last_stats_time;       /* 上次统计时间 */
    uint64_t last_completed_count;  /* 上次完成数量 */
    uint32_t current_throughput;    /* 当前吞吐量(ops/sec) */
    uint32_t peak_throughput;       /* 峰值吞吐量 */
} async_stats;

/**
 * 初始化异步处理模块
 */
int xfrm_cryptodev_async_init(void)
{
    /* 创建异步上下文缓存 */
    async_ctx_cache = kmem_cache_create("xfrm_async_ctx",
                                        sizeof(struct xfrm_crypto_async_ctx),
                                        0, SLAB_HWCACHE_ALIGN, NULL);
    if (!async_ctx_cache) {
        printk(KERN_ERR "Failed to create async context cache\n");
        return -ENOMEM;
    }

    /* 初始化统计信息 */
    memset(&async_stats, 0, sizeof(async_stats));

    /* 启动轮询机制 */
    if (xfrm_cryptodev_start_polling() != 0) {
        kmem_cache_destroy(async_ctx_cache);
        async_ctx_cache = NULL;
        return -ENOMEM;
    }

    printk(KERN_INFO "XFRM cryptodev async module initialized\n");
    return 0;
}

/**
 * 清理异步处理模块
 */
void xfrm_cryptodev_async_uninit(void)
{
    struct xfrm_crypto_async_ctx *ctx, *tmp;
    unsigned long flags;

    /* 停止轮询机制 */
    xfrm_cryptodev_stop_polling();

    /* 清理所有待处理的操作 */
    spin_lock_irqsave(&pending_ops_lock, flags);
    list_for_each_entry_safe(ctx, tmp, &pending_async_ops, list) {
        list_del(&ctx->list);
        
        /* 释放资源 */
        if (ctx->op) {
            rte_crypto_op_free(ctx->op);
        }
        if (ctx->x) {
            xfrm_state_put(ctx->x);
        }
        if (ctx->skb) {
            kfree_skb(ctx->skb);
        }
        
        kmem_cache_free(async_ctx_cache, ctx);
    }
    spin_unlock_irqrestore(&pending_ops_lock, flags);

    /* 销毁缓存 */
    if (async_ctx_cache) {
        kmem_cache_destroy(async_ctx_cache);
        async_ctx_cache = NULL;
    }

    printk(KERN_INFO "XFRM cryptodev async module uninitialized\n");
}

/**
 * 分配异步上下文
 */
struct xfrm_crypto_async_ctx *xfrm_crypto_async_ctx_alloc(void)
{
    struct xfrm_crypto_async_ctx *ctx;

    if (!async_ctx_cache) {
        return NULL;
    }

    ctx = kmem_cache_alloc(async_ctx_cache, GFP_ATOMIC);
    if (!ctx) {
        return NULL;
    }

    /* 初始化上下文 */
    memset(ctx, 0, sizeof(*ctx));
    INIT_LIST_HEAD(&ctx->list);
    ctx->submit_time = rte_get_tsc_cycles();

    return ctx;
}

/**
 * 释放异步上下文
 */
void xfrm_crypto_async_ctx_free(struct xfrm_crypto_async_ctx *ctx)
{
    if (!ctx || !async_ctx_cache) {
        return;
    }

    /* 确保从列表中移除 */
    if (!list_empty(&ctx->list)) {
        list_del(&ctx->list);
    }

    /* 释放引用的资源 */
    if (ctx->x) {
        xfrm_state_put(ctx->x);
        ctx->x = NULL;
    }

    if (ctx->skb) {
        kfree_skb(ctx->skb);
        ctx->skb = NULL;
    }

    if (ctx->op) {
        rte_crypto_op_free(ctx->op);
        ctx->op = NULL;
    }

    /* 释放上下文 */
    kmem_cache_free(async_ctx_cache, ctx);
}

/**
 * 初始化异步上下文
 */
int xfrm_crypto_async_ctx_init(struct xfrm_crypto_async_ctx *ctx,
                               struct xfrm_state *x,
                               struct sk_buff *skb,
                               uint32_t direction)
{
    if (!ctx || !x || !skb) {
        return -EINVAL;
    }

    /* 设置基本信息 */
    ctx->x = x;
    ctx->skb = skb;
    ctx->direction = direction;
    ctx->seq_num = XFRM_SKB_CB(skb)->seq.input.low;
    ctx->submit_time = rte_get_tsc_cycles();

    /* 增加xfrm_state引用计数 */
    xfrm_state_hold(x);

    return 0;
}

/**
 * 添加异步操作到待处理列表
 */
int xfrm_crypto_async_add_pending(struct xfrm_crypto_async_ctx *ctx)
{
    unsigned long flags;

    if (!ctx) {
        return -EINVAL;
    }

    spin_lock_irqsave(&pending_ops_lock, flags);
    
    /* 添加到待处理列表 */
    list_add_tail(&ctx->list, &pending_async_ops);
    
    /* 更新统计 */
    async_stats.total_submitted++;
    async_stats.current_pending++;
    if (async_stats.current_pending > async_stats.max_pending) {
        async_stats.max_pending = async_stats.current_pending;
    }
    
    spin_unlock_irqrestore(&pending_ops_lock, flags);

    return 0;
}

/**
 * 从待处理列表中移除异步操作
 */
void xfrm_crypto_async_remove_pending(struct xfrm_crypto_async_ctx *ctx)
{
    unsigned long flags;

    if (!ctx) {
        return;
    }

    spin_lock_irqsave(&pending_ops_lock, flags);
    
    if (!list_empty(&ctx->list)) {
        list_del_init(&ctx->list);
        async_stats.current_pending--;
    }
    
    spin_unlock_irqrestore(&pending_ops_lock, flags);
}

/**
 * 异步操作完成处理
 */
void xfrm_crypto_async_completion(struct xfrm_crypto_async_ctx *ctx, int status)
{
    uint64_t completion_time, latency;

    if (!ctx) {
        return;
    }

    /* 计算延迟 */
    completion_time = rte_get_tsc_cycles();
    latency = completion_time - ctx->submit_time;

    /* 从待处理列表中移除 */
    xfrm_crypto_async_remove_pending(ctx);

    /* 更新统计 */
    if (status == 0) {
        async_stats.total_completed++;

        /* 更新延迟统计 */
        async_stats.total_latency_cycles += latency;
        if (async_stats.min_latency_cycles == 0 || latency < async_stats.min_latency_cycles) {
            async_stats.min_latency_cycles = latency;
        }
        if (latency > async_stats.max_latency_cycles) {
            async_stats.max_latency_cycles = latency;
        }

        /* 更新操作类型统计 */
        if (ctx->direction == XFRM_CRYPTO_DIR_ENCRYPT) {
            async_stats.encrypt_count++;
        } else {
            async_stats.decrypt_count++;
        }
    } else {
        async_stats.total_failed++;
    }

    /* 调用完成回调 */
    if (ctx->completion_cb) {
        ctx->completion_cb(ctx, status);
    } else {
        /* 默认完成处理 */
        if (ctx->direction == XFRM_CRYPTO_DIR_ENCRYPT) {
            xfrm_output_crypto_done(ctx->skb, status);
        } else {
            xfrm_input_crypto_done(ctx->skb, status);
        }
    }

    /* 释放上下文 */
    xfrm_crypto_async_ctx_free(ctx);
}

/**
 * 获取异步操作统计信息
 */
void xfrm_crypto_async_get_stats(uint64_t *submitted, uint64_t *completed, 
                                 uint64_t *failed, uint64_t *pending)
{
    if (submitted) *submitted = async_stats.total_submitted;
    if (completed) *completed = async_stats.total_completed;
    if (failed) *failed = async_stats.total_failed;
    if (pending) *pending = async_stats.current_pending;
}

/**
 * 重置异步操作统计信息
 */
void xfrm_crypto_async_reset_stats(void)
{
    memset(&async_stats, 0, sizeof(async_stats));
}

/**
 * 轮询cryptodev设备获取完成的操作
 */
int xfrm_cryptodev_poll_completions(uint8_t dev_id, uint16_t qp_id)
{
    struct rte_crypto_op *completed_ops[32];  /* 批量处理 */
    uint16_t nb_completed;
    int i;
    int total_processed = 0;

    /* 从cryptodev队列中获取完成的操作 */
    nb_completed = rte_cryptodev_dequeue_burst(dev_id, qp_id,
                                               completed_ops,
                                               sizeof(completed_ops)/sizeof(completed_ops[0]));

    if (nb_completed == 0) {
        return 0;  /* 没有完成的操作 */
    }

    /* 处理每个完成的操作 */
    for (i = 0; i < nb_completed; i++) {
        struct rte_crypto_op *op = completed_ops[i];
        struct xfrm_crypto_async_ctx *ctx;
        int status;

        if (!op) {
            continue;
        }

        /* 从操作中获取异步上下文 */
        ctx = (struct xfrm_crypto_async_ctx *)op->opaque_data;
        if (!ctx) {
            CRYPTO_ERROR("No async context found in completed operation\n");
            rte_crypto_op_free(op);
            continue;
        }

        /* 检查操作状态 */
        if (op->status == RTE_CRYPTO_OP_STATUS_SUCCESS) {
            status = 0;  /* 成功 */

            /* 恢复数据到skb */
            struct sk_buff *result_skb = mbuf_to_skb(ctx->mbuf);
            if (result_skb) {
                ctx->skb = result_skb;  /* 更新skb */
            } else {
                CRYPTO_ERROR("Failed to convert mbuf back to skb\n");
                status = -ENOMEM;
            }
        } else {
            status = -EIO;  /* 操作失败 */
            CRYPTO_ERROR("Crypto operation failed with status: %d\n", op->status);
        }

        /* 清理crypto操作 */
        rte_crypto_op_free(op);
        ctx->op = NULL;

        /* 调用完成处理 */
        xfrm_crypto_async_completion(ctx, status);

        total_processed++;
    }

    return total_processed;
}

/**
 * 轮询所有活跃的cryptodev设备
 */
int xfrm_cryptodev_poll_all_devices(void)
{
    int total_processed = 0;
    int i, j;

    if (!cryptodev_enabled) {
        return 0;
    }

    /* 轮询所有设备的所有队列对 */
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        uint8_t dev_id = xfrm_cryptodev_ctx.dev_ids[i];

        /* 检查设备是否活跃 */
        if (!xfrm_cryptodev_ctx.dev_active[i]) {
            continue;
        }

        /* 轮询该设备的所有队列对 */
        for (j = 0; j < xfrm_cryptodev_ctx.nb_qps; j++) {
            struct xfrm_cryptodev_qp *qp = &xfrm_cryptodev_ctx.qp_table[j];

            if (qp->dev_id == dev_id) {
                int processed = xfrm_cryptodev_poll_completions(dev_id, qp->qp_id);
                total_processed += processed;
            }
        }
    }

    return total_processed;
}

/**
 * 轮询工作函数
 */
static void xfrm_crypto_poll_work_func(struct work_struct *work)
{
    static unsigned long last_timeout_check = 0;
    int processed;

    if (!poll_enabled) {
        return;
    }

    /* 轮询所有设备 */
    processed = xfrm_cryptodev_poll_all_devices();

    /* 定期检查超时（每秒一次） */
    if (time_after(jiffies, last_timeout_check + HZ)) {
        xfrm_crypto_check_timeouts();
        last_timeout_check = jiffies;
    }

    /* 如果处理了操作，立即重新调度 */
    if (processed > 0) {
        queue_work(poll_workqueue, &poll_work);
    }
}

/**
 * 轮询定时器函数
 */
static void xfrm_crypto_poll_timer_func(struct timer_list *timer)
{
    if (poll_enabled && poll_workqueue) {
        /* 调度工作队列 */
        queue_work(poll_workqueue, &poll_work);

        /* 重新设置定时器 */
        mod_timer(&poll_timer, jiffies + msecs_to_jiffies(POLL_INTERVAL_MS));
    }
}

/**
 * 启动轮询机制
 */
int xfrm_cryptodev_start_polling(void)
{
    if (poll_enabled) {
        return 0;  /* 已经启动 */
    }

    /* 创建工作队列 */
    poll_workqueue = create_singlethread_workqueue("xfrm_crypto_poll");
    if (!poll_workqueue) {
        printk(KERN_ERR "Failed to create crypto poll workqueue\n");
        return -ENOMEM;
    }

    /* 初始化工作 */
    INIT_WORK(&poll_work, xfrm_crypto_poll_work_func);

    /* 初始化定时器 */
    timer_setup(&poll_timer, xfrm_crypto_poll_timer_func, 0);

    /* 启动轮询 */
    poll_enabled = 1;
    mod_timer(&poll_timer, jiffies + msecs_to_jiffies(POLL_INTERVAL_MS));

    printk(KERN_INFO "XFRM cryptodev polling started\n");
    return 0;
}

/**
 * 停止轮询机制
 */
void xfrm_cryptodev_stop_polling(void)
{
    if (!poll_enabled) {
        return;
    }

    poll_enabled = 0;

    /* 停止定时器 */
    del_timer_sync(&poll_timer);

    /* 停止工作队列 */
    if (poll_workqueue) {
        cancel_work_sync(&poll_work);
        destroy_workqueue(poll_workqueue);
        poll_workqueue = NULL;
    }

    printk(KERN_INFO "XFRM cryptodev polling stopped\n");
}

/**
 * 检查并处理超时的异步操作
 */
static void xfrm_crypto_check_timeouts(void)
{
    struct xfrm_crypto_async_ctx *ctx, *tmp;
    unsigned long flags;
    uint64_t current_time = rte_get_tsc_cycles();
    uint64_t timeout_cycles = rte_get_tsc_hz() * 5;  /* 5秒超时 */
    LIST_HEAD(timeout_list);

    /* 查找超时的操作 */
    spin_lock_irqsave(&pending_ops_lock, flags);
    list_for_each_entry_safe(ctx, tmp, &pending_async_ops, list) {
        if (current_time - ctx->submit_time > timeout_cycles) {
            /* 移动到超时列表 */
            list_move(&ctx->list, &timeout_list);
            async_stats.current_pending--;
            async_stats.total_timeout++;
        }
    }
    spin_unlock_irqrestore(&pending_ops_lock, flags);

    /* 处理超时的操作 */
    list_for_each_entry_safe(ctx, tmp, &timeout_list, list) {
        CRYPTO_ERROR("Async crypto operation timeout, seq=%u, dir=%u\n",
                     ctx->seq_num, ctx->direction);

        /* 调用完成处理，状态为超时 */
        xfrm_crypto_async_completion(ctx, -ETIMEDOUT);
    }
}

/**
 * 异步错误处理函数
 */
void xfrm_crypto_async_handle_error(struct xfrm_crypto_async_ctx *ctx, int error_code)
{
    if (!ctx) {
        return;
    }

    CRYPTO_ERROR("Async crypto operation error: seq=%u, dir=%u, error=%d\n",
                 ctx->seq_num, ctx->direction, error_code);

    /* 从待处理列表中移除 */
    xfrm_crypto_async_remove_pending(ctx);

    /* 更新错误统计 */
    async_stats.total_failed++;

    /* 根据错误类型进行不同处理 */
    switch (error_code) {
    case -ETIMEDOUT:
        /* 超时错误 - 可能需要重置设备 */
        CRYPTO_ERROR("Crypto operation timeout\n");
        break;

    case -EIO:
        /* 硬件错误 - 可能需要禁用设备 */
        CRYPTO_ERROR("Crypto hardware error\n");
        break;

    case -ENOMEM:
        /* 内存错误 - 可能需要清理资源 */
        CRYPTO_ERROR("Crypto memory error\n");
        break;

    default:
        CRYPTO_ERROR("Unknown crypto error: %d\n", error_code);
        break;
    }

    /* 调用完成处理 */
    if (ctx->completion_cb) {
        ctx->completion_cb(ctx, error_code);
    } else {
        /* 默认错误处理 */
        if (ctx->direction == XFRM_CRYPTO_DIR_ENCRYPT) {
            xfrm_output_crypto_done(ctx->skb, error_code);
        } else {
            xfrm_input_crypto_done(ctx->skb, error_code);
        }
    }

    /* 释放上下文 */
    xfrm_crypto_async_ctx_free(ctx);
}

/**
 * 设备错误恢复
 */
int xfrm_crypto_device_recovery(uint8_t dev_id)
{
    struct xfrm_crypto_async_ctx *ctx, *tmp;
    unsigned long flags;
    LIST_HEAD(failed_list);
    int recovered = 0;

    CRYPTO_ERROR("Starting device recovery for dev_id=%u\n", dev_id);

    /* 查找该设备上的所有待处理操作 */
    spin_lock_irqsave(&pending_ops_lock, flags);
    list_for_each_entry_safe(ctx, tmp, &pending_async_ops, list) {
        /* 检查操作是否在该设备上 */
        struct xfrm_cryptodev_session *session = xfrm_get_cryptodev_session(ctx->x);
        if (session && session->dev_id == dev_id) {
            /* 移动到失败列表 */
            list_move(&ctx->list, &failed_list);
            async_stats.current_pending--;
            recovered++;
        }
    }
    spin_unlock_irqrestore(&pending_ops_lock, flags);

    /* 处理失败的操作 */
    list_for_each_entry_safe(ctx, tmp, &failed_list, list) {
        xfrm_crypto_async_handle_error(ctx, -EIO);
    }

    CRYPTO_ERROR("Device recovery completed: %d operations recovered\n", recovered);
    return recovered;
}

/**
 * 清理所有待处理的异步操作（用于紧急情况）
 */
void xfrm_crypto_async_cleanup_all(void)
{
    struct xfrm_crypto_async_ctx *ctx, *tmp;
    unsigned long flags;
    LIST_HEAD(cleanup_list);

    CRYPTO_ERROR("Emergency cleanup of all async operations\n");

    /* 移动所有操作到清理列表 */
    spin_lock_irqsave(&pending_ops_lock, flags);
    list_splice_init(&pending_async_ops, &cleanup_list);
    async_stats.current_pending = 0;
    spin_unlock_irqrestore(&pending_ops_lock, flags);

    /* 清理所有操作 */
    list_for_each_entry_safe(ctx, tmp, &cleanup_list, list) {
        xfrm_crypto_async_handle_error(ctx, -ECANCELED);
    }
}

/**
 * 更新吞吐量统计
 */
static void xfrm_crypto_update_throughput_stats(void)
{
    uint64_t current_time = rte_get_tsc_cycles();
    uint64_t time_diff, completed_diff;
    uint32_t throughput;

    if (async_stats.last_stats_time == 0) {
        async_stats.last_stats_time = current_time;
        async_stats.last_completed_count = async_stats.total_completed;
        return;
    }

    time_diff = current_time - async_stats.last_stats_time;
    completed_diff = async_stats.total_completed - async_stats.last_completed_count;

    /* 计算吞吐量（每秒操作数） */
    if (time_diff > 0) {
        throughput = (completed_diff * rte_get_tsc_hz()) / time_diff;
        async_stats.current_throughput = throughput;

        if (throughput > async_stats.peak_throughput) {
            async_stats.peak_throughput = throughput;
        }
    }

    async_stats.last_stats_time = current_time;
    async_stats.last_completed_count = async_stats.total_completed;
}

/**
 * 获取详细的异步操作统计信息
 */
void xfrm_crypto_async_get_detailed_stats(struct xfrm_crypto_async_stats *stats)
{
    uint64_t avg_latency_cycles = 0;
    uint64_t avg_latency_us = 0;
    uint64_t min_latency_us = 0;
    uint64_t max_latency_us = 0;
    uint64_t tsc_hz = rte_get_tsc_hz();

    if (!stats) {
        return;
    }

    /* 更新吞吐量统计 */
    xfrm_crypto_update_throughput_stats();

    /* 计算平均延迟 */
    if (async_stats.total_completed > 0) {
        avg_latency_cycles = async_stats.total_latency_cycles / async_stats.total_completed;
        avg_latency_us = (avg_latency_cycles * 1000000) / tsc_hz;
        min_latency_us = (async_stats.min_latency_cycles * 1000000) / tsc_hz;
        max_latency_us = (async_stats.max_latency_cycles * 1000000) / tsc_hz;
    }

    /* 填充统计信息 */
    stats->total_submitted = async_stats.total_submitted;
    stats->total_completed = async_stats.total_completed;
    stats->total_failed = async_stats.total_failed;
    stats->total_timeout = async_stats.total_timeout;
    stats->current_pending = async_stats.current_pending;
    stats->max_pending = async_stats.max_pending;

    stats->encrypt_count = async_stats.encrypt_count;
    stats->decrypt_count = async_stats.decrypt_count;

    stats->avg_latency_us = avg_latency_us;
    stats->min_latency_us = min_latency_us;
    stats->max_latency_us = max_latency_us;

    stats->current_throughput = async_stats.current_throughput;
    stats->peak_throughput = async_stats.peak_throughput;
}

/**
 * 显示异步操作性能统计
 */
void xfrm_crypto_async_show_perf_stats(void)
{
    struct xfrm_crypto_async_stats stats;

    xfrm_crypto_async_get_detailed_stats(&stats);

    printk(KERN_INFO "=== XFRM Cryptodev Async Performance Stats ===\n");
    printk(KERN_INFO "Operations:\n");
    printk(KERN_INFO "  Submitted:    %llu\n", stats.total_submitted);
    printk(KERN_INFO "  Completed:    %llu\n", stats.total_completed);
    printk(KERN_INFO "  Failed:       %llu\n", stats.total_failed);
    printk(KERN_INFO "  Timeout:      %llu\n", stats.total_timeout);
    printk(KERN_INFO "  Pending:      %llu (max: %llu)\n",
           stats.current_pending, stats.max_pending);

    printk(KERN_INFO "Operation Types:\n");
    printk(KERN_INFO "  Encrypt:      %llu\n", stats.encrypt_count);
    printk(KERN_INFO "  Decrypt:      %llu\n", stats.decrypt_count);

    printk(KERN_INFO "Latency (microseconds):\n");
    printk(KERN_INFO "  Average:      %llu\n", stats.avg_latency_us);
    printk(KERN_INFO "  Minimum:      %llu\n", stats.min_latency_us);
    printk(KERN_INFO "  Maximum:      %llu\n", stats.max_latency_us);

    printk(KERN_INFO "Throughput (ops/sec):\n");
    printk(KERN_INFO "  Current:      %u\n", stats.current_throughput);
    printk(KERN_INFO "  Peak:         %u\n", stats.peak_throughput);

    if (stats.total_submitted > 0) {
        uint32_t success_rate = (stats.total_completed * 100) / stats.total_submitted;
        printk(KERN_INFO "Success Rate:   %u%%\n", success_rate);
    }
}
