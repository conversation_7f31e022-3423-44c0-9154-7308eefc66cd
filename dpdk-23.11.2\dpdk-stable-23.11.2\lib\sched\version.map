DPDK_24 {
	global:

	rte_approx;
	rte_pie_config_init;
	rte_pie_rt_data_init;
	rte_red_config_init;
	rte_red_log2_1_minus_Wq;
	rte_red_pow2_frac_inv;
	rte_red_rand_seed;
	rte_red_rand_val;
	rte_red_rt_data_init;
	rte_sched_pipe_config;
	rte_sched_port_config;
	rte_sched_port_dequeue;
	rte_sched_port_enqueue;
	rte_sched_port_free;
	rte_sched_port_get_memory_footprint;
	rte_sched_port_pkt_read_color;
	rte_sched_port_pkt_read_tree_path;
	rte_sched_port_pkt_write;
	rte_sched_port_subport_profile_add;
	rte_sched_queue_read_stats;
	rte_sched_subport_config;
	rte_sched_subport_pipe_profile_add;
	rte_sched_subport_read_stats;
	rte_sched_subport_tc_ov_config;

	local: *;
};
