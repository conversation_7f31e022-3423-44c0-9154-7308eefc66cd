DPDK_24 {
	global:

	rte_fbk_hash_create;
	rte_fbk_hash_find_existing;
	rte_fbk_hash_free;
	rte_hash_add_key;
	rte_hash_add_key_data;
	rte_hash_add_key_with_hash;
	rte_hash_add_key_with_hash_data;
	rte_hash_count;
	rte_hash_create;
	rte_hash_del_key;
	rte_hash_del_key_with_hash;
	rte_hash_find_existing;
	rte_hash_free;
	rte_hash_free_key_with_position;
	rte_hash_get_key_with_position;
	rte_hash_hash;
	rte_hash_iterate;
	rte_hash_lookup;
	rte_hash_lookup_bulk;
	rte_hash_lookup_bulk_data;
	rte_hash_lookup_data;
	rte_hash_lookup_with_hash;
	rte_hash_lookup_with_hash_bulk;
	rte_hash_lookup_with_hash_bulk_data;
	rte_hash_lookup_with_hash_data;
	rte_hash_max_key_id;
	rte_hash_rcu_qsbr_add;
	rte_hash_reset;
	rte_hash_set_cmp_func;
	rte_thash_add_helper;
	rte_thash_adjust_tuple;
	rte_thash_complete_matrix;
	rte_thash_find_existing;
	rte_thash_free_ctx;
	rte_thash_get_complement;
	rte_thash_get_gfni_matrices;
	rte_thash_get_helper;
	rte_thash_get_key;
	rte_thash_gfni_supported;
	rte_thash_init_ctx;

	local: *;
};
