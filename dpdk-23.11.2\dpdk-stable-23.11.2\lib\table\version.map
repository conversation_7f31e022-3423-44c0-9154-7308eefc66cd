DPDK_24 {
	global:

	rte_table_acl_ops;
	rte_table_array_ops;
	rte_table_hash_cuckoo_ops;
	rte_table_hash_ext_ops;
	rte_table_hash_key16_ext_ops;
	rte_table_hash_key16_lru_ops;
	rte_table_hash_key32_ext_ops;
	rte_table_hash_key32_lru_ops;
	rte_table_hash_key8_ext_ops;
	rte_table_hash_key8_lru_ops;
	rte_table_hash_lru_ops;
	rte_table_lpm_ipv6_ops;
	rte_table_lpm_ops;
	rte_table_stub_ops;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 20.11
	rte_swx_table_exact_match_ops;
	rte_swx_table_exact_match_unoptimized_ops;

	# added in 21.05
	rte_swx_table_wildcard_match_ops;

	# added in 21.08
	rte_swx_table_selector_create;
	rte_swx_table_selector_footprint_get;
	rte_swx_table_selector_free;
	rte_swx_table_selector_group_set;
	rte_swx_table_selector_mailbox_size_get;
	rte_swx_table_selector_select;

	# added in 21.11
	rte_swx_table_learner_add;
	rte_swx_table_learner_create;
	rte_swx_table_learner_delete;
	rte_swx_table_learner_footprint_get;
	rte_swx_table_learner_free;
	rte_swx_table_learner_lookup;
	rte_swx_table_learner_mailbox_size_get;

	# added in 22.07
	rte_swx_table_learner_rearm;
	rte_swx_table_learner_rearm_new;
	rte_swx_table_learner_timeout_update;
};
