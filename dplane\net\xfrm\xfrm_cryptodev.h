/*
 * xfrm_cryptodev.h
 *
 * Description: DPDK cryptodev integration for xfrm framework
 */

#ifndef _XFRM_CRYPTODEV_H
#define _XFRM_CRYPTODEV_H

#include <net/xfrm.h>
#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>

/* 包含配置头文件 */
#include "xfrm_cryptodev_config.h"

/* 调试宏 */
#define CRYPTODEV_DEBUG 1  /* 设置为 0 禁用调试输出 */

#if CRYPTODEV_DEBUG
#define CRYPTO_DEBUG(fmt, args...) \
    IPSEC_DEBUG("CRYPTO: " fmt, ##args)
#else
#define CRYPTO_DEBUG(fmt, args...)
#endif

#define CRYPTO_ERROR(fmt, args...) \
    IPSEC_DEBUG_DROP("CRYPTO ERROR: " fmt, ##args)

/* Cryptodev 标志 */
#define XFRM_CRYPTO_FLAG_HW_OFFLOAD    0x0001  /* 使用硬件加速 */
#define XFRM_CRYPTO_FLAG_SW_FALLBACK   0x0002  /* 使用软件回退 */
#define XFRM_CRYPTO_FLAG_ASYNC         0x0004  /* 使用异步处理 */

/* 算法映射表项数 */
#define CRYPTODEV_MAP_ENTRIES       1024  /* 算法映射表项数 */

/* 加密算法映射表结构 */
struct xfrm_cipher_algo_map {
    const char *name;                  /* IPsec 算法名称 */
    enum rte_crypto_cipher_algorithm cipher_algo; /* DPDK 算法 */
    uint16_t key_size;                 /* 密钥大小 */
    uint16_t iv_size;                  /* IV 大小 */
    uint16_t block_size;               /* 块大小 */
};

/* 认证算法映射表结构 */
struct xfrm_auth_algo_map {
    const char *name;                  /* IPsec 算法名称 */
    enum rte_crypto_auth_algorithm auth_algo; /* DPDK 算法 */
    uint16_t key_size;                 /* 密钥大小 */
    uint16_t digest_size;              /* 摘要大小 */
    uint16_t block_size;               /* 块大小 */
};

/* AEAD 算法映射表结构 */
struct xfrm_aead_algo_map {
    const char *name;                  /* IPsec 算法名称 */
    enum rte_crypto_aead_algorithm aead_algo; /* DPDK 算法 */
    uint16_t key_size;                 /* 密钥大小 */
    uint16_t digest_size;              /* 摘要大小 */
    uint16_t aad_size;                 /* AAD 大小 */
    uint16_t iv_size;                  /* IV 大小 */
};

/* Cryptodev 会话信息 */
struct xfrm_cryptodev_session {
    void *session;                /* Cryptodev 会话指针 */
    uint8_t dev_id;               /* Cryptodev 设备 ID */
    uint16_t qp_id;               /* 队列对 ID */
    enum rte_crypto_op_sess_type sess_type;  /* 会话类型 */
    struct rte_crypto_sym_xform *xforms;     /* 加密转换 */
    uint32_t flags;               /* 会话标志 */
};

/* Cryptodev 队列对 */
struct xfrm_cryptodev_qp {
    uint8_t dev_id;               /* Cryptodev ID */
    uint16_t qp_id;               /* 队列对 ID */
    struct rte_crypto_op **ops_buffer;  /* 操作缓冲区 */
    uint16_t nb_ops;              /* 缓冲区中的操作数 */
    uint16_t in_flight;           /* 正在处理的操作数 */
    unsigned int core_id;         /* 关联的核心 ID */
};

/* 算法映射键 */
struct xfrm_cdev_key {
    uint8_t lcore_id;             /* 核心 ID */
    uint8_t cipher_algo;          /* 加密算法 */
    uint8_t auth_algo;            /* 认证算法 */
    uint8_t aead_algo;            /* AEAD 算法 */
};

/* Cryptodev 上下文 */
struct xfrm_cryptodev_ctx {
    struct rte_mempool *session_pool;  /* 会话池 */
    struct rte_mempool *op_pool;       /* 操作池 */
    struct rte_mempool *mbuf_pool;     /* mbuf 池 */
    struct xfrm_cryptodev_qp *qp_table;  /* 队列对表 */
    uint16_t nb_qps;                   /* 队列对数量 */
    struct rte_hash *algo_map;         /* 算法映射表 */
    uint8_t *dev_ids;                  /* 设备 ID 列表 */
    uint8_t nb_devs;                   /* 设备数量 */
    uint8_t *dev_active;               /* 设备活动状态 */
    uint64_t *last_error_count;        /* 上次错误计数 */
};

/* Cryptodev 元数据 */
struct xfrm_cryptodev_metadata {
    struct xfrm_state *x;        /* 关联的 SA */
    int dir;                     /* 方向（输入/输出） */
    void *orig_data;             /* 原始数据指针 */
    int orig_len;                /* 原始数据长度 */
    uint32_t seq;                /* 序列号 */
};

/* 全局变量声明 */
extern struct xfrm_cryptodev_ctx xfrm_cryptodev_ctx;
extern int cryptodev_enabled;
extern int cryptodev_async_mode;
extern int cryptodev_error_policy;

/* 函数声明 */
/* 初始化和清理函数 */
int xfrm_cryptodev_init(void);
void xfrm_cryptodev_uninit(void);
int xfrm_cryptodev_poll(void);
void xfrm_cryptodev_health_check(void);
void xfrm_cleanup(void);

/* 算法映射函数 */
const struct xfrm_cipher_algo_map *xfrm_find_cipher_algo(const char *name, uint16_t key_size);
const struct xfrm_auth_algo_map *xfrm_find_auth_algo(const char *name, uint16_t key_size);
const struct xfrm_aead_algo_map *xfrm_find_aead_algo(const char *name, uint16_t key_size);
int xfrm_set_crypto_xforms(struct xfrm_state *x, struct rte_crypto_sym_xform **xforms);

/* 会话管理函数 */
int xfrm_cryptodev_session_create(struct xfrm_state *x);
void xfrm_cryptodev_session_destroy(struct xfrm_state *x);
int xfrm_to_cryptodev_xforms(struct xfrm_state *x, struct rte_crypto_sym_xform **xforms);
int xfrm_cryptodev_select_device(struct xfrm_state *x, uint16_t *dev_id, uint16_t *qp_id);

/* 数据包处理函数 */
int xfrm_cryptodev_encrypt(struct xfrm_state *x, struct sk_buff *skb);
int xfrm_cryptodev_decrypt(struct xfrm_state *x, struct sk_buff *skb);
int xfrm_cryptodev_submit_op(struct rte_crypto_op *op, struct xfrm_state *x);
int xfrm_set_crypto_op_params(struct xfrm_state *x, struct rte_crypto_op *op, struct sk_buff *skb);
void xfrm_cryptodev_enqueue_burst(struct xfrm_cryptodev_qp *qp);
uint16_t xfrm_cryptodev_dequeue_burst(struct xfrm_cryptodev_qp *qp);
void xfrm_cryptodev_process_completed(struct xfrm_cryptodev_qp *qp, uint16_t nb_ops);

/* 数据转换函数 */
struct rte_mbuf *skb_to_mbuf(struct sk_buff *skb);
struct sk_buff *mbuf_to_skb(struct rte_mbuf *m);

/* 配置和管理函数 */
int xfrm_cryptodev_set_enabled(int enabled);
void xfrm_cryptodev_show_status(void);
void xfrm_cryptodev_show_stats(void);
void xfrm_cryptodev_reset_stats(void);

/* 统计更新函数 */
void update_crypto_stats_submit(void);
void update_crypto_stats_complete(unsigned long processing_time);
void update_crypto_stats_fail(void);
void reset_crypto_stats(void);
void xfrm_cryptodev_show_perf_stats(void);
int xfrm_cryptodev_handle_error(struct xfrm_state *x, struct sk_buff *skb, int err);
void xfrm_cryptodev_health_check(void);

/* 异步完成处理函数 */
void xfrm_input_crypto_done(struct sk_buff *skb, int err);
void xfrm_output_crypto_done(struct sk_buff *skb, int err);

/* 如果 RTE_VERSION_NUM 宏未定义，则定义它 */
#ifndef RTE_VERSION_NUM
#define RTE_VERSION_NUM(a, b, c, d) ((a) << 24 | (b) << 16 | (c) << 8 | (d))
#endif

/* 如果 LIST_END 宏未定义，则定义它们 */
#ifndef RTE_CRYPTO_CIPHER_LIST_END
#define RTE_CRYPTO_CIPHER_LIST_END RTE_CRYPTO_CIPHER_AES_ECB
#endif

#ifndef RTE_CRYPTO_AUTH_LIST_END
#define RTE_CRYPTO_AUTH_LIST_END RTE_CRYPTO_AUTH_SHA1_HMAC
#endif

#ifndef RTE_CRYPTO_AEAD_LIST_END
#define RTE_CRYPTO_AEAD_LIST_END RTE_CRYPTO_AEAD_AES_GCM
#endif

#endif /* _XFRM_CRYPTODEV_H */
