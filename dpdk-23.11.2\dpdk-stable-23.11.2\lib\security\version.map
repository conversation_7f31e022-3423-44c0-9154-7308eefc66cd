DPDK_24 {
	global:

	__rte_security_set_pkt_metadata;
	rte_security_capabilities_get;
	rte_security_capability_get;
	rte_security_dynfield_offset;
	rte_security_macsec_sa_create;
	rte_security_macsec_sa_destroy;
	rte_security_macsec_sa_stats_get;
	rte_security_macsec_sc_create;
	rte_security_macsec_sc_destroy;
	rte_security_macsec_sc_stats_get;
	rte_security_session_create;
	rte_security_session_destroy;
	rte_security_session_get_size;
	rte_security_session_stats_get;
	rte_security_session_update;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 23.11
	rte_security_inb_pkt_rx_inject;
	rte_security_oop_dynfield_offset;
	rte_security_rx_inject_configure;
};

INTERNAL {
	global:

	rte_security_dynfield_register;
	rte_security_oop_dynfield_register;
};
