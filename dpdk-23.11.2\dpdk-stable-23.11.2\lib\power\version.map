DPDK_24 {
	global:

	rte_power_check_env_supported;
	rte_power_ethdev_pmgmt_queue_disable;
	rte_power_ethdev_pmgmt_queue_enable;
	rte_power_exit;
	rte_power_freq_disable_turbo;
	rte_power_freq_down;
	rte_power_freq_enable_turbo;
	rte_power_freq_max;
	rte_power_freq_min;
	rte_power_freq_up;
	rte_power_freqs;
	rte_power_get_capabilities;
	rte_power_get_env;
	rte_power_get_freq;
	rte_power_get_uncore_freq;
	rte_power_guest_channel_receive_msg;
	rte_power_guest_channel_send_msg;
	rte_power_init;
	rte_power_pmd_mgmt_get_emptypoll_max;
	rte_power_pmd_mgmt_get_pause_duration;
	rte_power_pmd_mgmt_get_scaling_freq_max;
	rte_power_pmd_mgmt_get_scaling_freq_min;
	rte_power_pmd_mgmt_set_emptypoll_max;
	rte_power_pmd_mgmt_set_pause_duration;
	rte_power_pmd_mgmt_set_scaling_freq_max;
	rte_power_pmd_mgmt_set_scaling_freq_min;
	rte_power_set_env;
	rte_power_set_freq;
	rte_power_set_uncore_freq;
	rte_power_turbo_status;
	rte_power_uncore_exit;
	rte_power_uncore_freq_max;
	rte_power_uncore_freq_min;
	rte_power_uncore_get_num_dies;
	rte_power_uncore_get_num_freqs;
	rte_power_uncore_get_num_pkgs;
	rte_power_uncore_init;
	rte_power_unset_env;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 23.11
	rte_power_get_uncore_env;
	rte_power_set_uncore_env;
	rte_power_uncore_freqs;
	rte_power_unset_uncore_env;
};
