DPDK_24 {
	global:

	rte_rawdev_close;
	rte_rawdev_configure;
	rte_rawdev_count;
	rte_rawdev_dequeue_buffers;
	rte_rawdev_dump;
	rte_rawdev_enqueue_buffers;
	rte_rawdev_firmware_load;
	rte_rawdev_firmware_status_get;
	rte_rawdev_firmware_unload;
	rte_rawdev_firmware_version_get;
	rte_rawdev_get_attr;
	rte_rawdev_get_dev_id;
	rte_rawdev_info_get;
	rte_rawdev_pmd_allocate;
	rte_rawdev_pmd_release;
	rte_rawdev_queue_conf_get;
	rte_rawdev_queue_count;
	rte_rawdev_queue_release;
	rte_rawdev_queue_setup;
	rte_rawdev_reset;
	rte_rawdev_selftest;
	rte_rawdev_set_attr;
	rte_rawdev_socket_id;
	rte_rawdev_start;
	rte_rawdev_stop;
	rte_rawdev_xstats_by_name_get;
	rte_rawdev_xstats_get;
	rte_rawdev_xstats_names_get;
	rte_rawdev_xstats_reset;
	rte_rawdevs;

	local: *;
};
