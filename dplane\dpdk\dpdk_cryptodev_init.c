/*
 * dpdk_cryptodev_init.c
 *
 * Description: DPDK cryptodev 初始化
 */

/* 定义 _GNU_SOURCE 宏，用于 RTLD_DEFAULT */
#define _GNU_SOURCE

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <errno.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/socket.h>
#include <fcntl.h>
#include <dlfcn.h>

#include <rte_config.h>
#include <rte_eal.h>
#include <rte_cryptodev.h>
#include <rte_vdev.h>
#include <rte_mempool.h>
#include <rte_malloc.h>
#include <rte_log.h>
#include <rte_version.h>
#include <rte_errno.h>

#if 0
#define CRYPTO_LOG(level, fmt, args...) \
    RTE_LOG(level, CRYPTODEV, "%s(): " fmt "\n", __func__, ##args)
#else
#define CRYPTO_LOG(level, fmt, args...) \
    printf("%s(): " fmt "\n", __func__, ##args)
#endif

#define CRYPTO_DEBUG(fmt, args...) CRYPTO_LOG(DEBUG, fmt, ## args)
#define CRYPTO_INFO(fmt, args...) CRYPTO_LOG(INFO, fmt, ## args)
#define CRYPTO_ERROR(fmt, args...) CRYPTO_LOG(ERR, fmt, ## args)

/* 加密设备配置 */
#define CRYPTO_MAX_QUEUE_PAIRS 8
#define CRYPTO_MAX_SESSIONS 2048
#define CRYPTO_SESSION_POOL_CACHE_SIZE 32
#define CRYPTO_MAX_OPS 2048
#define CRYPTO_BURST_SIZE 32

/* 全局变量 */
struct rte_mempool *crypto_session_pool = NULL;
struct rte_mempool *crypto_op_pool = NULL;
int cryptodev_initialized = 0;

/* 创建 OpenSSL cryptodev 设备 */
static int create_openssl_cryptodev(void)
{
    uint8_t dev_id;
    int ret;
    uint8_t nb_devs_before, nb_devs_after;

    /* 获取当前设备数量 */
    nb_devs_before = rte_cryptodev_count();
    CRYPTO_INFO("Current number of crypto devices: %u", nb_devs_before);

    /* 检查是否已经有 OpenSSL 设备 */
    for (dev_id = 0; dev_id < nb_devs_before; dev_id++) {
        struct rte_cryptodev_info info;

        /* 检查设备 ID 是否有效 */
        if (!rte_cryptodev_is_valid_dev(dev_id)) {
            CRYPTO_INFO("Device %u is not valid, skipping", dev_id);
            continue;
        }

        /* rte_cryptodev_info_get 在某些 DPDK 版本中返回 void */
        rte_cryptodev_info_get(dev_id, &info);

        /* 检查 driver_name 是否有效 */
        if (info.driver_name == NULL) {
            CRYPTO_ERROR("Failed to get driver name for device %u", dev_id);
            continue;
        }

        if (info.driver_name && strcmp(info.driver_name, "crypto_openssl") == 0) {
            CRYPTO_INFO("OpenSSL PMD already exists at device %u", dev_id);
            return dev_id;
        }
    }

    /* 检查 DPDK 库路径 */
    CRYPTO_INFO("Checking DPDK library paths");
    struct stat st;
    const char *dpdk_lib_paths[] = {
        "/usr/lib/librte_crypto_openssl.so",
        "/usr/local/lib/librte_crypto_openssl.so",
        "/usr/lib/x86_64-linux-gnu/librte_crypto_openssl.so",
        "/usr/lib64/librte_crypto_openssl.so",
        "../../../lib/prebuild-dpdk/dpdk/build/lib/librte_crypto_openssl.so",
        "../../../lib/prebuild-dpdk/dpdk/lib/librte_crypto_openssl.so",
        NULL
    };

    int found_lib = 0;
    for (int i = 0; dpdk_lib_paths[i] != NULL; i++) {
        if (stat(dpdk_lib_paths[i], &st) == 0) {
            CRYPTO_INFO("Found OpenSSL PMD library at %s", dpdk_lib_paths[i]);
            found_lib = 1;
            break;
        }
    }

    if (!found_lib) {
        CRYPTO_ERROR("OpenSSL PMD library not found in standard paths");
        CRYPTO_ERROR("Please ensure DPDK is compiled with OpenSSL PMD support");
    }

    /* 检查 DPDK 是否支持 crypto_openssl PMD */
    CRYPTO_INFO("Checking if DPDK supports crypto_openssl PMD");
    void *handle = dlopen("librte_crypto_openssl.so", RTLD_LAZY);
    if (handle) {
        CRYPTO_INFO("Successfully loaded librte_crypto_openssl.so");
        dlclose(handle);
    } else {
        CRYPTO_ERROR("Failed to load librte_crypto_openssl.so: %s", dlerror());
    }

    /* 检查 DPDK 版本和配置 */
    CRYPTO_INFO("DPDK version: %s", rte_version());
    CRYPTO_INFO("DPDK has cryptodev support: %s", rte_cryptodev_count() >= 0 ? "yes" : "no");
    CRYPTO_INFO("DPDK cryptodev count: %d", rte_cryptodev_count());

    /* 检查 DPDK 编译选项 */
    CRYPTO_INFO("Checking DPDK compile-time options");
    ret = system("grep -i crypto /usr/include/rte_config.h 2>/dev/null || echo 'No crypto options found in rte_config.h'");
    CRYPTO_INFO("DPDK crypto compile options: %d", ret);

    /* 尝试使用不同的方法创建 cryptodev 设备 */
    CRYPTO_INFO("Trying different methods to create cryptodev device");

    /* 方法 1: 使用 rte_vdev_init 创建 OpenSSL PMD */
    CRYPTO_INFO("Method 1: Using rte_vdev_init with crypto_openssl");

    /* 检查 /dev/crypto 设备是否存在 */
    /* 使用之前已经声明的 st 变量 */
    if (stat("/dev/crypto", &st) == 0) {
        CRYPTO_INFO("/dev/crypto device exists");

        /* 检查 /dev/crypto 设备权限 */
        if ((st.st_mode & S_IRWXU) != S_IRWXU) {
            CRYPTO_ERROR("/dev/crypto device permissions issue: %o", st.st_mode & 0777);
        }

        /* 尝试打开 /dev/crypto 设备 */
        int fd = open("/dev/crypto", O_RDWR);
        if (fd < 0) {
            CRYPTO_ERROR("Failed to open /dev/crypto: %d (errno: %d - %s)",
                        fd, errno, strerror(errno));
        } else {
            CRYPTO_INFO("Successfully opened /dev/crypto, fd: %d", fd);
            close(fd);
        }
    } else {
        CRYPTO_ERROR("/dev/crypto device does not exist (errno: %d - %s)",
                    errno, strerror(errno));
    }

    /* 尝试创建 OpenSSL PMD 设备 */
    const char *openssl_args = "socket_id=0";

    /* 检查 rte_vdev_init 函数是否可用 */
    CRYPTO_INFO("Checking if rte_vdev_init function is available");
    void *vdev_func = dlsym(RTLD_DEFAULT, "rte_vdev_init");
    if (vdev_func) {
        CRYPTO_INFO("rte_vdev_init function is available");
    } else {
        CRYPTO_ERROR("rte_vdev_init function is not available: %s", dlerror());
        CRYPTO_ERROR("DPDK was compiled without vdev support");
        CRYPTO_ERROR("Will try to use existing hardware cryptodev devices");

        /* 尝试查找现有的硬件cryptodev设备 */
        if (nb_devs_before > 0) {
            CRYPTO_INFO("Found %u existing cryptodev devices, will use the first one", nb_devs_before);
            return 0;  /* 使用第一个现有设备 */
        } else {
            CRYPTO_ERROR("No existing cryptodev devices found and cannot create virtual devices");
            return 0xFF;  /* 返回错误，表示无法创建设备 */
        }
    }

    /* 检查 DPDK 是否已经初始化 */
    CRYPTO_INFO("Checking if DPDK is initialized");
    if (rte_eal_get_configuration()) {
        CRYPTO_INFO("DPDK is initialized");
    } else {
        CRYPTO_ERROR("DPDK is not initialized");
    }

    /* 检查 DPDK 是否支持虚拟设备 */
    CRYPTO_INFO("Checking if DPDK supports virtual devices");
    /* 直接检查 rte_vdev_init 函数是否可用，而不是 rte_vdev_bus */
    if (vdev_func) {
        CRYPTO_INFO("DPDK supports virtual devices");
    } else {
        CRYPTO_ERROR("DPDK does not support virtual devices");
    }

    /* 尝试创建 OpenSSL PMD 设备 */
    CRYPTO_INFO("Trying to create OpenSSL PMD device with rte_vdev_init");

    /* 清除之前的错误 */
    rte_errno = 0;

    ret = rte_vdev_init("crypto_openssl", openssl_args);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to create OpenSSL PMD device with rte_vdev_init: %d", ret);
        CRYPTO_ERROR("System errno: %d - %s", errno, strerror(errno));
        CRYPTO_ERROR("DPDK errno: %d - %s", rte_errno, rte_strerror(rte_errno));

        /* 记录详细错误信息 */
        CRYPTO_ERROR("DPDK error code: %d", ret);
        CRYPTO_ERROR("DPDK error string: %s", rte_strerror(abs(ret)));
        CRYPTO_ERROR("Please check DPDK configuration and ensure vdev support is enabled");
    } else {
        CRYPTO_INFO("Successfully created OpenSSL PMD device with rte_vdev_init");
    }

    /* 检查设备创建结果 */

    /* 获取新创建的设备 ID */
    nb_devs_after = rte_cryptodev_count();
    CRYPTO_INFO("Number of crypto devices after init: %u", nb_devs_after);

    if (nb_devs_after <= nb_devs_before) {
        CRYPTO_ERROR("No new crypto device was created");

        CRYPTO_ERROR("Failed to create any crypto device");
        return 0xFF;  /* 返回错误标志 */
    }

    /* 查找新创建的加密设备 */
    for (dev_id = 0; dev_id < nb_devs_after; dev_id++) {
        struct rte_cryptodev_info info;

        /* 检查设备 ID 是否有效 */
        if (!rte_cryptodev_is_valid_dev(dev_id)) {
            CRYPTO_INFO("Device %u is not valid, skipping", dev_id);
            continue;
        }

        /* rte_cryptodev_info_get 在某些 DPDK 版本中返回 void */
        rte_cryptodev_info_get(dev_id, &info);

        /* 检查 driver_name 是否有效 */
        if (info.driver_name == NULL) {
            CRYPTO_ERROR("Failed to get driver name for device %u", dev_id);
            continue;
        }

        /* 接受任何加密设备，不仅仅是 OpenSSL */
        CRYPTO_INFO("Found crypto device %u with driver %s", dev_id, info.driver_name);
        return dev_id;
    }

    CRYPTO_ERROR("Failed to find any crypto device");
    return 0xFF;  /* 使用 0xFF 作为错误标志，而不是 -1 */
}

/* 配置 cryptodev 设备 */
static int configure_cryptodev(uint8_t dev_id)
{
    struct rte_cryptodev_info dev_info;
    struct rte_cryptodev_config dev_config;
    struct rte_cryptodev_qp_conf qp_conf;
    uint16_t qp_id;
    int ret;

    /* 检查设备 ID 是否有效 */
    if (!rte_cryptodev_is_valid_dev(dev_id)) {
        CRYPTO_ERROR("Invalid cryptodev ID: %u", dev_id);
        return -EINVAL;
    }

    /* 获取设备信息 */
    /* rte_cryptodev_info_get 在某些 DPDK 版本中返回 void */
    rte_cryptodev_info_get(dev_id, &dev_info);

    /* 检查 driver_name 是否有效 */
    if (dev_info.driver_name == NULL) {
        CRYPTO_ERROR("Failed to get driver name for cryptodev %u", dev_id);
        return -EINVAL;
    }

    CRYPTO_INFO("Configuring cryptodev %u: %s", dev_id, dev_info.driver_name ? dev_info.driver_name : "unknown");

    /* 配置设备 */
    memset(&dev_config, 0, sizeof(dev_config));
    dev_config.socket_id = rte_socket_id();
    dev_config.nb_queue_pairs = RTE_MIN(dev_info.max_nb_queue_pairs, CRYPTO_MAX_QUEUE_PAIRS);
    if (dev_config.nb_queue_pairs == 0) {
        dev_config.nb_queue_pairs = 1;  /* 至少需要一个队列对 */
    }

    /* 检查设备是否支持对称加密 */
    if (!(dev_info.feature_flags & RTE_CRYPTODEV_FF_SYMMETRIC_CRYPTO)) {
        CRYPTO_INFO("Device %u does not support symmetric crypto, disabling feature flag", dev_id);
        dev_config.ff_disable = 0;  /* 不禁用任何特性 */
    } else {
        dev_config.ff_disable = RTE_CRYPTODEV_FF_SYMMETRIC_CRYPTO;
    }

    CRYPTO_INFO("Configuring device %u with %u queue pairs", dev_id, dev_config.nb_queue_pairs);
    ret = rte_cryptodev_configure(dev_id, &dev_config);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to configure cryptodev %u: %d (errno: %d - %s)",
                    dev_id, ret, errno, strerror(errno));
        return ret;
    }

    /* 配置队列对 */
    memset(&qp_conf, 0, sizeof(qp_conf));
    qp_conf.nb_descriptors = CRYPTO_MAX_OPS;
    qp_conf.mp_session = crypto_session_pool;

    for (qp_id = 0; qp_id < dev_config.nb_queue_pairs; qp_id++) {
        CRYPTO_INFO("Setting up queue pair %u on device %u", qp_id, dev_id);
        ret = rte_cryptodev_queue_pair_setup(dev_id, qp_id, &qp_conf, rte_socket_id());
        if (ret < 0) {
            CRYPTO_ERROR("Failed to setup queue pair %u on cryptodev %u: %d (errno: %d - %s)",
                        qp_id, dev_id, ret, errno, strerror(errno));
            return ret;
        }
    }

    /* 启动设备 */
    CRYPTO_INFO("Starting cryptodev %u", dev_id);
    ret = rte_cryptodev_start(dev_id);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to start cryptodev %u: %d (errno: %d - %s)",
                    dev_id, ret, errno, strerror(errno));
        return ret;
    }

    CRYPTO_INFO("Cryptodev %u configured successfully with %u queue pairs", dev_id, dev_config.nb_queue_pairs);
    return 0;
}

/* 初始化 cryptodev */
int dpdk_openssl_cryptodev_init(void)
{
    uint8_t dev_id;
    int ret;

    if (cryptodev_initialized) {
        CRYPTO_INFO("Cryptodev already initialized");
        return 0;
    }

    CRYPTO_INFO("Initializing OpenSSL cryptodev");

    /* 检查 DPDK EAL 是否已初始化 */
    /* 注意：rte_eal_get_configuration 返回的是一个结构体指针 */
    if (!rte_eal_has_hugepages()) {
        CRYPTO_ERROR("DPDK EAL not properly initialized (no hugepages)");
        return -EINVAL;
    }

    /* 检查 DPDK 版本和配置 */
    CRYPTO_INFO("DPDK version: %s", rte_version());
    CRYPTO_INFO("DPDK configuration: %s", rte_eal_get_configuration() ? "valid" : "invalid");
    CRYPTO_INFO("DPDK process type: %s", rte_eal_process_type() == RTE_PROC_PRIMARY ? "primary" : "secondary");

    /* 检查 DPDK 内存和 hugepage 配置 */
    void *cfg = (void *)rte_eal_get_configuration();
    if (cfg) {
        CRYPTO_INFO("DPDK configuration available");
    } else {
        CRYPTO_ERROR("DPDK configuration not available");
    }

    /* 检查用户权限 */
    if (geteuid() != 0) {
        CRYPTO_ERROR("Not running as root, may cause permission issues");
    } else {
        CRYPTO_INFO("Running as root, permissions should be sufficient");
    }

    /* 创建会话池 */
    if (crypto_session_pool == NULL) {
        CRYPTO_INFO("Creating crypto session pool");

        /* 检查 librte_cryptodev 是否可用 */
        struct stat st;
        if (stat("/usr/lib/librte_cryptodev.so", &st) != 0 &&
            stat("/usr/local/lib/librte_cryptodev.so", &st) != 0) {
            CRYPTO_ERROR("librte_cryptodev.so not found, crypto may not be supported");
            /* 继续尝试，因为库可能以其他方式链接 */
        }

        crypto_session_pool = rte_cryptodev_sym_session_pool_create(
            "crypto_session_pool",
            CRYPTO_MAX_SESSIONS,
            0,  /* 私有数据大小，由驱动决定 */
            CRYPTO_SESSION_POOL_CACHE_SIZE,
            0,
            rte_socket_id());

        if (crypto_session_pool == NULL) {
            CRYPTO_ERROR("Failed to create session pool (errno: %d - %s)",
                        errno, strerror(errno));
            return -ENOMEM;
        }

        CRYPTO_INFO("Crypto session pool created successfully");
    }

    /* 创建操作池 */
    if (crypto_op_pool == NULL) {
        CRYPTO_INFO("Creating crypto op pool");

        crypto_op_pool = rte_crypto_op_pool_create(
            "crypto_op_pool",
            RTE_CRYPTO_OP_TYPE_SYMMETRIC,
            CRYPTO_MAX_OPS,
            CRYPTO_SESSION_POOL_CACHE_SIZE,
            0,  /* 私有数据大小 */
            rte_socket_id());

        if (crypto_op_pool == NULL) {
            CRYPTO_ERROR("Failed to create op pool (errno: %d - %s)",
                        errno, strerror(errno));
            rte_mempool_free(crypto_session_pool);
            crypto_session_pool = NULL;
            return -ENOMEM;
        }

        CRYPTO_INFO("Crypto op pool created successfully");
    }

    /* 创建 OpenSSL cryptodev 设备 */
    CRYPTO_INFO("Creating OpenSSL cryptodev device");
    dev_id = create_openssl_cryptodev();
    if (dev_id == 0xFF) {  /* 使用 0xFF 作为错误标志，而不是 -1 */
        CRYPTO_ERROR("Failed to create OpenSSL cryptodev");

        /* 尝试使用 vdev 参数创建 */
        CRYPTO_INFO("Trying to create OpenSSL PMD with vdev parameter");
        ret = system("lsmod | grep -q crypto_openssl || echo 'crypto_openssl module not loaded'");

        rte_mempool_free(crypto_op_pool);
        rte_mempool_free(crypto_session_pool);
        crypto_op_pool = NULL;
        crypto_session_pool = NULL;
        return -1;
    }

    /* 配置 cryptodev 设备 */
    CRYPTO_INFO("Configuring cryptodev device %u", dev_id);
    ret = configure_cryptodev(dev_id);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to configure cryptodev %u: %d", dev_id, ret);
        rte_mempool_free(crypto_op_pool);
        rte_mempool_free(crypto_session_pool);
        crypto_op_pool = NULL;
        crypto_session_pool = NULL;
        return ret;
    }

    cryptodev_initialized = 1;
    CRYPTO_INFO("OpenSSL cryptodev initialization complete");

    return 0;
}

/* 清理 cryptodev 资源 */
void dpdk_openssl_cryptodev_uninit(void)
{
    uint8_t dev_id;

    if (!cryptodev_initialized)
        return;

    CRYPTO_INFO("Uninitializing OpenSSL cryptodev");

    /* 停止所有 cryptodev 设备 */
    for (dev_id = 0; dev_id < rte_cryptodev_count(); dev_id++) {
        struct rte_cryptodev_info info;
        rte_cryptodev_info_get(dev_id, &info);
        if (strcmp(info.driver_name, "crypto_openssl") == 0) {
            rte_cryptodev_stop(dev_id);
            CRYPTO_INFO("Stopped cryptodev %u: %s", dev_id, info.driver_name);
        }
    }

    /* 释放资源 */
    if (crypto_op_pool != NULL) {
        rte_mempool_free(crypto_op_pool);
        crypto_op_pool = NULL;
    }

    if (crypto_session_pool != NULL) {
        rte_mempool_free(crypto_session_pool);
        crypto_session_pool = NULL;
    }

    cryptodev_initialized = 0;
    CRYPTO_INFO("OpenSSL cryptodev uninitialization complete");
}
