/* SPDX-License-Identifier: (BSD-3-Clause OR GPL-2.0)
 * Copyright(c) 2010-2014 Intel Corporation
 */

#ifndef _RTE_PCI_DEV_FEATURES_H
#define _RTE_PCI_DEV_FEATURES_H

#ifdef __cplusplus
extern "C" {
#endif

#include <rte_pci_dev_feature_defs.h>

#define RTE_INTR_MODE_NONE_NAME "none"
#define RTE_INTR_MODE_LEGACY_NAME "legacy"
#define RTE_INTR_MODE_MSI_NAME "msi"
#define RTE_INTR_MODE_MSIX_NAME "msix"

#ifdef __cplusplus
}
#endif

#endif
