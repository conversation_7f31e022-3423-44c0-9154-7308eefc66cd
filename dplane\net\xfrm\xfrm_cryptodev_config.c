/*
 * xfrm_cryptodev_config.c
 *
 * Description: Configuration and command line interface for cryptodev
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"

/* 全局配置变量 */
int cryptodev_enabled = 1; /* 默认启用 */
int cryptodev_async_mode = 1; /* 默认启用异步模式 */
static int cryptodev_debug_level = 1; /* 默认调试级别 */
static int cryptodev_sw_fallback = 1; /* 默认启用软件回退 */
static int cryptodev_stats_interval = 60; /* 默认统计信息间隔（秒） */

struct xfrm_cryptodev_stats crypto_stats;
static DEFINE_SPINLOCK(crypto_stats_lock);

/* 统计函数实现 */
void update_crypto_stats_submit(void)
{
    unsigned long flags;

    spin_lock_irqsave(&crypto_stats_lock, flags);
    crypto_stats.total_submitted++;
    crypto_stats.current_in_flight++;
    spin_unlock_irqrestore(&crypto_stats_lock, flags);
}

void update_crypto_stats_complete(unsigned long processing_time)
{
    unsigned long flags;

    spin_lock_irqsave(&crypto_stats_lock, flags);
    crypto_stats.total_completed++;
    crypto_stats.current_in_flight--;

    /* 更新处理时间统计 */
    if (processing_time > 0) {
        crypto_stats.total_processing_time += processing_time;
        if (processing_time > crypto_stats.max_processing_time) {
            crypto_stats.max_processing_time = processing_time;
        }
        if (crypto_stats.min_processing_time == 0 ||
            processing_time < crypto_stats.min_processing_time) {
            crypto_stats.min_processing_time = processing_time;
        }
    }
    spin_unlock_irqrestore(&crypto_stats_lock, flags);
}

void update_crypto_stats_fail(void)
{
    unsigned long flags;

    spin_lock_irqsave(&crypto_stats_lock, flags);
    crypto_stats.total_failed++;
    crypto_stats.current_in_flight--;
    spin_unlock_irqrestore(&crypto_stats_lock, flags);
}

void reset_crypto_stats(void)
{
    unsigned long flags;

    spin_lock_irqsave(&crypto_stats_lock, flags);
    memset(&crypto_stats, 0, sizeof(crypto_stats));
    spin_unlock_irqrestore(&crypto_stats_lock, flags);
}

/* 更新提交统计信息 */
void update_crypto_stats_submit(void)
{
    spin_lock(&crypto_stats_lock);
    crypto_stats.submitted++;
    spin_unlock(&crypto_stats_lock);
}

/* 更新完成统计信息 */
void update_crypto_stats_complete(uint64_t time_ns)
{
    spin_lock(&crypto_stats_lock);
    crypto_stats.completed++;
    crypto_stats.total_time_ns += time_ns;
    spin_unlock(&crypto_stats_lock);
}

/* 更新失败统计信息 */
void update_crypto_stats_fail(void)
{
    spin_lock(&crypto_stats_lock);
    crypto_stats.failed++;
    spin_unlock(&crypto_stats_lock);
}

/* 更新软件回退统计信息 */
void update_crypto_stats_sw_fallback(void)
{
    spin_lock(&crypto_stats_lock);
    crypto_stats.sw_fallback++;
    spin_unlock(&crypto_stats_lock);
}

/* 重置统计信息 */
void reset_crypto_stats(void)
{
    spin_lock(&crypto_stats_lock);
    memset(&crypto_stats, 0, sizeof(crypto_stats));
    spin_unlock(&crypto_stats_lock);
}

/* 获取统计信息 */
void get_crypto_stats(struct xfrm_cryptodev_stats *stats)
{
    spin_lock(&crypto_stats_lock);
    memcpy(stats, &crypto_stats, sizeof(*stats));
    spin_unlock(&crypto_stats_lock);
}

/* 打印统计信息 */
void print_crypto_stats(void)
{
    struct xfrm_cryptodev_stats stats;
    uint64_t avg_time_ns = 0;

    get_crypto_stats(&stats);

    if (stats.completed > 0)
        avg_time_ns = stats.total_time_ns / stats.completed;

    printf("Cryptodev Statistics:\n");
    printf("  Submitted operations: %llu\n", (unsigned long long)stats.submitted);
    printf("  Completed operations: %llu\n", (unsigned long long)stats.completed);
    printf("  Failed operations: %llu\n", (unsigned long long)stats.failed);
    printf("  Software fallbacks: %llu\n", (unsigned long long)stats.sw_fallback);
    printf("  Average processing time: %llu ns\n", (unsigned long long)avg_time_ns);
}

/* 打印设备信息 */
void print_cryptodev_info(void)
{
    uint8_t dev_id;
    struct rte_cryptodev_info dev_info;
    struct rte_cryptodev_stats dev_stats;

    printf("Cryptodev Devices:\n");

    for (dev_id = 0; dev_id < xfrm_cryptodev_ctx.nb_devs; dev_id++) {
        uint8_t real_dev_id = xfrm_cryptodev_ctx.dev_ids[dev_id];

        rte_cryptodev_info_get(real_dev_id, &dev_info);
        rte_cryptodev_stats_get(real_dev_id, &dev_stats);

        printf("  Device %u: (driver %s)\n",
               real_dev_id, dev_info.driver_name);
        printf("    Active: %s\n", xfrm_cryptodev_ctx.dev_active[dev_id] ? "Yes" : "No");
        printf("    Max queue pairs: %u\n", dev_info.max_nb_queue_pairs);
        printf("    Capabilities: 0x%llx\n", (unsigned long long)dev_info.feature_flags);
        printf("    Enqueued count: %llu\n", (unsigned long long)dev_stats.enqueued_count);
        printf("    Dequeued count: %llu\n", (unsigned long long)dev_stats.dequeued_count);
    }
}

/* 设置 cryptodev 启用状态 */
int set_cryptodev_enabled(int enabled)
{
    if (enabled == cryptodev_enabled)
        return 0;

    if (enabled) {
        /* 启用 cryptodev - 通过统一初始化函数 */
        extern int unified_cryptodev_init(void);
        int ret = unified_cryptodev_init();
        if (ret < 0) {
            printf("Failed to initialize cryptodev: %d\n", ret);
            return ret;
        }

        cryptodev_enabled = 1;
        printf("Cryptodev support enabled\n");
    } else {
        /* 禁用 cryptodev - 通过统一清理函数 */
        extern void unified_cryptodev_uninit(void);
        unified_cryptodev_uninit();
        cryptodev_enabled = 0;
        printf("Cryptodev support disabled\n");
    }

    return 0;
}

/* 设置调试级别 */
void set_cryptodev_debug_level(int level)
{
    cryptodev_debug_level = level;
    printf("Cryptodev debug level set to %d\n", level);
}

/* 设置软件回退选项 */
void set_cryptodev_sw_fallback(int enabled)
{
    cryptodev_sw_fallback = enabled;
    printf("Cryptodev software fallback %s\n", enabled ? "enabled" : "disabled");
}

/* 设置统计信息间隔 */
void set_cryptodev_stats_interval(int interval)
{
    cryptodev_stats_interval = interval;
    printf("Cryptodev statistics interval set to %d seconds\n", interval);
}

/* 获取 cryptodev 启用状态 */
int get_cryptodev_enabled(void)
{
    return cryptodev_enabled;
}

/* 获取调试级别 */
int get_cryptodev_debug_level(void)
{
    return cryptodev_debug_level;
}

/* 获取软件回退选项 */
int get_cryptodev_sw_fallback(void)
{
    return cryptodev_sw_fallback;
}

/* 获取统计信息间隔 */
int get_cryptodev_stats_interval(void)
{
    return cryptodev_stats_interval;
}

/* 重置设备 */
int reset_cryptodev_device(uint8_t dev_id)
{
    uint8_t real_dev_id;
    int i;

    /* 查找真实设备 ID */
    for (i = 0; i < xfrm_cryptodev_ctx.nb_devs; i++) {
        if (i == dev_id) {
            real_dev_id = xfrm_cryptodev_ctx.dev_ids[i];
            break;
        }
    }

    if (i >= xfrm_cryptodev_ctx.nb_devs) {
        printf("Invalid device ID: %u\n", dev_id);
        return -EINVAL;
    }

    /* 停止设备 */
    rte_cryptodev_stop(real_dev_id);

    /* 重新配置设备 */
    struct rte_cryptodev_config config = {
        .nb_queue_pairs = 1,
        .socket_id = rte_socket_id()
    };

    struct rte_cryptodev_qp_conf qp_conf = {
        .nb_descriptors = CRYPTODEV_QUEUE_SIZE,
        .mp_session = xfrm_cryptodev_ctx.session_pool
    };

    /* 重新配置设备 */
    if (rte_cryptodev_configure(real_dev_id, &config) < 0) {
        printf("Failed to reconfigure device %d\n", real_dev_id);
        xfrm_cryptodev_ctx.dev_active[i] = 0;
        return -EIO;
    }

    /* 重新配置队列对 */
    if (rte_cryptodev_queue_pair_setup(real_dev_id, 0, &qp_conf, rte_socket_id()) < 0) {
        printf("Failed to setup queue pair for device %d\n", real_dev_id);
        xfrm_cryptodev_ctx.dev_active[i] = 0;
        return -EIO;
    }

    /* 重新启动设备 */
    if (rte_cryptodev_start(real_dev_id) < 0) {
        printf("Failed to restart device %d\n", real_dev_id);
        /* 标记设备不可用 */
        xfrm_cryptodev_ctx.dev_active[i] = 0;
        return -EIO;
    }

    printf("Device %d reset successfully\n", real_dev_id);
    xfrm_cryptodev_ctx.dev_active[i] = 1;

    return 0;
}

/* 命令行处理函数 */
int cryptodev_cmd_handler(int argc, char **argv)
{
    if (argc < 2) {
        printf("Usage: cryptodev <command> [options]\n");
        printf("Commands:\n");
        printf("  status              - Show cryptodev status\n");
        printf("  enable              - Enable cryptodev support\n");
        printf("  disable             - Disable cryptodev support\n");
        printf("  debug <level>       - Set debug level (0-3)\n");
        printf("  fallback <0|1>      - Set software fallback option\n");
        printf("  stats               - Show statistics\n");
        printf("  reset_stats         - Reset statistics\n");
        printf("  devices             - Show device information\n");
        printf("  reset_device <id>   - Reset specific device\n");
        return -EINVAL;
    }

    if (strcmp(argv[1], "status") == 0) {
        printf("Cryptodev Status:\n");
        printf("  Enabled: %s\n", cryptodev_enabled ? "Yes" : "No");
        printf("  Debug level: %d\n", cryptodev_debug_level);
        printf("  Software fallback: %s\n", cryptodev_sw_fallback ? "Enabled" : "Disabled");
        printf("  Statistics interval: %d seconds\n", cryptodev_stats_interval);
        return 0;
    } else if (strcmp(argv[1], "enable") == 0) {
        return set_cryptodev_enabled(1);
    } else if (strcmp(argv[1], "disable") == 0) {
        return set_cryptodev_enabled(0);
    } else if (strcmp(argv[1], "debug") == 0) {
        if (argc < 3) {
            printf("Usage: cryptodev debug <level>\n");
            return -EINVAL;
        }
        int level = atoi(argv[2]);
        set_cryptodev_debug_level(level);
        return 0;
    } else if (strcmp(argv[1], "fallback") == 0) {
        if (argc < 3) {
            printf("Usage: cryptodev fallback <0|1>\n");
            return -EINVAL;
        }
        int enabled = atoi(argv[2]);
        set_cryptodev_sw_fallback(enabled);
        return 0;
    } else if (strcmp(argv[1], "stats") == 0) {
        print_crypto_stats();
        return 0;
    } else if (strcmp(argv[1], "reset_stats") == 0) {
        reset_crypto_stats();
        printf("Statistics reset\n");
        return 0;
    } else if (strcmp(argv[1], "devices") == 0) {
        print_cryptodev_info();
        return 0;
    } else if (strcmp(argv[1], "reset_device") == 0) {
        if (argc < 3) {
            printf("Usage: cryptodev reset_device <id>\n");
            return -EINVAL;
        }
        uint8_t dev_id = atoi(argv[2]);
        return reset_cryptodev_device(dev_id);
    } else {
        printf("Unknown command: %s\n", argv[1]);
        return -EINVAL;
    }
}
