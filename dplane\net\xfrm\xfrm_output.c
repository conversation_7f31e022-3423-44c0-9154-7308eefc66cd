/*
 * xfrm_output.c - Common IPsec encapsulation code.
 *
 * Copyright (c) 2007 <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version
 * 2 of the License, or (at your option) any later version.
 */

//#include <linux/errno.h>
#include <linux/module.h>
#include <linux/netdevice.h>
#include <linux/netfilter.h>
#include <linux/skbuff.h>
#include <linux/slab.h>
#include <linux/spinlock.h>
#include <net/xfrm.h>
#include <ip_policy.h>
#include <net/ip_policy.h>
#include <net/netfilter/nf_conntrack.h>
#include <linux/ipv6.h>
#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_config.h"

char *xfrm_skb_saddr(struct sk_buff *skb, struct xfrm_state *x)
{
	if (x && x->sel.ip4in6)
		return ip6_string(&ipv6_hdr(skb)->saddr);
	else
		return ip4_string(ip_hdr(skb)->saddr);
}

char *xfrm_skb_daddr(struct sk_buff *skb, struct xfrm_state *x)
{
	if (x && x->sel.ip4in6)
		return ip6_string2(&ipv6_hdr(skb)->daddr);
	else
		return ip4_string2(ip_hdr(skb)->daddr);

}

static int xfrm_skb_check_space(struct sk_buff *skb)
{
	struct xfrm_sa_bundle *sp = skb->sp;

	if (!sp || sp->xfrm_nr < 1 ||sp->xfrm_nr > XFRM_MAX_DEPTH) {
		return 1;
	}

	return pskb_expand_head(skb, sp->head_room, sp->tail_room, GFP_ATOMIC);
}

int xfrm_inner_extract_output(struct xfrm_state *x, struct sk_buff *skb)
{
#if 0
	struct xfrm_mode *inner_mode;
	if (x->sel.family == AF_UNSPEC)
		inner_mode = xfrm_ip2inner_mode(x,
				xfrm_af2proto(skb_dst(skb)->ops->family));
	else
		inner_mode = x->inner_mode;

	if (inner_mode == NULL)
		return -EAFNOSUPPORT;
	return inner_mode->afinfo->extract_output(x, skb);
#endif
	return 0;
}

int xfrm_output2(struct sk_buff *skb)
{
	struct net *net = &init_net;
	int err = 0;
	int state_index = 0;
	struct xfrm_state *x;

	x = skb->sp->xvec_out[state_index++];
	if (!x) {
		err = 1;
		IPSEC_DEBUG_DROP("output* no sa in the sp. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		goto error_nolock;
	}

	err = xfrm_skb_check_space(skb);
	if (err) {
		IPSEC_DEBUG_DROP("output* check space error:%d, maybe insufficient memory. daddr %s  saddr %s, len %d\n",
			err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		XFRM_INC_STATS(net, LINUX_MIB_XFRMOUTERROR);
		goto error_nolock;
	}

	skb_reset_network_header(skb);
	xfrm_adjust_tcp_mss(skb);

#ifdef XFRM_DEBUG
	if (dp_debug_ipsec) {
		IPSEC_DEBUG("output* dump-1. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		xfrm_pkt_dump(skb);
	}
#endif

	do {

		/**tunnel or transport mode xfrm4_tunnel_mode xfrm4_transport_mode*/
		err = x->outer_mode->output(x, skb);
		if (err) {
			IPSEC_DEBUG_DROP("output* mode output error:%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMOUTSTATEMODEERROR);
			goto error_nolock;
		}

		spin_lock_bh(&x->lock);
		err = xfrm_state_check_expire(x, skb->sp);
		if (err) {
			IPSEC_DEBUG_DROP("output* SA is expired :%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMOUTSTATEEXPIRED);
			goto error;
		}

		err = x->repl->overflow(x, skb);
		if (err) {
			IPSEC_DEBUG_DROP("output* SA seq error :%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMOUTSTATESEQERROR);
			goto error;
		}

		x->curlft.bytes += skb->len;
		x->curlft.packets++;

		spin_unlock_bh(&x->lock);

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("output* dump-2. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		/* 根据全局配置选择处理路径 */
#ifdef ENABLE_CRYPTODEV
		switch (xfrm_get_crypto_mode()) {
		case XFRM_CRYPTO_MODE_CRYPTODEV:
			/* 纯cryptodev模式 */
			if (xfrm_has_cryptodev_session(x) && cryptodev_enabled) {
				err = xfrm_cryptodev_encrypt(x, skb);
				if (err == 0) {
					goto crypto_success;
				} else if (err == -EINPROGRESS) {
					xfrm_state_hold(x);
					return 0;
				} else {
					IPSEC_DEBUG("Cryptodev encrypt failed: %d, falling back to software\n", err);
					xfrm_cryptodev_cleanup_context(x);
				}
			}
			break;

		case XFRM_CRYPTO_MODE_AUTO:
			/* 自动模式 */
			if (xfrm_has_cryptodev_session(x) && cryptodev_enabled) {
				err = xfrm_cryptodev_encrypt(x, skb);
				if (err == 0) {
					goto crypto_success;
				} else if (err == -EINPROGRESS) {
					xfrm_state_hold(x);
					return 0;
				}
				IPSEC_DEBUG("Cryptodev failed, using software fallback\n");
			}
			break;

		case XFRM_CRYPTO_MODE_SOFTWARE:
		default:
			/* 纯软件模式 */
			break;
		}
#endif

		/**ah_type or esp_type*/
		err = x->type->output(x, skb);

crypto_success:
		if (err) {
			IPSEC_DEBUG_DROP("output* type output error :%d. daddr %s  saddr %s, len %d\n",
				err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			XFRM_INC_STATS(net, LINUX_MIB_XFRMOUTSTATEPROTOERROR);
			goto error_nolock;
		}

#ifdef XFRM_DEBUG
		if (dp_debug_ipsec) {
			IPSEC_DEBUG("output* dump-3. daddr %s  saddr %s, len %d\n",
				ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
			xfrm_pkt_dump(skb);
		}
#endif

		if (state_index < skb->sp->xfrm_nr)
			x = skb->sp->xvec_out[state_index++];
		else
			break;

	} while (x && !(x->outer_mode->flags & XFRM_MODE_FLAG_TUNNEL));

	//skb->ipsec_done = 1;
	skb_vpn_flag_set(skb,SKB_VPN_FLAG_IPSEC_ENCRYPT);

	if (x->sel.ip4in6)
		;
	else
		ip_send_check(ip_hdr(skb));

	nf_reset(skb);
	skb->is_local = 1;

	IPSEC_DEBUG("output* successful. daddr %s  saddr %s, len %d\n",
			xfrm_skb_daddr(skb, x), xfrm_skb_saddr(skb, x), skb->len);
	//需要考虑IPv6,要封装一下...FIXME
	if (x->sel.ip4in6)
		err = ip6_local_out(skb);
	else
		err = ip_local_out(skb);

out_exit:
	return err;
error:
	spin_unlock_bh(&x->lock);
error_nolock:
	IPSEC_DEBUG_DROP("xfrm out2 droped packet:%d. daddr %s  saddr %s, len %d\n",
			err,ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
	kfree_skb(skb);
	goto out_exit;
}

int xfrm_output(struct sk_buff *skb)
{
	struct xfrm_sa_bundle *sp; //security policy
	struct nf_conn*ct = (struct nf_conn*)skb->nfct;
	struct policy_entry *fw_policy;

	if (!ct) {
		IPSEC_DEBUG_DROP("xfrm out droped packet. no session. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		kfree_skb(skb);
		return 1;
	}

	fw_policy = (struct policy_entry *)ct->policy;
	if (!fw_policy || fw_policy->action != PLCY_ACTION_IPSEC) {
		IPSEC_DEBUG_DROP("xfrm out droped packet. no policy or not IPsec policy. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		kfree_skb(skb);
		return 1;
	}

	sp = xfrm_lookup(skb, AF_INET, fw_policy->id);
	if (!sp) {
		IPSEC_DEBUG_DROP("xfrm out droped packet. no sp. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		kfree_skb(skb);
		return 1;
	}

	if (sp->state != XFRM_STATE_VALID) {
		IPSEC_DEBUG_DROP("xfrm out droped packet. sp state is invalid. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		xfrm_sab_put(sp);
		kfree_skb(skb);
		return 1;
	}

	if (!sp->xfrm_nr) {
		//should not happen!
		IPSEC_DEBUG_DROP("xfrm out droped packet. no saboundle in sp. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);
		xfrm_sab_put(sp);
		kfree_skb(skb);
		return 1;
	}

	skb->sp = sp;

	IPSEC_DEBUG("output* find the sa boundle. daddr %s  saddr %s, len %d\n",
			ip4_string(ip_hdr(skb)->daddr),ip4_string2(ip_hdr(skb)->saddr), skb->len);

	return xfrm_output2(skb);
}

/* 处理 cryptodev 完成的加密数据包 */
void xfrm_output_crypto_done(struct sk_buff *skb, int err)
{
	struct xfrm_cryptodev_metadata *meta;
	struct xfrm_state *x;
	struct net *net = &init_net;

	/* 获取元数据 */
	meta = (struct xfrm_cryptodev_metadata *)skb->cb;
	x = meta->x;

	/* 如果操作失败，释放资源并返回 */
	if (err) {
		IPSEC_DEBUG_DROP("Cryptodev encrypt failed: %d\n", err);
		xfrm_state_put(x);
		kfree_skb(skb);
		return;
	}

	/* 继续处理加密后的数据包 */
	IPSEC_DEBUG("Cryptodev encrypt completed successfully\n");

	/* 设置 VPN 标志 */
	skb_vpn_flag_set(skb, SKB_VPN_FLAG_IPSEC_ENCRYPT);

	/* 检查 IP 版本 */
	if (x->sel.ip4in6)
		;
	else
		ip_send_check(ip_hdr(skb));

	/* 重置 netfilter 状态 */
	nf_reset(skb);
	skb->is_local = 1;

	IPSEC_DEBUG("output* crypto_done successful. daddr %s saddr %s, len %d\n",
			xfrm_skb_daddr(skb, x), xfrm_skb_saddr(skb, x), skb->len);

	/* 发送数据包 */
	int ret;
	if (x->sel.ip4in6)
		ret = ip6_local_out(skb);
	else
		ret = ip_local_out(skb);

	/* 释放 SA 引用 */
	xfrm_state_put(x);

	if (ret != 0) {
		IPSEC_DEBUG_DROP("Cryptodev encrypt: ip_local_out failed: %d\n", ret);
	}
}
