# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2018 <PERSON> <<EMAIL>>

arch_headers = files(
        'rte_altivec.h',
        'rte_atomic.h',
        'rte_byteorder.h',
        'rte_cpuflags.h',
        'rte_cycles.h',
        'rte_io.h',
        'rte_memcpy.h',
        'rte_pause.h',
        'rte_power_intrinsics.h',
        'rte_prefetch.h',
        'rte_rwlock.h',
        'rte_spinlock.h',
        'rte_vect.h',
)
install_headers(arch_headers, subdir: get_option('include_subdir_arch'))
