/*
 * xfrm_cryptodev_ops.c
 *
 * Description: DPDK cryptodev operations for xfrm framework
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"

/* 将 sk_buff 转换为 rte_mbuf - 基于原有DPDK机制 */
struct rte_mbuf *skb_to_mbuf(struct sk_buff *skb)
{
    struct rte_mbuf *m;

    /* 在DPDK框架下，skb->work直接指向关联的mbuf */
    m = (struct rte_mbuf *)skb->work;
    if (unlikely(!m)) {
        CRYPTO_ERROR("skb->work is NULL, invalid skb for cryptodev\n");
        return NULL;
    }

    /* 更新mbuf的数据信息以反映skb的当前状态 */
    m->data_len = skb->len;
    m->pkt_len = skb->len;
    m->data_off = skb->data - skb->head;

    /* 在mbuf的私有区域保存skb指针，用于后续恢复 */
    if (rte_pktmbuf_priv_size(m->pool) >= sizeof(struct sk_buff *)) {
        void *priv = rte_mbuf_to_priv(m);
        *((struct sk_buff **)priv) = skb;
    } else {
        CRYPTO_ERROR("mbuf pool has no private area for skb pointer\n");
        return NULL;
    }

    return m;
}

/* 将 rte_mbuf 转换回 sk_buff - 基于原有DPDK机制 */
struct sk_buff *mbuf_to_skb(struct rte_mbuf *m)
{
    struct sk_buff *skb;

    /* 在DPDK框架下，我们需要从mbuf找回原始skb */
    /* 由于skb->work指向mbuf，我们需要通过其他方式找回skb */
    /* 这里使用mbuf的私有区域存储skb指针 */
    if (rte_pktmbuf_priv_size(m->pool) >= sizeof(struct sk_buff *)) {
        void *priv = rte_mbuf_to_priv(m);
        skb = *((struct sk_buff **)priv);
    } else {
        CRYPTO_ERROR("mbuf pool has no private area for skb pointer\n");
        return NULL;
    }

    if (unlikely(!skb)) {
        CRYPTO_ERROR("No skb pointer found in mbuf private area\n");
        return NULL;
    }

    /* 更新skb的长度信息以反映mbuf处理后的状态 */
    if (skb->len != m->data_len) {
        if (m->data_len > skb->len) {
            skb_put(skb, m->data_len - skb->len);
        } else {
            skb_trim(skb, m->data_len);
        }
    }

    /* 在DPDK框架下，skb和mbuf共享数据区域，不需要拷贝 */
    /* 只需要确保skb的数据指针正确 */
    skb->data = skb->head + m->data_off;

    return skb;
}

/* 设置加密操作参数 */
int xfrm_set_crypto_op_params(struct xfrm_state *x, struct rte_crypto_op *op, struct sk_buff *skb)
{
    struct rte_crypto_sym_op *sym_op = op->sym;
    struct ip_esp_hdr *esph;
    int alen, clen;

    /* 获取 ESP 头 */
    esph = (struct ip_esp_hdr *)skb_transport_header(skb);

    /* 设置 IV - 注意：在某些版本的 DPDK 中，cipher 结构体可能没有 iv 成员 */
    /* 使用 data 成员代替 */
    sym_op->cipher.data.offset = (uint8_t *)esph - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
    sym_op->cipher.data.length = x->enc_key_len;

    /* 设置加密数据 */
    clen = skb->len - skb_transport_offset(skb) - sizeof(struct ip_esp_hdr) - x->ealg->ivsize;
    if (x->props.mode == XFRM_MODE_TRANSPORT) {
        /* 加密 */
        sym_op->cipher.data.offset = (uint8_t *)esph->enc_data + x->ealg->ivsize - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
        sym_op->cipher.data.length = clen;
    } else {
        /* 解密 */
        sym_op->cipher.data.offset = (uint8_t *)esph->enc_data + x->ealg->ivsize - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
        sym_op->cipher.data.length = clen - x->aalg->authsize;
    }

    /* 设置认证数据 */
    alen = skb->len - skb_transport_offset(skb) - x->aalg->authsize;
    sym_op->auth.data.offset = (uint8_t *)esph - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
    sym_op->auth.data.length = alen;

    /* 设置认证摘要 - 注意：在某些版本的 DPDK 中，digest 结构体可能没有 length 成员 */
    sym_op->auth.digest.data = (uint8_t *)esph + alen;
    sym_op->auth.digest.phys_addr = rte_pktmbuf_mtophys_offset(sym_op->m_src,
                                   (uint8_t *)esph + alen - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *));

    return 0;
}

/* 同步加密函数 - 立即完成处理 */
int xfrm_cryptodev_encrypt_sync(struct xfrm_state *x, struct sk_buff *skb)
{
    /* 注意：xfrm_state 结构体中没有 crypto_session 成员，需要从上下文中获取 */
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;
    struct xfrm_cryptodev_metadata meta;

    CRYPTO_DEBUG("Encrypting packet with cryptodev: spi=%u\n", x->id.spi);

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 将 skb 转换为 mbuf */
    m = skb_to_mbuf(skb);
    if (!m) {
        CRYPTO_ERROR("Failed to convert skb to mbuf\n");
        rte_crypto_op_free(op);
        return -ENOMEM;
    }

    /* 设置加密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置加密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存元数据 */
    meta.x = x;
    meta.dir = XFRM_POLICY_OUT;
    meta.orig_data = skb->data;
    meta.orig_len = skb->len;
    meta.seq = XFRM_SKB_CB(skb)->seq.output.low;

    /* 将元数据保存到 skb 中 */
    memcpy(skb->cb, &meta, sizeof(meta));

    /* 同步处理：入队并立即等待完成 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 同步等待操作完成 */
    struct rte_crypto_op *completed_ops[1];
    int completed = 0;
    int max_retries = 1000;  /* 最大重试次数 */

    while (completed == 0 && max_retries-- > 0) {
        completed = rte_cryptodev_dequeue_burst(
            crypto_session->dev_id,
            crypto_session->qp_id,
            completed_ops, 1);

        if (completed == 0) {
            /* 短暂等待 */
            rte_delay_us(1);
        }
    }

    if (completed != 1) {
        CRYPTO_ERROR("Sync encrypt timeout or failed to dequeue\n");
        return -ETIMEDOUT;
    }

    /* 检查操作状态 */
    if (completed_ops[0]->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
        CRYPTO_ERROR("Crypto operation failed with status: %d\n", completed_ops[0]->status);
        rte_crypto_op_free(completed_ops[0]);
        return -EIO;
    }

    /* 恢复数据到skb */
    struct sk_buff *result_skb = mbuf_to_skb(m);
    if (!result_skb) {
        CRYPTO_ERROR("Failed to convert mbuf back to skb\n");
        rte_crypto_op_free(completed_ops[0]);
        return -ENOMEM;
    }

    /* 清理资源 */
    rte_crypto_op_free(completed_ops[0]);

    /* 更新统计信息 */
    update_crypto_stats_submit();
    update_crypto_stats_complete();

    /* 同步处理完成 */
    return 0;
}

/* 异步加密函数 - 提交后异步完成 */
int xfrm_cryptodev_encrypt_async(struct xfrm_state *x, struct sk_buff *skb)
{
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 转换 skb 到 mbuf */
    m = skb_to_mbuf(skb);
    if (!m)
        return -ENOMEM;

    /* 分配 crypto 操作 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 设置加密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置加密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存异步上下文信息到op的用户数据中 */
    op->opaque_data = (void *)skb;  /* 保存原始skb指针 */

    /* 异步提交：只入队，不等待完成 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue async crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 更新统计信息 */
    update_crypto_stats_submit();

    /* 异步提交成功，返回EINPROGRESS表示处理中 */
    return -EINPROGRESS;
}

/* 同步解密函数 - 立即完成处理 */
int xfrm_cryptodev_decrypt_sync(struct xfrm_state *x, struct sk_buff *skb)
{
    /* 注意：xfrm_state 结构体中没有 crypto_session 成员，需要从上下文中获取 */
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;
    struct xfrm_cryptodev_metadata meta;

    CRYPTO_DEBUG("Decrypting packet with cryptodev: spi=%u\n", x->id.spi);

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 将 skb 转换为 mbuf */
    m = skb_to_mbuf(skb);
    if (!m) {
        CRYPTO_ERROR("Failed to convert skb to mbuf\n");
        rte_crypto_op_free(op);
        return -ENOMEM;
    }

    /* 设置解密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置解密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存元数据 */
    meta.x = x;
    meta.dir = XFRM_POLICY_IN;
    meta.orig_data = skb->data;
    meta.orig_len = skb->len;
    meta.seq = XFRM_SKB_CB(skb)->seq.input.low;

    /* 将元数据保存到 skb 中 */
    memcpy(skb->cb, &meta, sizeof(meta));

    /* 同步处理：入队并立即等待完成 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 同步等待操作完成 */
    struct rte_crypto_op *completed_ops[1];
    int completed = 0;
    int max_retries = 1000;  /* 最大重试次数 */

    while (completed == 0 && max_retries-- > 0) {
        completed = rte_cryptodev_dequeue_burst(
            crypto_session->dev_id,
            crypto_session->qp_id,
            completed_ops, 1);

        if (completed == 0) {
            /* 短暂等待 */
            rte_delay_us(1);
        }
    }

    if (completed != 1) {
        CRYPTO_ERROR("Sync decrypt timeout or failed to dequeue\n");
        /* 注意：操作可能仍在队列中，但我们无法等待更长时间 */
        return -ETIMEDOUT;
    }

    /* 检查操作状态 */
    if (completed_ops[0]->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
        CRYPTO_ERROR("Crypto operation failed with status: %d\n", completed_ops[0]->status);
        rte_crypto_op_free(completed_ops[0]);
        return -EIO;
    }

    /* 恢复数据到skb */
    struct sk_buff *result_skb = mbuf_to_skb(m);
    if (!result_skb) {
        CRYPTO_ERROR("Failed to convert mbuf back to skb\n");
        rte_crypto_op_free(completed_ops[0]);
        return -ENOMEM;
    }

    /* 清理资源 */
    rte_crypto_op_free(completed_ops[0]);

    /* 更新统计信息 */
    update_crypto_stats_submit();
    update_crypto_stats_complete();

    /* 同步处理完成 */
    return 0;
}

/* 异步解密函数 - 提交后异步完成 */
int xfrm_cryptodev_decrypt_async(struct xfrm_state *x, struct sk_buff *skb)
{
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 转换 skb 到 mbuf */
    m = skb_to_mbuf(skb);
    if (!m)
        return -ENOMEM;

    /* 分配 crypto 操作 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 设置解密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置解密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存异步上下文信息到op的用户数据中 */
    op->opaque_data = (void *)skb;  /* 保存原始skb指针 */

    /* 异步提交：只入队，不等待完成 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue async crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 更新统计信息 */
    update_crypto_stats_submit();

    /* 异步提交成功，返回EINPROGRESS表示处理中 */
    return -EINPROGRESS;
}

/* 批量入队加密操作 */
void xfrm_cryptodev_enqueue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint32_t i, len, ret;

    len = qp->nb_ops;
    ret = rte_cryptodev_enqueue_burst(qp->dev_id, qp->qp_id, qp->ops_buffer, len);
    if (ret < len) {
        CRYPTO_ERROR("Cryptodev %u queue %u: enqueued %u crypto ops out of %u\n",
                   qp->dev_id, qp->qp_id, ret, len);
        /* 释放未能入队的操作 */
        for (i = ret; i < len; i++) {
            struct rte_crypto_op *op = qp->ops_buffer[i];
            rte_pktmbuf_free(op->sym->m_src);
            rte_crypto_op_free(op);
        }
    }
    qp->in_flight += ret;
    qp->nb_ops = 0;
}

/* 批量出队加密操作 */
uint16_t xfrm_cryptodev_dequeue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint16_t nb_ops;

    nb_ops = rte_cryptodev_dequeue_burst(qp->dev_id, qp->qp_id,
                                        qp->ops_buffer, CRYPTODEV_BURST_SIZE);
    if (nb_ops > 0) {
        qp->in_flight -= nb_ops;
    }

    return nb_ops;
}

/* 处理完成的加密操作 */
void xfrm_cryptodev_process_completed(struct xfrm_cryptodev_qp *qp, uint16_t nb_ops)
{
    int i;

    for (i = 0; i < nb_ops; i++) {
        struct rte_crypto_op *op = qp->ops_buffer[i];
        struct rte_mbuf *m = op->sym->m_src;
        struct sk_buff *skb;
        int err = 0;
        struct xfrm_cryptodev_metadata *meta;

        /* 检查操作状态 */
        if (op->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
            CRYPTO_ERROR("Crypto operation failed with status %d\n", op->status);
            err = -EINVAL;
        }

        /* 从 mbuf 恢复 skb */
        skb = mbuf_to_skb(m);

        /* 获取元数据 */
        meta = (struct xfrm_cryptodev_metadata *)skb->cb;

        /* 根据方向处理完成的操作 */
        if (meta->dir == XFRM_POLICY_OUT) {
            /* 加密完成 */
            xfrm_output_crypto_done(skb, err);
        } else {
            /* 解密完成 */
            xfrm_input_crypto_done(skb, err);
        }

        /* 释放加密操作 */
        rte_crypto_op_free(op);

        /* 更新统计信息 */
        if (err)
            update_crypto_stats_fail();
        else
            update_crypto_stats_complete(0); /* 暂不计算处理时间 */
    }
}
