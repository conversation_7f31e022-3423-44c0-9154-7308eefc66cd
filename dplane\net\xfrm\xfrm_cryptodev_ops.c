/*
 * xfrm_cryptodev_ops.c
 *
 * Description: DPDK cryptodev operations for xfrm framework
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"

/* 将 sk_buff 转换为 rte_mbuf */
struct rte_mbuf *skb_to_mbuf(struct sk_buff *skb)
{
    struct rte_mbuf *m;

    /* 分配 mbuf */
    m = rte_pktmbuf_alloc(xfrm_cryptodev_ctx.mbuf_pool);
    if (!m)
        return NULL;

    /* 复制数据 */
    char *data = rte_pktmbuf_append(m, skb->len);
    if (!data) {
        rte_pktmbuf_free(m);
        return NULL;
    }

    memcpy(data, skb->data, skb->len);

    /* 保存 skb 指针，用于后续恢复 */
    /* 使用 mbuf 的私有数据区域 */
    *((struct sk_buff **)rte_pktmbuf_mtod_offset(m, void *, m->priv_size)) = skb;

    return m;
}

/* 将 rte_mbuf 转换回 sk_buff */
struct sk_buff *mbuf_to_skb(struct rte_mbuf *m)
{
    struct sk_buff *skb;

    /* 获取原始 skb 指针 */
    skb = *((struct sk_buff **)rte_pktmbuf_mtod_offset(m, void *, m->priv_size));

    /* 更新 skb 数据 */
    /* 注意：这里假设 skb 有足够空间容纳处理后的数据 */
    /* 如果数据大小变化，可能需要调整 skb */
    memcpy(skb->data, rte_pktmbuf_mtod(m, void *), m->data_len);

    /* 如果数据长度变化，更新 skb 长度 */
    if (skb->len != m->data_len) {
        skb_trim(skb, m->data_len);
    }

    /* 释放 mbuf */
    rte_pktmbuf_free(m);

    return skb;
}

/* 设置加密操作参数 */
int xfrm_set_crypto_op_params(struct xfrm_state *x, struct rte_crypto_op *op, struct sk_buff *skb)
{
    struct rte_crypto_sym_op *sym_op = op->sym;
    struct ip_esp_hdr *esph;
    int alen, clen;

    /* 获取 ESP 头 */
    esph = (struct ip_esp_hdr *)skb_transport_header(skb);

    /* 设置 IV - 注意：在某些版本的 DPDK 中，cipher 结构体可能没有 iv 成员 */
    /* 使用 data 成员代替 */
    sym_op->cipher.data.offset = (uint8_t *)esph - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
    sym_op->cipher.data.length = x->enc_key_len;

    /* 设置加密数据 */
    clen = skb->len - skb_transport_offset(skb) - sizeof(struct ip_esp_hdr) - x->ealg->ivsize;
    if (x->props.mode == XFRM_MODE_TRANSPORT) {
        /* 加密 */
        sym_op->cipher.data.offset = (uint8_t *)esph->enc_data + x->ealg->ivsize - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
        sym_op->cipher.data.length = clen;
    } else {
        /* 解密 */
        sym_op->cipher.data.offset = (uint8_t *)esph->enc_data + x->ealg->ivsize - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
        sym_op->cipher.data.length = clen - x->aalg->authsize;
    }

    /* 设置认证数据 */
    alen = skb->len - skb_transport_offset(skb) - x->aalg->authsize;
    sym_op->auth.data.offset = (uint8_t *)esph - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *);
    sym_op->auth.data.length = alen;

    /* 设置认证摘要 - 注意：在某些版本的 DPDK 中，digest 结构体可能没有 length 成员 */
    sym_op->auth.digest.data = (uint8_t *)esph + alen;
    sym_op->auth.digest.phys_addr = rte_pktmbuf_mtophys_offset(sym_op->m_src,
                                   (uint8_t *)esph + alen - rte_pktmbuf_mtod(sym_op->m_src, uint8_t *));

    return 0;
}

/* 使用 cryptodev 进行加密 */
int xfrm_cryptodev_encrypt(struct xfrm_state *x, struct sk_buff *skb)
{
    /* 注意：xfrm_state 结构体中没有 crypto_session 成员，需要从上下文中获取 */
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;
    struct xfrm_cryptodev_metadata meta;

    CRYPTO_DEBUG("Encrypting packet with cryptodev: spi=%u\n", x->id.spi);

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 将 skb 转换为 mbuf */
    m = skb_to_mbuf(skb);
    if (!m) {
        CRYPTO_ERROR("Failed to convert skb to mbuf\n");
        rte_crypto_op_free(op);
        return -ENOMEM;
    }

    /* 设置加密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置加密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存元数据 */
    meta.x = x;
    meta.dir = XFRM_POLICY_OUT;
    meta.orig_data = skb->data;
    meta.orig_len = skb->len;
    meta.seq = XFRM_SKB_CB(skb)->seq.output.low;

    /* 将元数据保存到 skb 中 */
    memcpy(skb->cb, &meta, sizeof(meta));

    /* 入队加密操作 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 更新统计信息 */
    update_crypto_stats_submit();

    /* 操作已入队，将在后续处理中完成 */
    return 0;
}

/* 使用 cryptodev 进行解密 */
int xfrm_cryptodev_decrypt(struct xfrm_state *x, struct sk_buff *skb)
{
    /* 注意：xfrm_state 结构体中没有 crypto_session 成员，需要从上下文中获取 */
    struct xfrm_cryptodev_session *crypto_session;
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;
    struct xfrm_cryptodev_metadata meta;

    CRYPTO_DEBUG("Decrypting packet with cryptodev: spi=%u\n", x->id.spi);

    /* 从上下文中获取会话 */
    crypto_session = xfrm_get_cryptodev_session(x);
    if (!crypto_session || !crypto_session->session)
        return -EINVAL;

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(xfrm_cryptodev_ctx.op_pool, RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation\n");
        return -ENOMEM;
    }

    /* 将 skb 转换为 mbuf */
    m = skb_to_mbuf(skb);
    if (!m) {
        CRYPTO_ERROR("Failed to convert skb to mbuf\n");
        rte_crypto_op_free(op);
        return -ENOMEM;
    }

    /* 设置解密操作 */
    op->sym->m_src = m;
    op->sym->m_dst = NULL; /* 就地操作 */

    /* 附加会话 */
    rte_crypto_op_attach_sym_session(op, crypto_session->session);

    /* 设置解密参数 */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 保存元数据 */
    meta.x = x;
    meta.dir = XFRM_POLICY_IN;
    meta.orig_data = skb->data;
    meta.orig_len = skb->len;
    meta.seq = XFRM_SKB_CB(skb)->seq.input.low;

    /* 将元数据保存到 skb 中 */
    memcpy(skb->cb, &meta, sizeof(meta));

    /* 入队解密操作 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue crypto operation: %d\n", ret);
        rte_pktmbuf_free(m);
        rte_crypto_op_free(op);
        return -EBUSY;
    }

    /* 更新统计信息 */
    update_crypto_stats_submit();

    /* 操作已入队，将在后续处理中完成 */
    return 0;
}

/* 批量入队加密操作 */
void xfrm_cryptodev_enqueue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint32_t i, len, ret;

    len = qp->nb_ops;
    ret = rte_cryptodev_enqueue_burst(qp->dev_id, qp->qp_id, qp->ops_buffer, len);
    if (ret < len) {
        CRYPTO_ERROR("Cryptodev %u queue %u: enqueued %u crypto ops out of %u\n",
                   qp->dev_id, qp->qp_id, ret, len);
        /* 释放未能入队的操作 */
        for (i = ret; i < len; i++) {
            struct rte_crypto_op *op = qp->ops_buffer[i];
            rte_pktmbuf_free(op->sym->m_src);
            rte_crypto_op_free(op);
        }
    }
    qp->in_flight += ret;
    qp->nb_ops = 0;
}

/* 批量出队加密操作 */
uint16_t xfrm_cryptodev_dequeue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint16_t nb_ops;

    nb_ops = rte_cryptodev_dequeue_burst(qp->dev_id, qp->qp_id,
                                        qp->ops_buffer, CRYPTODEV_BURST_SIZE);
    if (nb_ops > 0) {
        qp->in_flight -= nb_ops;
    }

    return nb_ops;
}

/* 处理完成的加密操作 */
void xfrm_cryptodev_process_completed(struct xfrm_cryptodev_qp *qp, uint16_t nb_ops)
{
    int i;

    for (i = 0; i < nb_ops; i++) {
        struct rte_crypto_op *op = qp->ops_buffer[i];
        struct rte_mbuf *m = op->sym->m_src;
        struct sk_buff *skb;
        int err = 0;
        struct xfrm_cryptodev_metadata *meta;

        /* 检查操作状态 */
        if (op->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
            CRYPTO_ERROR("Crypto operation failed with status %d\n", op->status);
            err = -EINVAL;
        }

        /* 从 mbuf 恢复 skb */
        skb = mbuf_to_skb(m);

        /* 获取元数据 */
        meta = (struct xfrm_cryptodev_metadata *)skb->cb;

        /* 根据方向处理完成的操作 */
        if (meta->dir == XFRM_POLICY_OUT) {
            /* 加密完成 */
            xfrm_output_crypto_done(skb, err);
        } else {
            /* 解密完成 */
            xfrm_input_crypto_done(skb, err);
        }

        /* 释放加密操作 */
        rte_crypto_op_free(op);

        /* 更新统计信息 */
        if (err)
            update_crypto_stats_fail();
        else
            update_crypto_stats_complete(0); /* 暂不计算处理时间 */
    }
}
