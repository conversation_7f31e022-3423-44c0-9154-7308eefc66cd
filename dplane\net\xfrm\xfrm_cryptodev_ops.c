/*
 * xfrm_cryptodev_ops.c
 *
 * Description: DPDK cryptodev operations for xfrm framework
 */

#include <net/xfrm.h>
#include <linux/slab.h>
#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/interrupt.h>
#include <linux/wait.h>
#include <linux/sched.h>

#include <rte_cryptodev.h>
#include <rte_crypto_sym.h>
#include <rte_security.h>
#include <rte_hash.h>
#include <rte_jhash.h>
#include <rte_mempool.h>
#include <rte_mbuf.h>

#include "xfrm_cryptodev.h"
#include "xfrm_cryptodev_zerocopy.h"

/* 将 sk_buff 转换为 rte_mbuf */
struct rte_mbuf *skb_to_mbuf(struct sk_buff *skb)
{
    struct rte_mbuf *m;

    /* 分配 mbuf */
    m = rte_pktmbuf_alloc(xfrm_cryptodev_ctx.mbuf_pool);
    if (!m)
        return NULL;

    /* 复制数据 */
    char *data = rte_pktmbuf_append(m, skb->len);
    if (!data) {
        rte_pktmbuf_free(m);
        return NULL;
    }

    memcpy(data, skb->data, skb->len);

    /* 保存 skb 指针，用于后续恢复 */
    /* 使用 mbuf 的私有数据区域 */
    *((struct sk_buff **)rte_pktmbuf_mtod_offset(m, void *, m->priv_size)) = skb;

    return m;
}

/* 将 rte_mbuf 转换回 sk_buff */
struct sk_buff *mbuf_to_skb(struct rte_mbuf *m)
{
    struct sk_buff *skb;

    /* 获取原始 skb 指针 */
    skb = *((struct sk_buff **)rte_pktmbuf_mtod_offset(m, void *, m->priv_size));

    /* 更新 skb 数据 */
    /* 注意：这里假设 skb 有足够空间容纳处理后的数据 */
    /* 如果数据大小变化，可能需要调整 skb */
    memcpy(skb->data, rte_pktmbuf_mtod(m, void *), m->data_len);

    /* 如果数据长度变化，更新 skb 长度 */
    if (skb->len != m->data_len) {
        skb_trim(skb, m->data_len);
    }

    /* 释放 mbuf */
    rte_pktmbuf_free(m);

    return skb;
}

/* 设置加密操作参数（使用零拷贝实现） */
int xfrm_set_crypto_op_params(struct xfrm_state *x, struct rte_crypto_op *op, struct sk_buff *skb)
{
    struct rte_crypto_sym_op *sym_op = op->sym;
    struct rte_mbuf *m;
    uint32_t data_offset, data_length, auth_offset, auth_length;
    int ret;

    /* 获取关联的mbuf（零拷贝） */
    m = xfrm_prepare_mbuf_for_crypto(skb);
    if (!m) {
        CRYPTO_ERROR("Failed to prepare mbuf for crypto operation");
        return -EINVAL;
    }

    /* 设置会话 - 使用DPDK 23推荐的API */
    void *session = x->context;
    if (!session) {
        CRYPTO_ERROR("No cryptodev session available");
        return -EINVAL;
    }

    ret = rte_crypto_op_attach_sym_session(op, session);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to attach session to crypto operation");
        return -EINVAL;
    }

    /* 设置源和目标 mbuf */
    sym_op->m_src = m;
    sym_op->m_dst = NULL;  /* 就地操作 */

    /* 计算ESP偏移和长度 */
    ret = xfrm_calculate_esp_offsets(m, x, &data_offset, &data_length,
                                    &auth_offset, &auth_length);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to calculate ESP offsets: %d", ret);
        return ret;
    }

    /* 设置加密参数 */
    sym_op->cipher.data.offset = data_offset;
    sym_op->cipher.data.length = data_length;

    /* 设置IV */
    struct ip_esp_hdr *esph = xfrm_get_esp_header(m, x->props.family);
    if (esph && x->iv_len > 0) {
        sym_op->cipher.iv.data = (uint8_t *)(esph + 1);
        sym_op->cipher.iv.length = x->iv_len;
        sym_op->cipher.iv.phys_addr = rte_mbuf_data_iova(m) +
            ((char *)sym_op->cipher.iv.data - (char *)rte_pktmbuf_mtod(m, void *));
    }

    /* 设置认证参数 */
    sym_op->auth.data.offset = auth_offset;
    sym_op->auth.data.length = auth_length;

    /* 设置认证摘要 */
    if (x->auth_trunc_len > 0) {
        sym_op->auth.digest.data = rte_pktmbuf_mtod_offset(m, uint8_t *,
                                                          m->data_len - x->auth_trunc_len);
        sym_op->auth.digest.length = x->auth_trunc_len;
        sym_op->auth.digest.phys_addr = rte_mbuf_data_iova(m) +
            (m->data_len - x->auth_trunc_len);
    }

    CRYPTO_DEBUG("Crypto op params set: cipher_offset=%u, cipher_len=%u, auth_offset=%u, auth_len=%u",
                data_offset, data_length, auth_offset, auth_length);

    return 0;
}

/* 使用 cryptodev 进行加密（零拷贝实现） */
int xfrm_cryptodev_encrypt(struct xfrm_state *x, struct sk_buff *skb)
{
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;

    CRYPTO_DEBUG("Encrypting packet with cryptodev: spi=%u", x->id.spi);

    /* 检查数据包是否适合cryptodev处理 */
    if (!xfrm_packet_suitable_for_cryptodev(skb)) {
        CRYPTO_DEBUG("Packet not suitable for cryptodev, falling back to software");
        return -ENOTSUP;  /* 回退到软件加密 */
    }

    /* 检查会话是否可用 */
    if (!x->context) {
        CRYPTO_ERROR("No cryptodev session available for SA");
        return -EINVAL;
    }

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(unified_get_op_pool(), RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation");
        return -ENOMEM;
    }

    /* 设置加密操作参数（零拷贝） */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 获取mbuf（零拷贝） */
    m = op->sym->m_src;

    /* 保存异步上下文 */
    ret = xfrm_save_async_context(m, x, skb, XFRM_POLICY_OUT);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to save async context: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 提交加密操作到cryptodev */
    ret = xfrm_cryptodev_submit_op(op, x);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to submit crypto operation: %d", ret);
        xfrm_cleanup_async_context(m);
        rte_crypto_op_free(op);
        return ret;
    }

    CRYPTO_DEBUG("Crypto operation submitted successfully");

    /* 返回 -EINPROGRESS 表示异步处理中 */
    return -EINPROGRESS;
}

/* 提交crypto操作到设备 */
int xfrm_cryptodev_submit_op(struct rte_crypto_op *op, struct xfrm_state *x)
{
    struct xfrm_cryptodev_session *crypto_session;
    int ret;

    /* 从上下文中获取会话 */
    crypto_session = (struct xfrm_cryptodev_session *)x->context;
    if (!crypto_session || !crypto_session->session) {
        CRYPTO_ERROR("No valid cryptodev session for SA");
        return -EINVAL;
    }

    /* 入队操作 */
    ret = rte_cryptodev_enqueue_burst(
        crypto_session->dev_id,
        crypto_session->qp_id,
        &op, 1);

    if (ret != 1) {
        CRYPTO_ERROR("Failed to enqueue crypto operation: %d", ret);
        return -EBUSY;
    }

    /* 更新统计信息 */
    update_crypto_stats_submit();

    return 0;
}

/* 使用 cryptodev 进行解密（零拷贝实现） */
int xfrm_cryptodev_decrypt(struct xfrm_state *x, struct sk_buff *skb)
{
    struct rte_crypto_op *op;
    struct rte_mbuf *m;
    int ret;

    CRYPTO_DEBUG("Decrypting packet with cryptodev: spi=%u", x->id.spi);

    /* 检查数据包是否适合cryptodev处理 */
    if (!xfrm_packet_suitable_for_cryptodev(skb)) {
        CRYPTO_DEBUG("Packet not suitable for cryptodev, falling back to software");
        return -ENOTSUP;  /* 回退到软件解密 */
    }

    /* 检查会话是否可用 */
    if (!x->context) {
        CRYPTO_ERROR("No cryptodev session available for SA");
        return -EINVAL;
    }

    /* 获取操作结构 */
    op = rte_crypto_op_alloc(unified_get_op_pool(), RTE_CRYPTO_OP_TYPE_SYMMETRIC);
    if (!op) {
        CRYPTO_ERROR("Failed to allocate crypto operation");
        return -ENOMEM;
    }

    /* 设置解密操作参数（零拷贝） */
    ret = xfrm_set_crypto_op_params(x, op, skb);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to set crypto operation parameters: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 获取mbuf（零拷贝） */
    m = op->sym->m_src;

    /* 保存异步上下文 */
    ret = xfrm_save_async_context(m, x, skb, XFRM_POLICY_IN);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to save async context: %d", ret);
        rte_crypto_op_free(op);
        return ret;
    }

    /* 提交解密操作到cryptodev */
    ret = xfrm_cryptodev_submit_op(op, x);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to submit crypto operation: %d", ret);
        xfrm_cleanup_async_context(m);
        rte_crypto_op_free(op);
        return ret;
    }

    CRYPTO_DEBUG("Crypto operation submitted successfully");

    /* 返回 -EINPROGRESS 表示异步处理中 */
    return -EINPROGRESS;
}

/* 批量入队加密操作 */
void xfrm_cryptodev_enqueue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint32_t i, len, ret;

    len = qp->nb_ops;
    ret = rte_cryptodev_enqueue_burst(qp->dev_id, qp->qp_id, qp->ops_buffer, len);
    if (ret < len) {
        CRYPTO_ERROR("Cryptodev %u queue %u: enqueued %u crypto ops out of %u\n",
                   qp->dev_id, qp->qp_id, ret, len);
        /* 释放未能入队的操作 */
        for (i = ret; i < len; i++) {
            struct rte_crypto_op *op = qp->ops_buffer[i];
            rte_pktmbuf_free(op->sym->m_src);
            rte_crypto_op_free(op);
        }
    }
    qp->in_flight += ret;
    qp->nb_ops = 0;
}

/* 批量出队加密操作 */
uint16_t xfrm_cryptodev_dequeue_burst(struct xfrm_cryptodev_qp *qp)
{
    uint16_t nb_ops;

    nb_ops = rte_cryptodev_dequeue_burst(qp->dev_id, qp->qp_id,
                                        qp->ops_buffer, CRYPTODEV_BURST_SIZE);
    if (nb_ops > 0) {
        qp->in_flight -= nb_ops;
    }

    return nb_ops;
}

/* 处理完成的加密操作 */
void xfrm_cryptodev_process_completed(struct xfrm_cryptodev_qp *qp, uint16_t nb_ops)
{
    int i;

    for (i = 0; i < nb_ops; i++) {
        struct rte_crypto_op *op = qp->ops_buffer[i];
        struct rte_mbuf *m = op->sym->m_src;
        struct sk_buff *skb;
        int err = 0;
        struct xfrm_cryptodev_metadata *meta;

        /* 检查操作状态 */
        if (op->status != RTE_CRYPTO_OP_STATUS_SUCCESS) {
            CRYPTO_ERROR("Crypto operation failed with status %d\n", op->status);
            err = -EINVAL;
        }

        /* 从 mbuf 恢复 skb */
        skb = mbuf_to_skb(m);

        /* 获取元数据 */
        meta = (struct xfrm_cryptodev_metadata *)skb->cb;

        /* 根据方向处理完成的操作 */
        if (meta->dir == XFRM_POLICY_OUT) {
            /* 加密完成 */
            xfrm_output_crypto_done(skb, err);
        } else {
            /* 解密完成 */
            xfrm_input_crypto_done(skb, err);
        }

        /* 释放加密操作 */
        rte_crypto_op_free(op);

        /* 更新统计信息 */
        if (err)
            update_crypto_stats_fail();
        else
            update_crypto_stats_complete(0); /* 暂不计算处理时间 */
    }
}
