DPDK_24 {
	global:

	__rte_pktmbuf_linearize;
	__rte_pktmbuf_read;
	rte_get_ptype_inner_l2_name;
	rte_get_ptype_inner_l3_name;
	rte_get_ptype_inner_l4_name;
	rte_get_ptype_l2_name;
	rte_get_ptype_l3_name;
	rte_get_ptype_l4_name;
	rte_get_ptype_name;
	rte_get_ptype_tunnel_name;
	rte_get_rx_ol_flag_list;
	rte_get_rx_ol_flag_name;
	rte_get_tx_ol_flag_list;
	rte_get_tx_ol_flag_name;
	rte_mbuf_best_mempool_ops;
	rte_mbuf_check;
	rte_mbuf_dyn_dump;
	rte_mbuf_dyn_rx_timestamp_register;
	rte_mbuf_dyn_tx_timestamp_register;
	rte_mbuf_dynfield_lookup;
	rte_mbuf_dynfield_register;
	rte_mbuf_dynfield_register_offset;
	rte_mbuf_dynflag_lookup;
	rte_mbuf_dynflag_register;
	rte_mbuf_dynflag_register_bitnum;
	rte_mbuf_platform_mempool_ops;
	rte_mbuf_sanity_check;
	rte_mbuf_set_platform_mempool_ops;
	rte_mbuf_set_user_mempool_ops;
	rte_mbuf_user_mempool_ops;
	rte_pktmbuf_clone;
	rte_pktmbuf_copy;
	rte_pktmbuf_dump;
	rte_pktmbuf_free_bulk;
	rte_pktmbuf_init;
	rte_pktmbuf_pool_create;
	rte_pktmbuf_pool_create_by_ops;
	rte_pktmbuf_pool_create_extbuf;
	rte_pktmbuf_pool_init;

	local: *;
};
