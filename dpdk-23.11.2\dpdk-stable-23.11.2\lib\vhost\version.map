DPDK_24 {
	global:

	rte_vdpa_find_device_by_name;
	rte_vdpa_get_features;
	rte_vdpa_get_protocol_features;
	rte_vdpa_get_queue_num;
	rte_vdpa_get_rte_device;
	rte_vdpa_get_stats;
	rte_vdpa_get_stats_names;
	rte_vdpa_reset_stats;
	rte_vhost_avail_entries;
	rte_vhost_backend_config_change;
	rte_vhost_clr_inflight_desc_packed;
	rte_vhost_clr_inflight_desc_split;
	rte_vhost_crypto_create;
	rte_vhost_crypto_driver_start;
	rte_vhost_crypto_fetch_requests;
	rte_vhost_crypto_finalize_requests;
	rte_vhost_crypto_free;
	rte_vhost_crypto_set_zero_copy;
	rte_vhost_dequeue_burst;
	rte_vhost_driver_attach_vdpa_device;
	rte_vhost_driver_callback_register;
	rte_vhost_driver_detach_vdpa_device;
	rte_vhost_driver_disable_features;
	rte_vhost_driver_enable_features;
	rte_vhost_driver_get_features;
	rte_vhost_driver_get_protocol_features;
	rte_vhost_driver_get_queue_num;
	rte_vhost_driver_get_vdpa_dev_type;
	rte_vhost_driver_get_vdpa_device;
	rte_vhost_driver_register;
	rte_vhost_driver_set_features;
	rte_vhost_driver_set_protocol_features;
	rte_vhost_driver_start;
	rte_vhost_driver_unregister;
	rte_vhost_enable_guest_notification;
	rte_vhost_enqueue_burst;
	rte_vhost_extern_callback_register;
	rte_vhost_get_ifname;
	rte_vhost_get_log_base;
	rte_vhost_get_mem_table;
	rte_vhost_get_monitor_addr;
	rte_vhost_get_mtu;
	rte_vhost_get_negotiated_features;
	rte_vhost_get_negotiated_protocol_features;
	rte_vhost_get_numa_node;
	rte_vhost_get_vdpa_device;
	rte_vhost_get_vhost_ring_inflight;
	rte_vhost_get_vhost_vring;
	rte_vhost_get_vring_base;
	rte_vhost_get_vring_base_from_inflight;
	rte_vhost_get_vring_num;
	rte_vhost_log_used_vring;
	rte_vhost_log_write;
	rte_vhost_rx_queue_count;
	rte_vhost_set_inflight_desc_packed;
	rte_vhost_set_inflight_desc_split;
	rte_vhost_set_last_inflight_io_packed;
	rte_vhost_set_last_inflight_io_split;
	rte_vhost_set_vring_base;
	rte_vhost_va_from_guest_pa;
	rte_vhost_vring_call;
	rte_vhost_vring_call_nonblock;
	rte_vhost_vring_stats_get;
	rte_vhost_vring_stats_get_names;
	rte_vhost_vring_stats_reset;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 20.08
	rte_vhost_async_channel_register;
	rte_vhost_async_channel_unregister;
	rte_vhost_submit_enqueue_burst;
	rte_vhost_poll_enqueue_completed;

	# added in 21.08
	rte_vhost_async_get_inflight;
	rte_vhost_async_channel_register_thread_unsafe;
	rte_vhost_async_channel_unregister_thread_unsafe;
	rte_vhost_clear_queue_thread_unsafe;

	# added in 22.03
	rte_vhost_async_dma_configure;

	# added in 22.07
	rte_vhost_async_get_inflight_thread_unsafe;
	rte_vhost_async_try_dequeue_burst;
	rte_vhost_clear_queue;

	# added in 22.11
	rte_vhost_async_dma_unconfigure;

	# added in 23.07
	rte_vhost_driver_set_max_queue_num;
	rte_vhost_notify_guest;
};

INTERNAL {
	global:

	rte_vdpa_register_device;
	rte_vdpa_relay_vring_used;
	rte_vdpa_unregister_device;
	rte_vhost_host_notifier_ctrl;
};
