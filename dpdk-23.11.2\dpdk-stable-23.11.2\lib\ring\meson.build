# SPDX-License-Identifier: BSD-3-Clause
# Copyright(c) 2017 Intel Corporation

sources = files('rte_ring.c')
headers = files('rte_ring.h')
# most sub-headers are not for direct inclusion
indirect_headers += files (
        'rte_ring_core.h',
        'rte_ring_elem.h',
        'rte_ring_elem_pvt.h',
        'rte_ring_c11_pvt.h',
        'rte_ring_generic_pvt.h',
        'rte_ring_hts.h',
        'rte_ring_hts_elem_pvt.h',
        'rte_ring_peek.h',
        'rte_ring_peek_elem_pvt.h',
        'rte_ring_peek_zc.h',
        'rte_ring_rts.h',
        'rte_ring_rts_elem_pvt.h',
)
