/*
 * dpdk_cryptodev_unified.c
 *
 * Description: 统一的DPDK cryptodev初始化模块
 * 整合了原来分散在多个文件中的初始化逻辑
 */

#include <rte_cryptodev.h>
#include <rte_crypto.h>
#include <rte_mempool.h>
#include <rte_vdev.h>
#include <rte_errno.h>
#include <dlfcn.h>
#include <errno.h>
#include <string.h>

#include "dpdk_cryptodev_unified.h"
#include "../net/xfrm/xfrm_cryptodev.h"
#include "../net/xfrm/xfrm_cryptodev_config.h"

/* 全局变量 */
static struct rte_mempool *unified_session_pool = NULL;
static struct rte_mempool *unified_op_pool = NULL;
static int unified_cryptodev_initialized = 0;

/* 统一的cryptodev配置参数 */
#define UNIFIED_CRYPTO_MAX_QUEUE_PAIRS 8
#define UNIFIED_CRYPTO_MAX_SESSIONS 2048
#define UNIFIED_CRYPTO_SESSION_POOL_CACHE_SIZE 32
#define UNIFIED_CRYPTO_MAX_OPS 2048

/* 创建OpenSSL PMD设备 */
static int create_openssl_pmd_device(void)
{
    int ret;
    uint8_t nb_devs_before, nb_devs_after;
    
    CRYPTO_INFO("Creating OpenSSL PMD device");
    
    /* 记录创建前的设备数量 */
    nb_devs_before = rte_cryptodev_count();
    CRYPTO_DEBUG("Number of crypto devices before creation: %u", nb_devs_before);
    
    /* 检查 rte_vdev_init 函数是否可用 */
    void *vdev_func = dlsym(RTLD_DEFAULT, "rte_vdev_init");
    if (!vdev_func) {
        CRYPTO_ERROR("rte_vdev_init function is not available");
        CRYPTO_ERROR("DPDK was compiled without vdev support");
        
        /* 尝试查找现有的硬件cryptodev设备 */
        if (nb_devs_before > 0) {
            CRYPTO_INFO("Found %u existing cryptodev devices, will use the first one", nb_devs_before);
            return 0;  /* 使用第一个现有设备 */
        } else {
            CRYPTO_ERROR("No existing cryptodev devices found and cannot create virtual devices");
            return -ENOTSUP;
        }
    }
    
    /* 创建OpenSSL PMD设备 */
    ret = rte_vdev_init("crypto_openssl", "socket_id=0");
    if (ret < 0) {
        CRYPTO_ERROR("Failed to create OpenSSL PMD device: %d", ret);
        CRYPTO_ERROR("DPDK error: %s", rte_strerror(abs(ret)));
        return ret;
    }
    
    /* 验证设备是否创建成功 */
    nb_devs_after = rte_cryptodev_count();
    if (nb_devs_after <= nb_devs_before) {
        CRYPTO_ERROR("No new crypto device was created");
        return -ENODEV;
    }
    
    CRYPTO_INFO("Successfully created OpenSSL PMD device");
    CRYPTO_INFO("Number of crypto devices after creation: %u", nb_devs_after);
    
    return 0;
}

/* 创建内存池 */
static int create_memory_pools(void)
{
    CRYPTO_INFO("Creating cryptodev memory pools");
    
    /* 创建会话池 - 使用DPDK 23 API */
    unified_session_pool = rte_cryptodev_sym_session_pool_create(
        "unified_crypto_sess_pool",
        UNIFIED_CRYPTO_MAX_SESSIONS,
        0,  /* elt_size - 由DPDK自动计算 */
        UNIFIED_CRYPTO_SESSION_POOL_CACHE_SIZE,
        0,  /* priv_size - 私有数据大小 */
        rte_socket_id());
    
    if (!unified_session_pool) {
        CRYPTO_ERROR("Failed to create session pool");
        return -ENOMEM;
    }
    
    /* 创建操作池 */
    unified_op_pool = rte_crypto_op_pool_create(
        "unified_crypto_op_pool",
        RTE_CRYPTO_OP_TYPE_SYMMETRIC,
        UNIFIED_CRYPTO_MAX_OPS,
        UNIFIED_CRYPTO_SESSION_POOL_CACHE_SIZE,
        0,  /* 私有数据大小 */
        rte_socket_id());
    
    if (!unified_op_pool) {
        CRYPTO_ERROR("Failed to create operation pool");
        rte_mempool_free(unified_session_pool);
        unified_session_pool = NULL;
        return -ENOMEM;
    }
    
    CRYPTO_INFO("Memory pools created successfully");
    return 0;
}

/* 配置cryptodev设备 */
static int configure_cryptodev_devices(void)
{
    uint8_t dev_id;
    uint8_t nb_devs = rte_cryptodev_count();
    int ret;
    
    CRYPTO_INFO("Configuring %u cryptodev devices", nb_devs);
    
    for (dev_id = 0; dev_id < nb_devs; dev_id++) {
        struct rte_cryptodev_info dev_info;
        struct rte_cryptodev_config dev_config;
        struct rte_cryptodev_qp_conf qp_conf;
        uint16_t qp_id;
        
        /* 获取设备信息 */
        rte_cryptodev_info_get(dev_id, &dev_info);
        CRYPTO_INFO("Configuring device %u: %s", dev_id, 
                   dev_info.driver_name ? dev_info.driver_name : "unknown");
        
        /* 配置设备 */
        memset(&dev_config, 0, sizeof(dev_config));
        dev_config.socket_id = rte_socket_id();
        dev_config.nb_queue_pairs = RTE_MIN(dev_info.max_nb_queue_pairs, UNIFIED_CRYPTO_MAX_QUEUE_PAIRS);
        if (dev_config.nb_queue_pairs == 0) {
            dev_config.nb_queue_pairs = 1;  /* 至少需要一个队列对 */
        }
        
        /* 检查设备特性 */
        if (!(dev_info.feature_flags & RTE_CRYPTODEV_FF_SYMMETRIC_CRYPTO)) {
            CRYPTO_WARNING("Device %u does not support symmetric crypto", dev_id);
            continue;
        }

        /* 记录设备支持的高级特性 */
        if (dev_info.feature_flags & RTE_CRYPTODEV_FF_HW_ACCELERATED) {
            CRYPTO_INFO("Device %u supports hardware acceleration", dev_id);
        }
        if (dev_info.feature_flags & RTE_CRYPTODEV_FF_SYM_RAW_DP) {
            CRYPTO_INFO("Device %u supports raw data path API", dev_id);
        }
        if (dev_info.feature_flags & RTE_CRYPTODEV_FF_IN_PLACE_SGL) {
            CRYPTO_INFO("Device %u supports in-place scatter-gather", dev_id);
        }
        if (dev_info.feature_flags & RTE_CRYPTODEV_FF_CPU_AVX512) {
            CRYPTO_INFO("Device %u supports AVX512 instructions", dev_id);
        }
        
        ret = rte_cryptodev_configure(dev_id, &dev_config);
        if (ret < 0) {
            CRYPTO_ERROR("Failed to configure cryptodev %u: %d", dev_id, ret);
            continue;
        }
        
        /* 配置队列对 */
        memset(&qp_conf, 0, sizeof(qp_conf));
        qp_conf.nb_descriptors = UNIFIED_CRYPTO_MAX_OPS;
        qp_conf.mp_session = unified_session_pool;
        
        for (qp_id = 0; qp_id < dev_config.nb_queue_pairs; qp_id++) {
            ret = rte_cryptodev_queue_pair_setup(dev_id, qp_id, &qp_conf, rte_socket_id());
            if (ret < 0) {
                CRYPTO_ERROR("Failed to setup queue pair %u on device %u: %d", qp_id, dev_id, ret);
                break;
            }
        }
        
        if (ret < 0) {
            continue;  /* 跳过这个设备 */
        }
        
        /* 启动设备 */
        ret = rte_cryptodev_start(dev_id);
        if (ret < 0) {
            CRYPTO_ERROR("Failed to start cryptodev %u: %d", dev_id, ret);
            continue;
        }
        
        CRYPTO_INFO("Device %u configured and started successfully", dev_id);
    }
    
    return 0;
}

/* 统一的cryptodev初始化函数 */
int unified_cryptodev_init(void)
{
    int ret;
    
    if (unified_cryptodev_initialized) {
        CRYPTO_INFO("Cryptodev already initialized");
        return 0;
    }
    
    CRYPTO_INFO("Starting unified cryptodev initialization");
    
    /* 检查DPDK EAL是否已初始化 */
    if (!rte_eal_has_hugepages()) {
        CRYPTO_ERROR("DPDK EAL not properly initialized");
        return -EINVAL;
    }
    
    /* 步骤1: 创建cryptodev设备 */
    ret = create_openssl_pmd_device();
    if (ret < 0) {
        if (ret == -ENOTSUP) {
            CRYPTO_WARNING("Virtual device creation not supported, cryptodev disabled");
            cryptodev_enabled = 0;
            return 0;  /* 不返回错误，但禁用功能 */
        }
        CRYPTO_ERROR("Failed to create cryptodev devices: %d", ret);
        return ret;
    }
    
    /* 步骤2: 创建内存池 */
    ret = create_memory_pools();
    if (ret < 0) {
        CRYPTO_ERROR("Failed to create memory pools: %d", ret);
        return ret;
    }
    
    /* 步骤3: 配置设备 */
    ret = configure_cryptodev_devices();
    if (ret < 0) {
        CRYPTO_ERROR("Failed to configure cryptodev devices: %d", ret);
        goto cleanup;
    }
    
    /* 步骤4: 初始化xfrm cryptodev上下文 */
    struct xfrm_cryptodev_ctx ctx;
    memset(&ctx, 0, sizeof(ctx));
    ctx.session_pool = unified_session_pool;
    ctx.op_pool = unified_op_pool;
    ctx.nb_devs = rte_cryptodev_count();
    
    /* 设置设备ID和状态 */
    for (int i = 0; i < ctx.nb_devs && i < CRYPTODEV_MAX_DEVS; i++) {
        ctx.dev_ids[i] = i;
        ctx.dev_active[i] = 1;
    }
    
    ret = xfrm_cryptodev_init_ctx(&ctx);
    if (ret < 0) {
        CRYPTO_ERROR("Failed to initialize xfrm cryptodev context: %d", ret);
        goto cleanup;
    }
    
    unified_cryptodev_initialized = 1;
    CRYPTO_INFO("Unified cryptodev initialization completed successfully");
    
    return 0;
    
cleanup:
    if (unified_op_pool) {
        rte_mempool_free(unified_op_pool);
        unified_op_pool = NULL;
    }
    if (unified_session_pool) {
        rte_mempool_free(unified_session_pool);
        unified_session_pool = NULL;
    }
    return ret;
}

/* 统一的cryptodev清理函数 */
void unified_cryptodev_uninit(void)
{
    if (!unified_cryptodev_initialized) {
        return;
    }
    
    CRYPTO_INFO("Uninitializing unified cryptodev");
    
    /* 清理xfrm cryptodev上下文 */
    xfrm_cryptodev_uninit();
    
    /* 释放内存池 */
    if (unified_op_pool) {
        rte_mempool_free(unified_op_pool);
        unified_op_pool = NULL;
    }
    
    if (unified_session_pool) {
        rte_mempool_free(unified_session_pool);
        unified_session_pool = NULL;
    }
    
    unified_cryptodev_initialized = 0;
    CRYPTO_INFO("Unified cryptodev uninitialized");
}

/* 获取内存池指针 */
struct rte_mempool *unified_get_session_pool(void)
{
    return unified_session_pool;
}

struct rte_mempool *unified_get_op_pool(void)
{
    return unified_op_pool;
}

/* 检查初始化状态 */
int unified_cryptodev_is_initialized(void)
{
    return unified_cryptodev_initialized;
}
