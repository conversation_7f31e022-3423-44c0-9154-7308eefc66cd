This file will record any exceptions in DPDK Project with respect to DPDK
IP License policy as defined in DPDK Charter available at:

	https://www.dpdk.org/charter/

Note that following licenses are not exceptions:-
	- BSD-3-Clause
	- BSD-3-Clause OR GPL-2.0
	- BSD-3-Clause OR LGPL-2.1
	- GPL-2.0  (*Only for kernel code*)

---------------------------------------------------------------------------------------------------
SPDX Identifier     TB Approval Date  GB Approval Date  File name
---------------------------------------------------------------------------------------------------
1.MIT               10/23/2019        02/10/2020        lib/eal/windows/include/dirent.h
2.BSD-2-Clause      10/23/2019        12/18/2019        lib/eal/windows/include/getopt.h
3.ISC AND
  BSD-2-Clause      10/23/2019        12/18/2019        lib/eal/windows/getopt.c
4. MIT              10/19/2022        10/18/2022        drivers/net/gve/base/*
---------------------------------------------------------------------------------------------------
