#include "flow_common.h"
#include "flow_queue.h"
#include "flow_util.h"
#include "linux/rcupdate.h"
#include "net/ip_policy.h"
#include <ngfw_ipc.h>
#include <sys/resource.h>
#include <signal.h>
#include <ha.h>
#include "sysmon_msg.h"
#include <flow_acct.h>
#include <flow_qos.h>
#include "ngfw_oem.h"
#include "av_debug.h"
#include "ngfw_version.h"
#include "ip_capture.h"
#include "ngfw_platform.h"
#include "cpuusage.h"

#include "dp_running_mode.h"

#ifdef ENABLE_CRYPTODEV
#include "net/xfrm/xfrm_cryptodev.h"
extern int cryptodev_enabled;
#endif


#ifdef USING_DPDK_LIB
#include "../dpdk/dpdk_common.h"
#endif


extern FLOW_SHARED rwlock_t flow_config_rwlock;
extern FLOW_SHARED unsigned int dp_flow_pid[];
extern FLOW_SHARED unsigned int dp_watchdog_mask;

#ifdef FLOW_BURNIN
extern FLOW_SHARED int dp_burnin;
extern void dp_burnin_test(void);
#endif

FLOW_SHARED int dp_rfc2544_throughput_limit_mbps;
FLOW_SHARED int dp_rfc2544_throughput_limit_drop_ratio = 2;

FLOW_FAST void flow_process_one_packet(struct sk_buff *skb)
{
	pak_proc_f proc_func;

	proc_func = skb->proc_func;
	while (likely(proc_func)) {
		skb = proc_func(skb);
		if (likely(skb)) {
			proc_func = skb->proc_func;
		} else {
			/* if skb is NULL, the packet has been freed */
			break;
		}
	}
}

FLOW_SHARED_FAST struct sk_buff *last_rx_pkt[MAX_CORES];

#ifdef USING_DPDK_LIB
#ifndef MAX
# define MAX(a, b) (((a) < (b)) ? (b) : (a))
#endif
static inline int flow_dpdk_rxbulk_dqueue(void)
{
	int skb_nums = 0;
	int skb_index = 0;
	struct dpdk_soft_queue *rx_queue = NULL;
	struct sk_buff *bulk_skb[DPDK_MAX_PKT_BURST];

	rx_queue = &dpdk_conf.core_rcv_queue[core_id];

	if(unlikely(rx_queue->queue_ring == NULL)){
		dp_error("Core %d rx_queue:%s is NULL.\n",core_id, rx_queue->sq_name);
		return 0;
	}

	if(rte_ring_empty(rx_queue->queue_ring))
		return 0;

	skb_nums =rte_ring_dequeue_burst(rx_queue->queue_ring, (void *)bulk_skb, DPDK_MAX_PKT_BURST, NULL);
	for(skb_index = 0; skb_index < skb_nums; skb_index++)
		flow_process_one_packet(bulk_skb[skb_index]);
	return skb_nums;

}

extern void dpdk_process_one_mbuf(struct rte_mbuf *mbuf);
static inline int flow_dpdk_dispatch_dqueue(void)
{
	int mbuf_nums = 0;
	int mbuf_index = 0;
	unsigned nb_rx = 0;
	int nb_rx_bulk;
	int loops = 0;
	unsigned int node = 0;
	struct dpdk_soft_queue *rx_queue = NULL;
	struct rte_mbuf *bulk_mbuf[DPDK_MAX_PKT_BURST<<1];
	struct dpdk_dispatch_queue *disp_queue;

	disp_queue = dpdk_conf.core_disp_queue + core_id;
	if(!dp_dispatch_limit || disp_queue->rx_queues_num == 0)
		return 0;

	do {
		node = disp_queue->rx_queues_maps[disp_queue->rx_queues_pos];
		rx_queue = &disp_queue->rx_queues[node];
		nb_rx = rte_ring_count(rx_queue->queue_ring);
		if(nb_rx) {
			nb_rx_bulk = rte_ring_dequeue_burst(rx_queue->queue_ring, (void *)(bulk_mbuf + mbuf_nums), DPDK_MAX_PKT_BURST, NULL);
			mbuf_nums += nb_rx_bulk;
		}

		if(++disp_queue->rx_queues_pos >= disp_queue->rx_queues_num)
			disp_queue->rx_queues_pos = 0;

		if(mbuf_nums >= DPDK_MAX_PKT_BURST)
			break;
	} while(++loops < disp_queue->rx_queues_num);

	for(mbuf_index = 0; mbuf_index < mbuf_nums; mbuf_index++)
		dpdk_process_one_mbuf(bulk_mbuf[mbuf_index]);
	return mbuf_nums;
}

static inline int flow_dpdk_txbulk_dqueue(int pindex)
{
	int ret = 0;
	int mbuf_nums = 0;
	int mfree_index = 0;
	struct dpdk_soft_queue *tx_queue = NULL;
	struct dpdk_port_params *pparams = &dpdk_conf.port_params[pindex];
	struct rte_mbuf *bulk_mbuf[DPDK_MAX_PKT_BURST<<1];

#ifdef PLATFORM_LOONGSON
	if(pparams->p2cindex != core_id)
		return 0;

	tx_queue = &pparams->soft_tx_queue[core_id];
	if(unlikely(tx_queue->queue_ring == NULL)){
		/* see function dpdk_ports_init for more info about how to map soft-tx-queue */
		if (pparams->soft_tq_maps[core_id] == core_id)
			dp_error("Port %d on core %d tx_queue:%s is NULL.\n", pindex, core_id, tx_queue->sq_name);

		return 0;
	}

	if(rte_ring_empty(tx_queue->queue_ring))
		return 0;

	mbuf_nums =rte_ring_dequeue_burst(tx_queue->queue_ring, (void *)bulk_mbuf, DPDK_MAX_PKT_BURST, NULL);
#else
	unsigned nb_rx = 0;
	int nb_rx_bulk;
	int loops = 0;
	unsigned int ncore_id=0;
	struct dpdk_dispatch_conf *disp_conf;

	disp_conf = pparams->dispatch_conf + core_id;
	if(disp_conf->tx_get_core_num == 0)
		return 0;

	do {
		ncore_id = disp_conf->tx_get_from_core[disp_conf->tx_get_core_pos];
		tx_queue = &dpdk_conf.core_disp_queue[ncore_id].tx_queues[pindex];
		nb_rx = rte_ring_count(tx_queue->queue_ring);
		if(nb_rx) {
			nb_rx_bulk = rte_ring_dequeue_burst(tx_queue->queue_ring, (void *)(bulk_mbuf + mbuf_nums), DPDK_MAX_PKT_BURST, NULL);
			mbuf_nums += nb_rx_bulk;
		}

		if(++disp_conf->tx_get_core_pos >= disp_conf->tx_get_core_num)
			disp_conf->tx_get_core_pos = 0;

		if(mbuf_nums >= DPDK_MAX_PKT_BURST)
			break;
	} while(++loops < disp_conf->tx_get_core_num);
#endif

	ret = rte_eth_tx_burst(pindex, pparams->hw_tq_maps[core_id], bulk_mbuf, mbuf_nums);
	for(mfree_index = ret; mfree_index < mbuf_nums; mfree_index++){
		dp_debug(dp_debug_drop, "packet dropped as port:%d on core:%d tx burst err(ret:%d)\n", pindex, core_id, ret);
		rte_pktmbuf_free(bulk_mbuf[mfree_index]);
	}

	return mbuf_nums;

}

static inline int flow_queue_kni(int kni_fq_num)
{
	struct flow_queue *fq  = &global_flow_queue[kni_fq_num];

	if (unlikely(fq->rx_fn == NULL)){
		return 0;
	}
	else{
		return fq->rx_fn(kni_fq_num, fq->max_poll_size);
	}
}
static int FLOW_FAST flow_queue_loop(void)
{
	// int i;
	int tnum = 0,packet_num = 0;
	// struct flow_queue *fq;
	// struct dpdk_port_params *pparams = NULL;
	uint32_t lcore_id = rte_lcore_id();

	/* ?????????*/
	if (CORE_IS_RUNTIMER(core_id)) {
		if (dp_update_timer() > 0)
			return 1;
	}

	/*
	*    core_rcv_queue???????????????????for???????
	*    ????????????????????????????????????????
	*/

	tnum = flow_dpdk_rxbulk_dqueue();
	packet_num = MAX(packet_num, tnum);

	tnum = flow_dpdk_dispatch_dqueue();
	packet_num = MAX(packet_num, tnum);

#if 0
	for (i = 0; i < dpdk_conf.phyports; i++) {
		pparams = dpdk_conf.port_params + i;
		fq = &global_flow_queue[i];
		if (unlikely(fq->rx_fn == NULL))
			break;
		if (likely(pparams->hw_rq_maps[core_id] < fq->max_por_queue)){
			tnum = fq->rx_fn(i, fq->max_poll_size);		/* dpdk_work_rcv */
			packet_num = MAX(packet_num, tnum);
		}
		tnum = flow_dpdk_txbulk_dqueue(i);
		packet_num = MAX(packet_num, tnum);
	}
#else
	tnum = dpdk_work_rcv2(lcore_id);
	packet_num = MAX(packet_num, tnum);
#endif

	/* kni???cpu0????*/
	if(core_id == dpdk_conf.numa_fcoreid){
		tnum = flow_queue_kni(dpdk_conf.phyports);
		packet_num = MAX(packet_num, tnum);
	}
	return packet_num;
}
#else

static int FLOW_FAST flow_queue_loop(void)
{
	int i;
	int tnum = 0;
	int num;

	struct sk_buff *skb;
	struct flow_queue *fq;

	/* ?????????*/
	if (CORE_IS_RUNTIMER(core_id)) {
		if(dp_update_timer()> 0)
			return 1;
	}

	for (i = 0; i < FLOW_MAX_QUEUE_NUM; i++) {
		fq = &global_flow_queue[i];
		if (unlikely(fq->rx_fn == NULL))
			break;

		for (num = 0; num < fq->max_poll_size; num++) {
			skb = fq->rx_fn();
			if (unlikely(!skb))
				break;

			tnum++;
			last_rx_pkt[core_id] = skb;
			flow_process_one_packet(skb);
			last_rx_pkt[core_id] = NULL;
		}
	}

	return tnum;
}
#endif

FLOW_FAST void dp_config_wlock(void)
{
	ha_set_timeout(30);
#ifdef __aarch64__
	write_lock(&flow_config_rwlock);
#else
//#ifdef USING_DPDK_LIB
//	config_write_lock(&flow_config_rwlock);
//#else
	write_lock(&flow_config_rwlock);
//#endif
#endif
}

FLOW_FAST void dp_config_wunlock(void)
{
#ifdef __aarch64__
	write_unlock(&flow_config_rwlock);
#else
//#ifdef USING_DPDK_LIB
//	config_write_unlock(&flow_config_rwlock);
//#else
	write_unlock(&flow_config_rwlock);
//#endif
#endif
	ha_set_timeout(0);
}

FLOW_FAST void dp_config_rlock(void)
{

#if defined(PLATFORM_OCTEON) || defined(USING_DPDK_LIB)
	read_lock(&flow_config_rwlock);
#endif
}

FLOW_FAST void dp_config_unrlock(void)
{
#if defined(PLATFORM_OCTEON) || defined(USING_DPDK_LIB)
	read_unlock(&flow_config_rwlock);
#endif
}

extern int pcap_save(char *filename, void *buf, int len);
static void save_last_pkt(struct sk_buff *skb)
{
	char title[64];
	char obuf[4096];
	char corefile[128];

	int pkt_len;

	if (!skb)
		return;

	if (!skb_mac_header(skb))
		skb_reset_mac_header(skb);

	pkt_len = skb->tail - (void *)skb_mac_header(skb);

	if (access(NGFW_DISK_PATH_ROOT"db", F_OK) == 0)
		snprintf(corefile, sizeof(corefile), "%s/pkt-%d.pcap", DISK_COLLECT_LOG_PATH, core_id);
	else
		snprintf(corefile, sizeof(corefile), "%s/pkt-%d.pcap", COLLECT_LOG_PATH, core_id);
	pcap_save(corefile, skb_mac_header(skb), pkt_len);

	snprintf(title, sizeof(title), "WARNING: data plane in core%d is dying, last pkt len %d:\n",
		core_id, pkt_len);
	print_hex_dump_bytes_to_buffer(title,
		skb_mac_header(skb),
		pkt_len < 1520 ? pkt_len : 1520 , obuf, sizeof(obuf));
	printf_console("%s", obuf);
	return;
}

static void signal_crash(int signo)
{
	signal(SIGBUS, SIG_DFL);
	signal(SIGSEGV, SIG_DFL);
	signal(SIGABRT, SIG_DFL);

	save_last_pkt(last_rx_pkt[core_id]);
}

void flow_local_init(void)
{
	dp_flow_pid[get_core_id()] = get_process_id();
	dp_cpuusage_core_init();
	dp_timer_local_init();
	dp_log_local_init();
	skb_local_init();

#ifdef PLATFORM_OCTEON
	ipc_open(APP_DPLANE, getpid());
#endif

	signal(SIGBUS, signal_crash);
	signal(SIGSEGV, signal_crash);
	signal(SIGABRT, signal_crash);
}


FLOW_SHARED unsigned long flow_running_coremask = ~0UL;
FLOW_SHARED unsigned long flow_runtimer_coremask = 0UL;
FLOW_SHARED int flow_async_core = 0;

extern FLOW_SHARED int capture_need_stop;

extern FLOW_FAST int capture_save_pkt_to_file(void);

extern int flow_ifdev_led_gpio_act(void);
FLOW_LOCAL unsigned long led_gpio_act_jiffies;

FLOW_FAST int flow_async_process(void)
{
	int count = 0;

	if (flow_async_core == 0) { //fix bug WXFJ-474, core 0 is data dplane.
		dp_config_rlock();
		count += dp_ct_async_clean_callback();
		count += flow_acct_collect();
		dp_config_unrlock();
	} else {
		count += dp_ct_async_clean_callback();
		count += flow_acct_collect();
	}

	if (dp_qos_rematch_all == 1)
		count += dp_ct_async_qos_rematch_callback();

	count += dp_ct_async_clean_ffbit_callback();
	count += dp_log_flush();
	count += ip_inspect_async_stat();


	if (unlikely(capture_status || capture_need_stop)) {
	    count += capture_save_pkt_to_file();
	}

	if (time_after(jiffies, led_gpio_act_jiffies + (HZ / 100))) {
		flow_ifdev_led_gpio_act();				// 10ms
		led_gpio_act_jiffies = jiffies;
	}

	/* 轮询 cryptodev 设备处理完成的操作 */
#ifdef ENABLE_CRYPTODEV
	if (cryptodev_enabled) {
		count += xfrm_cryptodev_poll();
	}
#endif

	return count;
}

void flow_update_log_core()
{
	int i;

	if (core_num <= 2){
		flow_async_core = 0;
	} else {
		for (i = 0; i < MAX_CORES; i++) {
			if (CORE_IS_RUNNING(i)) {
				if (i == 0)
					continue;
				flow_async_core = i;
				break;
			}
		}
	}
}

static void flow_core_set_idle(void)
{
#ifndef USING_DPDK_LIB
	int cindex;

	//core0 is idle when core number > 4;
	//core0 and core1 are idle when core number > 16
	if (is_init_core()) {

		if (get_num_cores()>4)
			CORE_SET_IDLE(0);
		if (get_num_cores() > 16){
			//GY_UAC-ACG1000-P, when cpu is 6880 32 cores ,  only use 0-16 core.
			if(ngfw_get_feature(FEATURE_GY_ACG100_P) && (get_num_cores() > 16)){

				for(cindex = 16; cindex < get_num_cores(); cindex++)
					CORE_SET_IDLE(cindex);
				last_core_id = 15;
			}else
				CORE_SET_IDLE(1);
		}
		flow_update_log_core();
	}
#else
	if (is_init_core()) {
		flow_update_log_core();
	}
#endif

}

FLOW_SHARED struct dp_power_save_level dp_ps_level[MAX_PS_LEVEL] = {
	{DP_IDLE_LEVEL1_DEF, DP_SLEEP_LEVEL1_DEF},
	{DP_IDLE_LEVEL2_DEF, DP_SLEEP_LEVEL2_DEF},
	{0, DP_SLEEP_LEVEL3_DEF},
};

FLOW_SHARED int dp_running_mode = DEF_RUNNING_MODE;

void FLOW_FAST flow_main_loop(void)
{
	int packet_num;
	int zero_pkt_num = 0;
	int active;

	rcu_register_thread();

	flow_local_init();
	flow_core_set_idle();
	dp_core_set_runtimer();

	//wait config loaded
	while (!vtysh_init_ok) {
		sleep(1);
		rcu_quiescent_state();
		DP_WATCHDOG();
	}
printf_console("core %d enter main loop\n", core_id);
	while (1) {

		while (CORE_IS_RUNNING(core_id)) {

#ifdef FLOW_BURNIN
			if (unlikely(dp_burnin))
				dp_burnin_test();
#endif

			dp_cpuusage_core_start();

			dp_config_rlock();

			packet_num = active = flow_queue_loop();
			dp_cpuusage_core_count(active);

			if (unlikely(dp_qos_on)) {
				active = qos_dequeue_packets();
				dp_cpuusage_core_count(active);
			}
			dp_config_unrlock();

			if (unlikely(flow_async_core == core_id)) {
				active = flow_async_process();
				dp_cpuusage_core_count(active);
			}

			DP_WATCHDOG();

			rcu_quiescent_state();
			if (unlikely(dp_running_mode == POWER_SAVE)) {
				if (packet_num == 0) {
					zero_pkt_num++;
					if (zero_pkt_num < dp_ps_level[0].idle_times)
						usleep(dp_ps_level[0].sleep_time);
					else if (zero_pkt_num < dp_ps_level[1].idle_times)
						usleep(dp_ps_level[1].sleep_time);
					else
						usleep(dp_ps_level[2].sleep_time);
				} else {
					zero_pkt_num = 0;
				}
			}
		}

		//Idle cores reaches here
		rcu_quiescent_state();
		rcu_thread_offline();
		flow_update_log_core();
		while (!CORE_IS_RUNNING(core_id)) {
			DP_WATCHDOG();
			sleep(1);
		}
		rcu_thread_online();
		flow_update_log_core();
	}
}

