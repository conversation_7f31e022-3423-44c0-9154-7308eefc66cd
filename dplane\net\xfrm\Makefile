#
#  Top of build tree
#
TOP_DIR = ../../../..

#
#  Top of current module
#
MODULE_TOP_DIR = ../..

#
#  macros definition
#
include $(TOP_DIR)/arch.mk

#
#  include flag
#
INC_DIR += -I$(MODULE_TOP_DIR)/include
INC_DIR += -I$(MODULE_TOP_DIR)/include/app_engine
ifeq ($(ARCH), OCTEON)
CFLAGS_LOCAL += $(CPPFLAGS_GLOBAL)
INC_DIR += -I$(MODULE_TOP_DIR)/include/octeon_openssl
CFLAGS_LOCAL += -DOCTEON_OPENSSL
INC_DIR += -I$(OCTEON_ROOT)/target/include
endif

#
#  link flag (only for bin)
#
CFLAGS_LOCAL += -Wno-sign-compare -Wno-unused-function
ifneq ($(ARCH), OCTEON)
CFLAGS_LOCAL += -Wno-unused-but-set-variable
endif
CFLAGS_LOCAL += -Wno-implicit-function-declaration -Wno-missing-field-initializers -Wno-override-init -Wno-implicit-fallthrough \
		-Wno-error

# 添加 DPDK cryptodev 相关标志
CFLAGS_LOCAL += -DENABLE_CRYPTODEV

# 添加 cryptodev 相关源文件
SRCS_LOCAL += xfrm_cryptodev.c
SRCS_LOCAL += xfrm_cryptodev_session.c
SRCS_LOCAL += xfrm_cryptodev_ops.c
SRCS_LOCAL += xfrm_cryptodev_poll.c
SRCS_LOCAL += xfrm_cryptodev_config.c
SRCS_LOCAL += xfrm_cryptodev_cli.c
SRCS_LOCAL += xfrm_cryptodev_debug.c
SRCS_LOCAL += xfrm_cryptodev_utils.c
SRCS_LOCAL += xfrm_cryptodev_zerocopy.c
SRCS_LOCAL += xfrm_cryptodev_batch.c
SRCS_LOCAL += xfrm_cryptodev_async.c
SRCS_LOCAL += xfrm_algo_map.c

LDFLAGS_PATH += -lrte_cryptodev

DEP_LIBS_LIST +=

#
#  module properties
#
BUILD_TYPE = obj
BUILD_NAME = xfrm

#
#  subdirectories to build. SUB_MOD are subdirectory with objs to link
#
SUB_MOD =
#
#  You can add other libraries/executable in directories to build
#
SUB_DIR = $(SUB_MOD)

######## Include common rules
include $(TOP_DIR)/rules.mk

