DPDK_24 {
	global:

	rte_eth_add_first_rx_callback;
	rte_eth_add_rx_callback;
	rte_eth_add_tx_callback;
	rte_eth_allmulticast_disable;
	rte_eth_allmulticast_enable;
	rte_eth_allmulticast_get;
	rte_eth_call_rx_callbacks;
	rte_eth_call_tx_callbacks;
	rte_eth_dev_adjust_nb_rx_tx_desc;
	rte_eth_dev_callback_register;
	rte_eth_dev_callback_unregister;
	rte_eth_dev_close;
	rte_eth_dev_configure;
	rte_eth_dev_count_avail;
	rte_eth_dev_count_total;
	rte_eth_dev_default_mac_addr_set;
	rte_eth_dev_flow_ctrl_get;
	rte_eth_dev_flow_ctrl_set;
	rte_eth_dev_fw_version_get;
	rte_eth_dev_get_dcb_info;
	rte_eth_dev_get_eeprom;
	rte_eth_dev_get_eeprom_length;
	rte_eth_dev_get_mtu;
	rte_eth_dev_get_name_by_port;
	rte_eth_dev_get_port_by_name;
	rte_eth_dev_get_reg_info;
	rte_eth_dev_get_sec_ctx;
	rte_eth_dev_get_supported_ptypes;
	rte_eth_dev_get_vlan_offload;
	rte_eth_dev_info_get;
	rte_eth_dev_is_removed;
	rte_eth_dev_is_valid_port;
	rte_eth_dev_logtype;
	rte_eth_dev_mac_addr_add;
	rte_eth_dev_mac_addr_remove;
	rte_eth_dev_owner_delete;
	rte_eth_dev_owner_get;
	rte_eth_dev_owner_new;
	rte_eth_dev_owner_set;
	rte_eth_dev_owner_unset;
	rte_eth_dev_pool_ops_supported;
	rte_eth_dev_priority_flow_ctrl_set;
	rte_eth_dev_reset;
	rte_eth_dev_rss_hash_conf_get;
	rte_eth_dev_rss_hash_update;
	rte_eth_dev_rss_reta_query;
	rte_eth_dev_rss_reta_update;
	rte_eth_dev_rx_intr_ctl;
	rte_eth_dev_rx_intr_ctl_q;
	rte_eth_dev_rx_intr_ctl_q_get_fd;
	rte_eth_dev_rx_intr_disable;
	rte_eth_dev_rx_intr_enable;
	rte_eth_dev_rx_offload_name;
	rte_eth_dev_rx_queue_start;
	rte_eth_dev_rx_queue_stop;
	rte_eth_dev_set_eeprom;
	rte_eth_dev_set_link_down;
	rte_eth_dev_set_link_up;
	rte_eth_dev_set_mc_addr_list;
	rte_eth_dev_set_mtu;
	rte_eth_dev_set_ptypes;
	rte_eth_dev_set_rx_queue_stats_mapping;
	rte_eth_dev_set_tx_queue_stats_mapping;
	rte_eth_dev_set_vlan_ether_type;
	rte_eth_dev_set_vlan_offload;
	rte_eth_dev_set_vlan_pvid;
	rte_eth_dev_set_vlan_strip_on_queue;
	rte_eth_dev_socket_id;
	rte_eth_dev_start;
	rte_eth_dev_stop;
	rte_eth_dev_tx_offload_name;
	rte_eth_dev_tx_queue_start;
	rte_eth_dev_tx_queue_stop;
	rte_eth_dev_uc_all_hash_table_set;
	rte_eth_dev_uc_hash_table_set;
	rte_eth_dev_udp_tunnel_port_add;
	rte_eth_dev_udp_tunnel_port_delete;
	rte_eth_dev_vlan_filter;
	rte_eth_find_next;
	rte_eth_find_next_of;
	rte_eth_find_next_owned_by;
	rte_eth_find_next_sibling;
	rte_eth_fp_ops;
	rte_eth_iterator_cleanup;
	rte_eth_iterator_init;
	rte_eth_iterator_next;
	rte_eth_led_off;
	rte_eth_led_on;
	rte_eth_link_get;
	rte_eth_link_get_nowait;
	rte_eth_macaddr_get;
	rte_eth_promiscuous_disable;
	rte_eth_promiscuous_enable;
	rte_eth_promiscuous_get;
	rte_eth_remove_rx_callback;
	rte_eth_remove_tx_callback;
	rte_eth_rx_burst_mode_get;
	rte_eth_rx_metadata_negotiate;
	rte_eth_rx_queue_info_get;
	rte_eth_rx_queue_setup;
	rte_eth_set_queue_rate_limit;
	rte_eth_speed_bitflag;
	rte_eth_stats_get;
	rte_eth_stats_reset;
	rte_eth_timesync_adjust_time;
	rte_eth_timesync_disable;
	rte_eth_timesync_enable;
	rte_eth_timesync_read_rx_timestamp;
	rte_eth_timesync_read_time;
	rte_eth_timesync_read_tx_timestamp;
	rte_eth_timesync_write_time;
	rte_eth_tx_buffer_count_callback;
	rte_eth_tx_buffer_drop_callback;
	rte_eth_tx_buffer_init;
	rte_eth_tx_buffer_set_err_callback;
	rte_eth_tx_burst_mode_get;
	rte_eth_tx_done_cleanup;
	rte_eth_tx_queue_info_get;
	rte_eth_tx_queue_setup;
	rte_eth_xstats_get;
	rte_eth_xstats_get_by_id;
	rte_eth_xstats_get_id_by_name;
	rte_eth_xstats_get_names;
	rte_eth_xstats_get_names_by_id;
	rte_eth_xstats_reset;
	rte_flow_copy;
	rte_flow_create;
	rte_flow_destroy;
	rte_flow_error_set;
	rte_flow_flush;
	rte_flow_isolate;
	rte_flow_pick_transfer_proxy;
	rte_flow_query;
	rte_flow_validate;
	rte_tm_capabilities_get;
	rte_tm_get_number_of_leaf_nodes;
	rte_tm_hierarchy_commit;
	rte_tm_level_capabilities_get;
	rte_tm_mark_ip_dscp;
	rte_tm_mark_ip_ecn;
	rte_tm_mark_vlan_dei;
	rte_tm_node_add;
	rte_tm_node_capabilities_get;
	rte_tm_node_cman_update;
	rte_tm_node_delete;
	rte_tm_node_parent_update;
	rte_tm_node_resume;
	rte_tm_node_shaper_update;
	rte_tm_node_shared_shaper_update;
	rte_tm_node_shared_wred_context_update;
	rte_tm_node_stats_read;
	rte_tm_node_stats_update;
	rte_tm_node_suspend;
	rte_tm_node_type_get;
	rte_tm_node_wfq_weight_mode_update;
	rte_tm_node_wred_context_update;
	rte_tm_shaper_profile_add;
	rte_tm_shaper_profile_delete;
	rte_tm_shared_shaper_add_update;
	rte_tm_shared_shaper_delete;
	rte_tm_shared_wred_context_add_update;
	rte_tm_shared_wred_context_delete;
	rte_tm_wred_profile_add;
	rte_tm_wred_profile_delete;

	local: *;
};

EXPERIMENTAL {
	global:

	# added in 17.11
	rte_mtr_capabilities_get;
	rte_mtr_create;
	rte_mtr_destroy;
	rte_mtr_meter_disable;
	rte_mtr_meter_dscp_table_update;
	rte_mtr_meter_enable;
	rte_mtr_meter_profile_add;
	rte_mtr_meter_profile_delete;
	rte_mtr_meter_profile_update;
	rte_mtr_stats_read;
	rte_mtr_stats_update;

	# added in 18.05
	rte_eth_dev_get_module_eeprom;
	rte_eth_dev_get_module_info;

	# added in 18.11
	rte_flow_conv;

	# added in 19.08
	rte_eth_read_clock;

	# added in 19.11
	rte_eth_dev_hairpin_capability_get;
	rte_eth_rx_hairpin_queue_setup;
	rte_eth_tx_hairpin_queue_setup;
	rte_flow_dynf_metadata_offs;
	rte_flow_dynf_metadata_mask;
	rte_flow_dynf_metadata_register;

	# added in 20.02
	rte_flow_dev_dump;

	# added in 20.05
	__rte_ethdev_trace_rx_burst;
	__rte_ethdev_trace_tx_burst;
	rte_flow_get_aged_flows;

	# added in 20.11
	rte_eth_hairpin_bind;
	rte_eth_hairpin_get_peer_ports;
	rte_eth_hairpin_unbind;
	rte_eth_link_speed_to_str;
	rte_eth_link_to_str;
	rte_eth_fec_get_capability;
	rte_eth_fec_get;
	rte_eth_fec_set;
	rte_flow_tunnel_decap_set;
	rte_flow_tunnel_match;
	rte_flow_get_restore_info;
	rte_flow_tunnel_action_decap_release;
	rte_flow_tunnel_item_release;

	# added in 21.02
	rte_eth_get_monitor_addr;

	# added in 21.05
	rte_eth_representor_info_get;
	rte_flow_action_handle_create;
	rte_flow_action_handle_destroy;
	rte_flow_action_handle_update;
	rte_flow_action_handle_query;
	rte_mtr_meter_policy_add;
	rte_mtr_meter_policy_delete;
	rte_mtr_meter_policy_update;
	rte_mtr_meter_policy_validate;

	# added in 21.11
	rte_eth_dev_capability_name;
	rte_eth_dev_conf_get;
	rte_eth_macaddrs_get;
	rte_flow_flex_item_create;
	rte_flow_flex_item_release;

	# added in 22.03
	rte_eth_dev_priority_flow_ctrl_queue_configure;
	rte_eth_dev_priority_flow_ctrl_queue_info_get;
	rte_eth_dev_priv_dump;
	rte_eth_ip_reassembly_capability_get;
	rte_eth_ip_reassembly_conf_get;
	rte_eth_ip_reassembly_conf_set;
	rte_flow_info_get;
	rte_flow_configure;
	rte_flow_pattern_template_create;
	rte_flow_pattern_template_destroy;
	rte_flow_actions_template_create;
	rte_flow_actions_template_destroy;
	rte_flow_template_table_create;
	rte_flow_template_table_destroy;
	rte_flow_async_create;
	rte_flow_async_destroy;
	rte_flow_push;
	rte_flow_pull;
	rte_flow_async_action_handle_create;
	rte_flow_async_action_handle_destroy;
	rte_flow_async_action_handle_update;

	# added in 22.07
	rte_eth_rx_avail_thresh_query;
	rte_eth_rx_avail_thresh_set;
	rte_mtr_color_in_protocol_get;
	rte_mtr_color_in_protocol_priority_get;
	rte_mtr_color_in_protocol_set;
	rte_mtr_meter_vlan_table_update;

	# added in 22.11
	rte_eth_buffer_split_get_supported_hdr_ptypes;
	rte_eth_cman_config_get;
	rte_eth_cman_config_init;
	rte_eth_cman_config_set;
	rte_eth_cman_info_get;
	rte_eth_rx_descriptor_dump;
	rte_eth_tx_descriptor_dump;
	rte_flow_async_action_handle_query;
	rte_flow_get_q_aged_flows;
	rte_mtr_meter_policy_get;
	rte_mtr_meter_profile_get;

	# added in 23.03
	rte_eth_dev_count_aggr_ports;
	rte_eth_dev_map_aggr_tx_affinity;
	rte_flow_action_handle_query_update;
	rte_flow_async_action_handle_query_update;
	rte_flow_async_create_by_index;

	# added in 23.07
	rte_eth_rx_queue_is_valid;
	rte_eth_tx_queue_is_valid;
	rte_flow_action_list_handle_create;
	rte_flow_action_list_handle_destroy;
	rte_flow_action_list_handle_query_update;
	rte_flow_actions_update;
	rte_flow_async_action_list_handle_create;
	rte_flow_async_action_list_handle_destroy;
	rte_flow_async_action_list_handle_query_update;
	rte_flow_async_actions_update;
	rte_flow_restore_info_dynflag;

	# added in 23.11
	rte_eth_dev_rss_algo_name;
	rte_eth_recycle_rx_queue_info_get;
	rte_flow_group_set_miss_actions;
	rte_flow_calc_table_hash;
};

INTERNAL {
	global:

	rte_eth_dev_allocate;
	rte_eth_dev_allocated;
	rte_eth_dev_attach_secondary;
	rte_eth_dev_callback_process;
	rte_eth_dev_create;
	rte_eth_dev_destroy;
	rte_eth_dev_get_by_name;
	rte_eth_dev_is_rx_hairpin_queue;
	rte_eth_dev_is_tx_hairpin_queue;
	rte_eth_dev_probing_finish;
	rte_eth_dev_release_port;
	rte_eth_dev_internal_reset;
	rte_eth_devargs_parse;
	rte_eth_devices;
	rte_eth_dma_zone_free;
	rte_eth_dma_zone_reserve;
	rte_eth_hairpin_queue_peer_bind;
	rte_eth_hairpin_queue_peer_unbind;
	rte_eth_hairpin_queue_peer_update;
	rte_eth_ip_reassembly_dynfield_register;
	rte_eth_pkt_burst_dummy;
	rte_eth_representor_id_get;
	rte_eth_switch_domain_alloc;
	rte_eth_switch_domain_free;
};
