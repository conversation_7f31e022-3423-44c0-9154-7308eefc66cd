DPDK_24 {
	global:

	__rte_graph_mcore_dispatch_sched_node_enqueue;
	__rte_graph_mcore_dispatch_sched_wq_process;
	__rte_node_register;
	__rte_node_stream_alloc;
	__rte_node_stream_alloc_size;
	rte_graph_clone;
	rte_graph_cluster_stats_create;
	rte_graph_cluster_stats_destroy;
	rte_graph_cluster_stats_get;
	rte_graph_cluster_stats_reset;
	rte_graph_create;
	rte_graph_destroy;
	rte_graph_dump;
	rte_graph_export;
	rte_graph_from_name;
	rte_graph_id_to_name;
	rte_graph_list_dump;
	rte_graph_lookup;
	rte_graph_max_count;
	rte_graph_model_is_valid;
	rte_graph_model_mcore_dispatch_core_bind;
	rte_graph_model_mcore_dispatch_core_unbind;
	rte_graph_model_mcore_dispatch_node_lcore_affinity_set;
	rte_graph_node_get;
	rte_graph_node_get_by_name;
	rte_graph_obj_dump;
	rte_graph_walk;
	rte_graph_worker_model_get;
	rte_graph_worker_model_no_check_get;
	rte_graph_worker_model_set;
	rte_node_clone;
	rte_node_dump;
	rte_node_edge_count;
	rte_node_edge_get;
	rte_node_edge_shrink;
	rte_node_edge_update;
	rte_node_enqueue;
	rte_node_enqueue_next;
	rte_node_enqueue_x1;
	rte_node_enqueue_x2;
	rte_node_enqueue_x4;
	rte_node_from_name;
	rte_node_id_to_name;
	rte_node_list_dump;
	rte_node_max_count;
	rte_node_next_stream_get;
	rte_node_next_stream_move;
	rte_node_next_stream_put;

	local: *;
};
