
#
#  Top of build tree
#
TOP_DIR = ../../..

#
#  Top of current module
#
MODULE_TOP_DIR = ..

#
#  macros definition
#
include $(TOP_DIR)/arch.mk

#
#  include flag
#

INC_DIR += -I$(MODULE_TOP_DIR)/include

INC_DIR += -I$(MODULE_TOP_DIR)/

#
#  link flag (only for bin)
#

CFLAGS_LOCAL += -Wno-sign-compare -Wno-missing-field-initializers -Wno-implicit-fallthrough -Wno-implicit-function-declaration \
		-Wno-format-truncation -Wno-error

# 添加 DPDK cryptodev 相关标志
CFLAGS_LOCAL += -DENABLE_CRYPTODEV

LDFLAGS_PATH += -lpthread -lrte_cryptodev

DEP_LIBS_LIST +=

# 添加源文件
SRCS_LOCAL += dpdk_cryptodev_unified.c

#
#  module properties
#
BUILD_TYPE = obj
BUILD_NAME = dpdk

#
#  subdirectories to build. SUB_MOD are subdirectory with objs to link
#
SUB_MOD =
#
#  You can add other libraries/executable in directories to build
#
#SUB_DIR = $(SUB_MOD) $(DPDK_SDK)

######## Include common rules
include $(TOP_DIR)/rules.mk

