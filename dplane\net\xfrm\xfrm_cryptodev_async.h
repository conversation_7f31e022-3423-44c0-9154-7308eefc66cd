/*
 * xfrm_cryptodev_async.h
 *
 * Description: 优化的异步cryptodev操作头文件
 */

#ifndef _XFRM_CRYPTODEV_ASYNC_H
#define _XFRM_CRYPTODEV_ASYNC_H

#include <linux/workqueue.h>
#include <rte_cryptodev.h>

/* 异步处理常量 */
#define CRYPTODEV_COMPLETION_QUEUE_SIZE   256   /* 完成队列大小 */
#define CRYPTODEV_POLL_BURST_SIZE         32    /* 轮询批量大小 */
#define CRYPTODEV_MAX_PROCESS_PER_WORK    64    /* 每次工作处理的最大操作数 */

/* 异步处理统计信息 */
struct xfrm_crypto_async_stats {
    uint32_t total_pending;     /* 总待处理操作数 */
    uint32_t active_queues;     /* 活跃队列数 */
    int workqueue_active;       /* 工作队列是否活跃 */
};

/* 函数声明 */

/**
 * 初始化异步处理
 * 
 * @return 0成功，负数表示错误
 */
int xfrm_cryptodev_async_init(void);

/**
 * 清理异步处理
 */
void xfrm_cryptodev_async_uninit(void);

/**
 * 工作队列处理函数
 * 
 * @param work 工作结构指针
 */
void xfrm_crypto_completion_worker(struct work_struct *work);

/**
 * 轮询cryptodev设备并处理完成的操作
 * 
 * @param dev_id 设备ID
 * @param qp_id 队列对ID
 * @return 处理的操作数量
 */
int xfrm_cryptodev_poll_completions(uint8_t dev_id, uint16_t qp_id);

/**
 * 获取异步处理统计信息
 * 
 * @param stats 输出统计信息
 */
void xfrm_cryptodev_async_stats(struct xfrm_crypto_async_stats *stats);

#endif /* _XFRM_CRYPTODEV_ASYNC_H */
