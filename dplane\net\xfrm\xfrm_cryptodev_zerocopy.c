/*
 * xfrm_cryptodev_zerocopy.c
 *
 * Description: 零拷贝的skb-mbuf转换实现
 * 利用dplane原有的skb->work指向mbuf的设计，实现真正的零拷贝
 */

#include <linux/skbuff.h>
#include <rte_mbuf.h>
#include <rte_cryptodev.h>

#include "xfrm_cryptodev.h"

/* 从skb获取关联的mbuf（零拷贝） */
static inline struct rte_mbuf *skb_get_mbuf(struct sk_buff *skb)
{
    /* dplane架构中，skb->work直接指向原始的rte_mbuf */
    return (struct rte_mbuf *)skb->work;
}

/* 检查skb是否有有效的mbuf关联 */
static inline int skb_has_valid_mbuf(struct sk_buff *skb)
{
    struct rte_mbuf *m = skb_get_mbuf(skb);
    
    if (!m) {
        return 0;
    }
    
    /* 基本的mbuf有效性检查 */
    if (m->buf_addr == NULL || m->data_len == 0) {
        return 0;
    }
    
    /* 检查数据指针是否一致 */
    if (skb->data != (unsigned char *)rte_pktmbuf_mtod(m, void *)) {
        CRYPTO_WARNING("SKB data pointer inconsistent with mbuf");
        return 0;
    }
    
    return 1;
}

/* 为cryptodev操作准备mbuf（零拷贝） */
struct rte_mbuf *xfrm_prepare_mbuf_for_crypto(struct sk_buff *skb)
{
    struct rte_mbuf *m;
    
    /* 获取关联的mbuf */
    m = skb_get_mbuf(skb);
    if (!m) {
        CRYPTO_ERROR("SKB has no associated mbuf");
        return NULL;
    }
    
    /* 验证mbuf有效性 */
    if (!skb_has_valid_mbuf(skb)) {
        CRYPTO_ERROR("SKB has invalid mbuf association");
        return NULL;
    }
    
    /* 确保mbuf数据长度与skb一致 */
    if (m->data_len != skb->len) {
        CRYPTO_DEBUG("Updating mbuf data length from %u to %u", m->data_len, skb->len);
        m->data_len = skb->len;
        m->pkt_len = skb->len;
    }
    
    /* 增加mbuf引用计数，因为cryptodev会异步处理 */
    rte_mbuf_refcnt_update(m, 1);
    
    CRYPTO_DEBUG("Prepared mbuf for crypto: data_len=%u, pkt_len=%u", m->data_len, m->pkt_len);
    
    return m;
}

/* 从cryptodev操作完成后恢复skb（零拷贝） */
int xfrm_restore_skb_from_crypto(struct sk_buff *skb, struct rte_mbuf *m)
{
    /* 验证这是同一个mbuf */
    if (skb_get_mbuf(skb) != m) {
        CRYPTO_ERROR("Mbuf mismatch during restore");
        return -EINVAL;
    }
    
    /* 更新skb长度以反映cryptodev处理后的变化 */
    if (skb->len != m->data_len) {
        CRYPTO_DEBUG("Updating SKB length from %u to %u", skb->len, m->data_len);
        
        /* 使用skb_trim或skb_put来调整长度 */
        if (m->data_len < skb->len) {
            skb_trim(skb, m->data_len);
        } else if (m->data_len > skb->len) {
            /* 检查是否有足够的空间 */
            if (skb_tailroom(skb) >= (m->data_len - skb->len)) {
                skb_put(skb, m->data_len - skb->len);
            } else {
                CRYPTO_ERROR("Insufficient tailroom in SKB for expanded data");
                return -ENOSPC;
            }
        }
    }
    
    /* 减少mbuf引用计数 */
    rte_mbuf_refcnt_update(m, -1);
    
    CRYPTO_DEBUG("Restored SKB from crypto: len=%u", skb->len);
    
    return 0;
}

/* 为异步操作保存上下文信息 */
struct xfrm_crypto_async_context {
    struct xfrm_state *x;
    struct sk_buff *skb;
    int direction;  /* XFRM_POLICY_IN 或 XFRM_POLICY_OUT */
    uint32_t seq;
    void *orig_data;
    uint32_t orig_len;
};

/* 在mbuf中保存异步上下文（使用mbuf的私有区域） */
int xfrm_save_async_context(struct rte_mbuf *m, struct xfrm_state *x, 
                           struct sk_buff *skb, int direction)
{
    struct xfrm_crypto_async_context *ctx;
    
    /* 检查mbuf是否有足够的私有空间 */
    if (rte_pktmbuf_priv_size(m->pool) < sizeof(*ctx)) {
        CRYPTO_ERROR("Mbuf private area too small for async context");
        return -ENOSPC;
    }
    
    /* 获取私有区域指针 */
    ctx = (struct xfrm_crypto_async_context *)rte_pktmbuf_mtod_offset(m, void *, m->data_len);
    
    /* 保存上下文信息 */
    ctx->x = x;
    ctx->skb = skb;
    ctx->direction = direction;
    ctx->seq = (direction == XFRM_POLICY_OUT) ? 
               XFRM_SKB_CB(skb)->seq.output.low : 
               XFRM_SKB_CB(skb)->seq.input.low;
    ctx->orig_data = skb->data;
    ctx->orig_len = skb->len;
    
    /* 增加xfrm_state引用计数 */
    xfrm_state_hold(x);
    
    CRYPTO_DEBUG("Saved async context: direction=%d, seq=%u", direction, ctx->seq);
    
    return 0;
}

/* 从mbuf中恢复异步上下文 */
struct xfrm_crypto_async_context *xfrm_get_async_context(struct rte_mbuf *m)
{
    struct xfrm_crypto_async_context *ctx;
    
    /* 获取私有区域指针 */
    ctx = (struct xfrm_crypto_async_context *)rte_pktmbuf_mtod_offset(m, void *, m->data_len);
    
    return ctx;
}

/* 清理异步上下文 */
void xfrm_cleanup_async_context(struct rte_mbuf *m)
{
    struct xfrm_crypto_async_context *ctx = xfrm_get_async_context(m);
    
    if (ctx && ctx->x) {
        /* 释放xfrm_state引用 */
        xfrm_state_put(ctx->x);
        ctx->x = NULL;
    }
}

/* 检查数据包是否适合使用cryptodev */
int xfrm_packet_suitable_for_cryptodev(struct sk_buff *skb)
{
    /* 检查是否有关联的mbuf */
    if (!skb_has_valid_mbuf(skb)) {
        CRYPTO_DEBUG("Packet not suitable: no valid mbuf");
        return 0;
    }
    
    /* 检查数据包大小 */
    if (skb->len < sizeof(struct ip_esp_hdr)) {
        CRYPTO_DEBUG("Packet too small for ESP: %u bytes", skb->len);
        return 0;
    }
    
    /* 检查数据包是否过大 */
    if (skb->len > CRYPTODEV_MAX_PACKET_SIZE) {
        CRYPTO_DEBUG("Packet too large for cryptodev: %u bytes", skb->len);
        return 0;
    }
    
    /* 检查mbuf是否支持就地操作 */
    struct rte_mbuf *m = skb_get_mbuf(skb);
    if (rte_pktmbuf_headroom(m) < CRYPTODEV_MIN_HEADROOM ||
        rte_pktmbuf_tailroom(m) < CRYPTODEV_MIN_TAILROOM) {
        CRYPTO_DEBUG("Insufficient mbuf headroom/tailroom");
        return 0;
    }
    
    return 1;
}

/* 获取ESP头指针（基于mbuf） */
struct ip_esp_hdr *xfrm_get_esp_header(struct rte_mbuf *m, int ip_version)
{
    uint8_t *data = rte_pktmbuf_mtod(m, uint8_t *);
    struct ip_esp_hdr *esph;
    
    if (ip_version == 4) {
        struct iphdr *iph = (struct iphdr *)data;
        esph = (struct ip_esp_hdr *)(data + (iph->ihl << 2));
    } else {
        struct ipv6hdr *ip6h = (struct ipv6hdr *)data;
        esph = (struct ip_esp_hdr *)(data + sizeof(struct ipv6hdr));
    }
    
    return esph;
}

/* 计算ESP载荷偏移和长度 */
int xfrm_calculate_esp_offsets(struct rte_mbuf *m, struct xfrm_state *x,
                              uint32_t *data_offset, uint32_t *data_length,
                              uint32_t *auth_offset, uint32_t *auth_length)
{
    struct ip_esp_hdr *esph;
    uint32_t esp_hdr_len = sizeof(struct ip_esp_hdr);
    uint32_t iv_len = x->iv_len;
    uint32_t icv_len = x->auth_trunc_len;
    
    /* 获取ESP头 */
    esph = xfrm_get_esp_header(m, x->props.family);
    if (!esph) {
        return -EINVAL;
    }
    
    /* 计算加密数据的偏移和长度 */
    *data_offset = esp_hdr_len + iv_len;
    *data_length = m->data_len - esp_hdr_len - iv_len - icv_len;
    
    /* 计算认证数据的偏移和长度 */
    *auth_offset = 0;
    *auth_length = m->data_len - icv_len;
    
    CRYPTO_DEBUG("ESP offsets: data_offset=%u, data_length=%u, auth_offset=%u, auth_length=%u",
                *data_offset, *data_length, *auth_offset, *auth_length);
    
    return 0;
}
