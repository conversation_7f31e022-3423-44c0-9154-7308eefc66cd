/*
 * xfrm_cryptodev_utils.c
 *
 * Description: Utility functions for cryptodev
 */

#include <linux/skbuff.h>
#include <linux/ip.h>
#include <linux/types.h>
#include <linux/spinlock.h>
#include <linux/netdevice.h>
#include <linux/slab.h>
#include <linux/module.h>

#include <rte_config.h>
#include <rte_mbuf.h>
#include <rte_mempool.h>
#include <rte_crypto.h>
#include <rte_cryptodev.h>
#include <rte_ether.h>

#include "xfrm_cryptodev.h"

/* 定义 phys_addr_t 类型和 RTE_BAD_PHYS_ADDR 常量 */
typedef uint64_t phys_addr_t;

/* 实现 rte_pktmbuf_mtophys 函数 */
phys_addr_t rte_pktmbuf_mtophys(const struct rte_mbuf *m)
{
    /* 使用DPDK 23的标准API获取IOVA地址 */
    return rte_mbuf_data_iova(m);
}

/* 实现 rte_pktmbuf_mtophys_offset 函数 */
phys_addr_t rte_pktmbuf_mtophys_offset(const struct rte_mbuf *m, size_t offset)
{
    /* 检查偏移量是否在第一个段内 */
    if (offset < m->data_len)
        return rte_pktmbuf_mtophys(m) + offset;

    /* 如果偏移量超出第一个段，需要遍历段链表 */
    size_t off = offset;
    const struct rte_mbuf *seg = m;

    off -= seg->data_len;
    while ((seg = seg->next) != NULL) {
        if (off < seg->data_len)
            return rte_pktmbuf_mtophys(seg) + off;
        off -= seg->data_len;
    }

    /* 偏移量超出了 mbuf 的范围 */
    return (phys_addr_t)0;
}
